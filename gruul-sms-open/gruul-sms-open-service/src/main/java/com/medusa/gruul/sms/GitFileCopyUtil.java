package com.medusa.gruul.sms;

import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Git文件复制工具类
 * 根据Git提交编号查出该次提交涉及的文件并复制到指定目录
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public class GitFileCopyUtil {

    private static final String DEFAULT_COPY_DIR = "copy";
    private static String projectPath = null;


    public static void main(String[] args) {

        String targetDir = "提交/2025-07-24";

        String commitHash = "562e35ad";
        String projectDir = "D:/hh/code/boot/7.14/group-mall-admin-source";
        String endTargetDir ="/web";


       /*  commitHash = "b47e7b66";
         projectDir = "D:/hh/code/boot/7.14/gruul";
         endTargetDir ="/gruul";*/

        targetDir = targetDir+endTargetDir;
        // 1. 正向复制
        System.out.println("步骤1: 正向复制文件");
        int copiedCount = GitFileCopyUtil.copyCommitFiles(commitHash, targetDir,projectDir);
        System.out.println("正向复制: " + copiedCount + " 个文件");

        if (copiedCount > 0) {
            // 2. 逆复制
            System.out.println("步骤2: 逆复制文件");
            int reverseCopiedCount = GitFileCopyUtil.reverseCopyFiles(targetDir, false,projectDir);
            System.out.println("逆复制: " + reverseCopiedCount + " 个文件");

            if (reverseCopiedCount > 0) {
                System.out.println("✅ 基本逆复制功能测试通过");
            } else {
                System.out.println("❌ 逆复制失败");
            }
        } else {
            System.out.println("❌ 正向复制失败，跳过逆复制测试");
        }

        System.out.println();
        // 测试用例1: 使用默认copy目录和当前项目路径
//       copyCommitFiles("19ee5215","提交/取消服务项目适用类型/gruul");

        // 测试用例2: 设置全局项目路径
        // setProjectPath("D:/hh/code/boot/7.8/gruul");
        // copyCommitFiles("492c79f6","copy-test");

        // 测试用例3: 直接指定项目路径（推荐方式）
//        copyCommitFiles("d2abfd63", "提交/抽奖记录增加订单详情/web", "D:/hh/code/boot/7.14/group-mall-admin-source");

        // 测试用例4: 使用便利方法
        // copyCommitFilesFromProject("492c79f6", "D:/hh/code/boot/7.8/gruul");
    }

    /**
     * 设置项目路径
     *
     * @param path 项目根目录路径
     */
    public static void setProjectPath(String path) {
        projectPath = path;
    }

    /**
     * 获取项目路径，如果未设置则使用当前工作目录
     *
     * @return 项目路径
     */
    public static String getProjectPath() {
        return projectPath != null ? projectPath : System.getProperty("user.dir");
    }
    /**
     * 根据Git提交哈希复制文件到指定目录
     *
     * @param commitHash Git提交哈希值
     * @param targetDir 目标目录，如果为null则使用默认的copy目录
     * @return 复制成功的文件数量
     */
    public static int copyCommitFiles(String commitHash, String targetDir) {
        return copyCommitFiles(commitHash, targetDir, null);
    }

    /**
     * 根据Git提交哈希复制文件到指定目录
     *
     * @param commitHash Git提交哈希值
     * @param targetDir 目标目录，如果为null则使用默认的copy目录
     * @param projectDir 项目根目录，如果为null则使用当前工作目录
     * @return 复制成功的文件数量
     */
    public static int copyCommitFiles(String commitHash, String targetDir, String projectDir) {
        if (isBlank(commitHash)) {
            System.err.println("提交哈希值不能为空");
            return 0;
        }

        if (isBlank(targetDir)) {
            targetDir = DEFAULT_COPY_DIR;
        }

        // 确定项目目录
        String actualProjectDir = projectDir != null ? projectDir : getProjectPath();
        System.out.println("使用项目目录: " + actualProjectDir);

        try {
            // 获取提交涉及的文件列表
            List<String> commitFiles = getCommitFiles(commitHash, actualProjectDir);
            if (commitFiles.isEmpty()) {
                System.out.println("提交 " + commitHash + " 没有找到任何文件");
                return 0;
            }

            System.out.println("找到 " + commitFiles.size() + " 个文件需要复制");

            // 创建目标目录
            Path targetPath = Paths.get(targetDir);
            if (!Files.exists(targetPath)) {
                Files.createDirectories(targetPath);
                System.out.println("创建目标目录: " + targetPath.toAbsolutePath());
            }

            int successCount = 0;
            for (String filePath : commitFiles) {
                if (copyFileWithStructure(filePath, targetDir, actualProjectDir)) {
                    successCount++;
                }
            }

            System.out.println("成功复制 " + successCount + "/" + commitFiles.size() + " 个文件到目录: " + targetPath.toAbsolutePath());
            return successCount;

        } catch (Exception e) {
            System.err.println("复制提交文件时发生错误: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }
    
    /**
     * 获取指定提交涉及的文件列表
     *
     * @param commitHash Git提交哈希值
     * @return 文件路径列表
     */
    private static List<String> getCommitFiles(String commitHash) {
        return getCommitFiles(commitHash, getProjectPath());
    }

    /**
     * 获取指定提交涉及的文件列表
     *
     * @param commitHash Git提交哈希值
     * @param projectDir 项目根目录
     * @return 文件路径列表
     */
    private static List<String> getCommitFiles(String commitHash, String projectDir) {
        List<String> files = new ArrayList<>();
        
        try {
            // 使用git show --name-only命令获取提交涉及的文件
            ProcessBuilder pb = new ProcessBuilder("git", "show", "--name-only", "--pretty=format:", commitHash);
            pb.directory(new File(projectDir));
            
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (isNotBlank(line)) {
                        // 处理Git输出中可能包含的引号
                        if (line.startsWith("\"") && line.endsWith("\"")) {
                            line = line.substring(1, line.length() - 1);
                        }
                        // 统一路径分隔符为系统默认分隔符，并处理重复的分隔符
                        line = line.replace("/", File.separator).replace("\\", File.separator);
                        // 处理重复的分隔符
                        while (line.contains(File.separator + File.separator)) {
                            line = line.replace(File.separator + File.separator, File.separator);
                        }
                        files.add(line);
                    }
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 读取错误信息
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), "UTF-8"))) {
                    StringBuilder errorMsg = new StringBuilder();
                    String errorLine;
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorMsg.append(errorLine).append("\n");
                    }
                    System.err.println("Git命令执行失败，退出码: " + exitCode + ", 错误信息: " + errorMsg.toString());
                }
            }

        } catch (Exception e) {
            System.err.println("获取Git提交文件列表时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        return files;
    }

    /**
     * 复制文件并保持目录结构
     *
     * @param sourceFilePath 源文件相对路径
     * @param targetDir 目标根目录
     * @return 是否复制成功
     */
    private static boolean copyFileWithStructure(String sourceFilePath, String targetDir) {
        return copyFileWithStructure(sourceFilePath, targetDir, getProjectPath());
    }

    /**
     * 复制文件并保持目录结构
     *
     * @param sourceFilePath 源文件相对路径
     * @param targetDir 目标根目录
     * @param projectDir 项目根目录
     * @return 是否复制成功
     */
    private static boolean copyFileWithStructure(String sourceFilePath, String targetDir, String projectDir) {
        try {
            // 构建基于项目目录的源文件路径
            Path sourcePath = Paths.get(projectDir, sourceFilePath);
            Path targetPath = Paths.get(targetDir, sourceFilePath);

            // 检查源文件是否存在
            if (!Files.exists(sourcePath)) {
                System.out.println("源文件不存在: " + sourcePath.toAbsolutePath());
                return false;
            }

            // 创建目标文件的父目录
            Path targetParent = targetPath.getParent();
            if (targetParent != null && !Files.exists(targetParent)) {
                Files.createDirectories(targetParent);
                System.out.println("创建目录: " + targetParent.toAbsolutePath());
            }

            // 复制文件
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("复制文件: " + sourcePath.toAbsolutePath() + " -> " + targetPath.toAbsolutePath());

            return true;

        } catch (Exception e) {
            System.err.println("复制文件 " + sourceFilePath + " 时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据Git提交哈希复制文件到默认copy目录
     *
     * @param commitHash Git提交哈希值
     * @return 复制成功的文件数量
     */
    public static int copyCommitFiles(String commitHash) {
        return copyCommitFiles(commitHash, null);
    }

    /**
     * 根据Git提交哈希复制文件到默认copy目录，指定项目路径
     *
     * @param commitHash Git提交哈希值
     * @param projectDir 项目根目录
     * @return 复制成功的文件数量
     */
    public static int copyCommitFilesFromProject(String commitHash, String projectDir) {
        return copyCommitFiles(commitHash, null, projectDir);
    }

    /**
     * 清空指定目录
     *
     * @param targetDir 目标目录
     * @return 是否清空成功
     */
    public static boolean clearDirectory(String targetDir) {
        try {
            Path targetPath = Paths.get(targetDir);
            if (Files.exists(targetPath)) {
                deleteDirectory(targetPath.toFile());
                System.out.println("清空目录: " + targetPath.toAbsolutePath());
                return true;
            }
            return true;
        } catch (Exception e) {
            System.err.println("清空目录 " + targetDir + " 时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取Git提交信息
     *
     * @param commitHash Git提交哈希值
     * @return 提交信息
     */
    public static String getCommitInfo(String commitHash) {
        return getCommitInfo(commitHash, getProjectPath());
    }

    /**
     * 获取Git提交信息
     *
     * @param commitHash Git提交哈希值
     * @param projectDir 项目根目录
     * @return 提交信息
     */
    public static String getCommitInfo(String commitHash, String projectDir) {
        if (isBlank(commitHash)) {
            return "";
        }

        StringBuilder info = new StringBuilder();

        try {
            ProcessBuilder pb = new ProcessBuilder("git", "show", "--pretty=format:%H%n%an%n%ad%n%s", "--no-patch", commitHash);
            pb.directory(new File(projectDir));

            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    info.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                System.err.println("获取Git提交信息失败，退出码: " + exitCode);
                return "";
            }

        } catch (Exception e) {
            System.err.println("获取Git提交信息时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return info.toString().trim();
    }

    /**
     * 逆复制：将指定目录的文件复制回项目根目录（覆盖原文件）
     *
     * @param sourceDir 源目录路径
     * @return 复制成功的文件数量
     */
    public static int reverseCopyFiles(String sourceDir) {
        return reverseCopyFiles(sourceDir, true);
    }

    /**
     * 逆复制：将指定目录的文件复制回项目根目录
     *
     * @param sourceDir 源目录路径
     * @param createBackup 是否创建备份
     * @return 复制成功的文件数量
     */
    public static int reverseCopyFiles(String sourceDir, boolean createBackup) {
        return reverseCopyFiles(sourceDir, createBackup, null);
    }

    /**
     * 逆复制：将指定目录的文件复制回指定项目目录
     *
     * @param sourceDir 源目录路径
     * @param createBackup 是否创建备份
     * @param projectDir 目标项目目录，如果为null则使用当前项目路径
     * @return 复制成功的文件数量
     */
    public static int reverseCopyFiles(String sourceDir, boolean createBackup, String projectDir) {
        if (isBlank(sourceDir)) {
            System.err.println("源目录不能为空");
            return 0;
        }

        Path sourcePath = Paths.get(sourceDir);
        if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath)) {
            System.err.println("源目录不存在或不是目录: " + sourcePath.toAbsolutePath());
            return 0;
        }

        // 确定实际项目目录
        String actualProjectDir = projectDir != null ? projectDir : getProjectPath();
        System.out.println("使用项目目录: " + actualProjectDir);

        try {
            // 获取源目录中的所有文件
            List<String> allFiles = getAllFilesInDirectory(sourceDir);
            if (allFiles.isEmpty()) {
                System.out.println("源目录 " + sourceDir + " 中没有找到任何文件");
                return 0;
            }

            System.out.println("找到 " + allFiles.size() + " 个文件需要逆复制");

            // 创建备份目录（如果需要）
            String backupDir = null;
            if (createBackup) {
                backupDir = createBackupDirectory();
                System.out.println("备份目录: " + backupDir);
            }

            int successCount = 0;
            for (String filePath : allFiles) {
                if (reverseCopyFileWithBackup(filePath, sourceDir, backupDir, actualProjectDir)) {
                    successCount++;
                }
            }

            System.out.println("成功逆复制 " + successCount + "/" + allFiles.size() + " 个文件");
            if (createBackup && successCount > 0) {
                System.out.println("原文件已备份到: " + backupDir);
            }

            return successCount;

        } catch (Exception e) {
            System.err.println("逆复制文件时发生错误: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 带确认机制的逆复制
     *
     * @param sourceDir 源目录路径
     * @param requireConfirm 是否需要用户确认
     * @return 复制成功的文件数量
     */
    public static int reverseCopyFilesWithConfirm(String sourceDir, boolean requireConfirm) {
        if (requireConfirm) {
            System.out.print("确认要将 " + sourceDir + " 中的文件逆复制到项目根目录吗？这将覆盖原有文件。(y/N): ");
            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
                String input = reader.readLine();
                if (!"y".equalsIgnoreCase(input) && !"yes".equalsIgnoreCase(input)) {
                    System.out.println("操作已取消");
                    return 0;
                }
            } catch (Exception e) {
                System.err.println("读取用户输入时发生错误: " + e.getMessage());
                return 0;
            }
        }

        return reverseCopyFiles(sourceDir, true, null);
    }

    /**
     * 批量逆复制多个目录
     *
     * @param sourceDirs 源目录数组
     * @param createBackup 是否创建备份
     * @return 总共复制成功的文件数量
     */
    public static int batchReverseCopy(String[] sourceDirs, boolean createBackup) {
        if (sourceDirs == null || sourceDirs.length == 0) {
            System.out.println("没有提供源目录");
            return 0;
        }

        System.out.println("=== 批量逆复制多个目录 ===");

        int totalCopied = 0;
        for (String sourceDir : sourceDirs) {
            System.out.println("处理目录: " + sourceDir);
            int copied = reverseCopyFiles(sourceDir, createBackup, null);
            totalCopied += copied;
            System.out.println("本次逆复制: " + copied + " 个文件");
            System.out.println();
        }

        System.out.println("批量逆复制完成，总共逆复制了 " + totalCopied + " 个文件");
        return totalCopied;
    }

    /**
     * 从备份恢复文件
     *
     * @param backupDir 备份目录
     * @return 恢复成功的文件数量
     */
    public static int restoreFromBackup(String backupDir) {
        if (isBlank(backupDir)) {
            System.err.println("备份目录不能为空");
            return 0;
        }

        Path backupPath = Paths.get(backupDir);
        if (!Files.exists(backupPath) || !Files.isDirectory(backupPath)) {
            System.err.println("备份目录不存在: " + backupPath.toAbsolutePath());
            return 0;
        }

        System.out.println("开始从备份恢复文件: " + backupDir);
        return reverseCopyFiles(backupDir, false, null); // 恢复时不再创建备份
    }

    /**
     * 检查字符串是否为空或空白
     */
    private static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空且不为空白
     */
    private static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 递归获取目录中的所有文件
     *
     * @param directory 目录路径
     * @return 文件路径列表（相对于项目根目录）
     */
    private static List<String> getAllFilesInDirectory(String directory) {
        List<String> files = new ArrayList<>();
        Path dirPath = Paths.get(directory);

        try {
            Files.walk(dirPath)
                .filter(Files::isRegularFile)
                .forEach(path -> {
                    // 获取相对于源目录的路径
                    String relativePath = getRelativePath(path.toString(), directory);
                    files.add(relativePath);
                });
        } catch (Exception e) {
            System.err.println("遍历目录时发生错误: " + e.getMessage());
        }

        return files;
    }

    /**
     * 获取相对路径
     *
     * @param fullPath 完整路径
     * @param baseDir 基础目录
     * @return 相对路径
     */
    private static String getRelativePath(String fullPath, String baseDir) {
        Path full = Paths.get(fullPath);
        Path base = Paths.get(baseDir);
        return base.relativize(full).toString();
    }

    /**
     * 创建备份目录
     *
     * @return 备份目录路径
     */
    private static String createBackupDirectory() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String backupDir = ".backup/backup_" + timestamp;

        try {
            Path backupPath = Paths.get(backupDir);
            Files.createDirectories(backupPath);
            return backupDir;
        } catch (Exception e) {
            System.err.println("创建备份目录失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 逆复制单个文件并备份原文件
     *
     * @param relativePath 相对路径
     * @param sourceDir 源目录
     * @param backupDir 备份目录
     * @param projectDir 目标项目目录
     * @return 是否复制成功
     */
    private static boolean reverseCopyFileWithBackup(String relativePath, String sourceDir, String backupDir, String projectDir) {
        try {
            Path sourcePath = Paths.get(sourceDir, relativePath);
            Path targetPath = Paths.get(projectDir, relativePath);

            // 检查源文件是否存在
            if (!Files.exists(sourcePath)) {
                System.out.println("源文件不存在: " + sourcePath.toAbsolutePath());
                return false;
            }

            // 备份原文件（如果存在且需要备份）
            if (backupDir != null && Files.exists(targetPath)) {
                Path backupPath = Paths.get(backupDir, relativePath);
                Path backupParent = backupPath.getParent();
                if (backupParent != null && !Files.exists(backupParent)) {
                    Files.createDirectories(backupParent);
                }
                Files.copy(targetPath, backupPath, StandardCopyOption.REPLACE_EXISTING);
                System.out.println("备份文件: " + targetPath.toAbsolutePath() + " -> " + backupPath.toAbsolutePath());
            }

            // 创建目标文件的父目录
            Path targetParent = targetPath.getParent();
            if (targetParent != null && !Files.exists(targetParent)) {
                Files.createDirectories(targetParent);
            }

            // 复制文件（覆盖）
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("逆复制文件: " + sourcePath.toAbsolutePath() + " -> " + targetPath.toAbsolutePath());

            return true;

        } catch (Exception e) {
            System.err.println("逆复制文件 " + relativePath + " 时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递归删除目录及其所有内容
     */
    private static void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
