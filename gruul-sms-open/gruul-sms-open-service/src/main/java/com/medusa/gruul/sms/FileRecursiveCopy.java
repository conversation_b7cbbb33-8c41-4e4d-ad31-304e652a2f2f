package com.medusa.gruul.sms;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 文件夹递归复制工具
 * 
 * 该工具可以递归遍历源文件夹中的所有文件和子文件夹，
 * 并将它们复制到目标文件夹中，保持相同的目录结构
 */
public class FileRecursiveCopy {

    /**
     * 主方法，程序入口
     * 
     * @param args 命令行参数，需要提供源文件夹路径和目标文件夹路径
     */
    public static void main(String[] args) {
//D:\hh\code\app-mall-Lz-2025-06-03\gruul
        String sourcePath = "D:\\hh\\code\\boot\\7.14\\gruul";
        String targetPath = "C:\\Users\\<USER>\\Desktop\\data2";

        File sourceDir = new File(sourcePath);
        File targetDir = new File(targetPath);

        // 检查源文件夹是否存在
        if (!sourceDir.exists()) {
            System.out.println("错误: 源文件夹不存在 - " + sourcePath);
            return;
        }

        // 检查源路径是否是一个文件夹
        if (!sourceDir.isDirectory()) {
            System.out.println("错误: 源路径不是一个文件夹 - " + sourcePath);
            return;
        }

        // 确保目标文件夹存在，如果不存在则创建
        if (!targetDir.exists()) {
            if (!targetDir.mkdirs()) {
                System.out.println("错误: 无法创建目标文件夹 - " + targetPath);
                return;
            }
        }

        try {
            // 开始递归复制
            copyDirectory(sourceDir, targetDir);
            System.out.println("文件复制完成！");
        } catch (IOException e) {
            System.out.println("复制过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 递归复制文件夹及其内容
     * 
     * @param sourceDir 源文件夹
     * @param targetDir 目标文件夹
     * @throws IOException 如果复制过程中发生IO错误
     */
    private static void copyDirectory(File sourceDir, File targetDir) throws IOException {
        // 获取源文件夹中的所有文件和子文件夹
        File[] files = sourceDir.listFiles();
        
        if (files == null) {
            System.out.println("警告: 无法列出文件夹内容 - " + sourceDir.getAbsolutePath());
            return;
        }
        
        // 遍历所有文件和子文件夹
        for (File file : files) {
            // 构建目标文件/文件夹路径
            File targetFile = new File(targetDir, file.getName());
            
            if (file.isDirectory()) {
                // 如果是文件夹，创建对应的目标文件夹并递归复制
                if (!targetFile.exists() && !targetFile.mkdirs()) {
                    throw new IOException("无法创建目录: " + targetFile.getAbsolutePath());
                }
                copyDirectory(file, targetFile);
            } else {
                // 如果是文件，直接复制
                copyFile(file, targetFile);
                System.out.println("已复制: " + file.getAbsolutePath() + " -> " + targetFile.getAbsolutePath());
            }
        }
    }

    /**
     * 复制单个文件
     * 
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @throws IOException 如果复制过程中发生IO错误
     */
    private static void copyFile(File sourceFile, File targetFile) throws IOException {
        Path sourcePath = Paths.get(sourceFile.getAbsolutePath());
        Path targetPath = Paths.get(targetFile.getAbsolutePath());
        
        // 使用Files.copy方法复制文件，如果目标文件已存在则替换
        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
    }
} 