<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.medusa.gruul.sms.mapper.TSmsTemplateEntityMapper">
    <resultMap id="BaseResultMap" type="com.medusa.gruul.sms.model.entity.TSmsTemplateEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
          <result column="user_id" property="userId" jdbcType="BIGINT"/>
          <result column="sms_provider_id" property="smsProviderId" jdbcType="BIGINT"/>
          <result column="sms_template_content" property="smsTemplateContent" jdbcType="VARCHAR"/>
          <result column="sms_template_code" property="smsTemplateCode" jdbcType="VARCHAR"/>
          <result column="sms_template_is_pass" property="smsTemplateIsPass" jdbcType="TINYINT"/>
          <result column="sms_remark" property="smsRemark" jdbcType="VARCHAR"/>
          <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
          <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
          <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
          <result column="template_type" property="templateType" jdbcType="INTEGER"/>
          <result column="sms_template_type" property="smsTemplateType" jdbcType="INTEGER"/>
          <result column="sms_template_name" property="smsTemplateName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
         id, user_id,  sms_provider_id,  sms_template_content,  sms_template_code,  sms_template_is_pass,  sms_remark,  is_deleted,  create_time,  update_time,  template_type,  sms_template_type,  sms_template_name, sms_template_name 
    </sql>

    <select id="searchByEntity" resultMap="BaseResultMap" parameterType="com.medusa.gruul.sms.model.entity.TSmsTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from  t_sms_template
        <where>
            <if test="id != null">
            id = #{id,jdbcType=BIGINT}
            </if>
               <if test="userId != null">
                   AND    user_id = #{userId,jdbcType=BIGINT}
               </if>
               <if test="smsProviderId != null">
                   AND    sms_provider_id = #{smsProviderId,jdbcType=BIGINT}
               </if>
               <if test="smsTemplateContent != null">
                   AND    sms_template_content = #{smsTemplateContent,jdbcType=VARCHAR}
               </if>
               <if test="smsTemplateCode != null">
                   AND    sms_template_code = #{smsTemplateCode,jdbcType=VARCHAR}
               </if>
               <if test="smsTemplateIsPass != null">
                   AND    sms_template_is_pass = #{smsTemplateIsPass,jdbcType=TINYINT}
               </if>
               <if test="smsRemark != null">
                   AND    sms_remark = #{smsRemark,jdbcType=VARCHAR}
               </if>
               <if test="isDeleted != null">
                   AND    is_deleted = #{isDeleted,jdbcType=TINYINT}
               </if>
               <if test="createTime != null">
                   AND    create_time = #{createTime,jdbcType=TIMESTAMP}
               </if>
               <if test="updateTime != null">
                   AND    update_time = #{updateTime,jdbcType=TIMESTAMP}
               </if>
               <if test="templateType != null">
                   AND    template_type = #{templateType,jdbcType=INTEGER}
               </if>
               <if test="smsTemplateType != null">
                   AND    sms_template_type = #{smsTemplateType,jdbcType=INTEGER}
               </if>
               <if test="smsTemplateName != null">
                   AND    sms_template_name = #{smsTemplateName,jdbcType=VARCHAR}
               </if>
            <if test="sysSmsTemplateCode != null">
                AND    sys_sms_template_code = #{sysSmsTemplateCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


</mapper>
