<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.medusa.gruul.sms.mapper.TSmsOrderEntityMapper">

    <resultMap id="BaseResultMap" type="com.medusa.gruul.sms.model.entity.TSmsOrderEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
          <result column="user_id" property="userId" jdbcType="BIGINT"/>
          <result column="sms_send_time" property="smsSendTime" jdbcType="BIGINT"/>
          <result column="sms_send_type" property="smsSendType" jdbcType="TINYINT"/>
          <result column="sms_send_content" property="smsSendContent" jdbcType="VARCHAR"/>
          <result column="sms_send_zone" property="smsSendZone" jdbcType="VARCHAR"/>
          <result column="sms_send_param" property="smsSendParam" jdbcType="VARCHAR"/>
          <result column="sms_send_mobiles" property="smsSendMobiles" jdbcType="VARCHAR"/>
          <result column="sms_send_count" property="smsSendCount" jdbcType="INTEGER"/>
          <result column="sms_send_success_count" property="smsSendSuccessCount" jdbcType="INTEGER"/>
          <result column="sms_send_fail_count" property="smsSendFailCount" jdbcType="INTEGER"/>
          <result column="sms_send_status" property="smsSendStatus" jdbcType="INTEGER"/>
          <result column="provider_id" property="providerId" jdbcType="BIGINT"/>
          <result column="sms_type" property="smsType" jdbcType="TINYINT"/>
          <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
          <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
          <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
          <result column="template_id" property="templateId" jdbcType="BIGINT"/>
          <result column="sign_id" property="signId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
         id, user_id,  sms_send_time,  sms_send_type,  sms_send_content,  sms_send_zone,  sms_send_param,  sms_send_mobiles,  sms_send_count,  sms_send_success_count,  sms_send_fail_count,  sms_send_status,  provider_id,  sms_type,  is_deleted,  create_time,  update_time,  template_id,  sign_id, sign_id
    </sql>

    <select id="doListWaitSendOrder" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where sms_send_status = #{smsSendStatus} AND   is_deleted =0  AND sms_send_time &lt; unix_timestamp(now())*1000;
    </select>


    <resultMap id="smsDoSendDto" type="com.medusa.gruul.sms.model.dto.SmsDoSendDto">
        <result column="smsOrderId" property="smsOrderId"/>
        <result column="providerAppId" property="providerAppId"/>
        <result column="providerAppSecret" property="providerAppSecret"/>
        <result column="templateCode" property="templateCode"/>
        <result column="smsTemplateContent" property="smsTemplateContent"/>
        <result column="smsSign" property="smsSign"/>
    </resultMap>


    <select id="getSendSmsCfg" resultMap="smsDoSendDto" parameterType="java.lang.Long">
     select tso.id                      as smsOrderId
     , tsp.sms_provider_appId      as providerAppId
     , tsp.sms_provider_app_secret as providerAppSecret
     , tst.sms_template_code       as templateCode
     , tst.sms_template_content       as smsTemplateContent
     , tss.sms_sign as  smsSign
     from t_sms_order tso
         left join t_sms_provider tsp on tso.provider_id = tsp.id
         left join t_sms_sign tss
                   on tss.id = tso.sign_id
         left join t_sms_template tst on tst.id = tso.template_id
      where tsp.is_deleted = 0
      and tss.is_deleted = 0
      and tst.is_deleted = 0
       and tso.id  = #{id}
      ;
    </select>

</mapper>
