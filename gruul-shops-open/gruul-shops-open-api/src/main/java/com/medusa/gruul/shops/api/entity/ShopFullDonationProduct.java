package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:42 2025/7/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_full_donation_product")
@ApiModel(value = "ShopFullDonationProduct对象", description = "满赠满减商品")
public class ShopFullDonationProduct extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;


    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;


    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_id")
    private Long mainId;

    @ApiModelProperty(value = "商品ID")
    @TableField(value = "product_id")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    @TableField(value = "sku_id")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    @TableField(value = "product_name")
    private String productName;




}
