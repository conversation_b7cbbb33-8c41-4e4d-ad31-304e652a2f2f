package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 营销活动抽奖明细表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_sale_prize_item")
@ApiModel(value = "ShopSalePrizeItem对象", description = "营销活动抽奖明细表")
public class ShopSalePrizeItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "主表ID")
    @TableField("main_id")
    private Long mainId;

    @ApiModelProperty(value = "奖项名称")
    @TableField("level_name")
    private String levelName;

    @ApiModelProperty(value = "排序")
    @TableField("seq_by")
    private Integer seqBy;

    @ApiModelProperty(value = "是否间隔奖项：0-否，1-是")
    @TableField("spacing_flag")
    private Integer spacingFlag;

    @ApiModelProperty(value = "是否奖品：0-否，1-是")
    @TableField("prize_flag")
    private Integer prizeFlag;

    @ApiModelProperty(value = "奖品发放方式：1-线上邮寄，2-线下核销")
    @TableField("verify_type")
    private Integer verifyType;

    @ApiModelProperty(value = "奖品有效期开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "奖品有效期结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品类型：1-商城商品，2-优惠券")
    @TableField("prize_type")
    private Integer prizeType;

    @ApiModelProperty(value = "商品ID")
    @TableField("product_id")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    @TableField("sku_id")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    @TableField("product_name")
    private String productName;

    @ApiModelProperty(value = "优惠券ID")
    @TableField("coupon_id")
    private Long couponId;

    @ApiModelProperty(value = "奖品名称")
    @TableField("prize_name")
    private String prizeName;

    @ApiModelProperty(value = "中奖概率")
    @TableField("winning_rate")
    private BigDecimal winningRate;

    @ApiModelProperty(value = "中奖参与人次")
    @TableField("winning_person_times")
    private Integer winningPersonTimes;

    @ApiModelProperty(value = "奖项图片")
    @TableField("level_pic")
    private String levelPic;

    @ApiModelProperty(value = "店铺ID")
    @TableField("shop_id")
    private String shopId;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    @ApiModelProperty(value = "修改人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;
} 