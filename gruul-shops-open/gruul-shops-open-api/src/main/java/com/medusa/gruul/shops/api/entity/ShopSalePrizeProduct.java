package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 营销活动抽奖商品表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_sale_prize_product")
@ApiModel(value = "ShopSalePrizeProduct对象", description = "营销活动抽奖商品表")
public class ShopSalePrizeProduct extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "主表ID")
    @TableField("main_id")
    private Long mainId;

    @ApiModelProperty(value = "商品ID")
    @TableField("product_id")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    @TableField("sku_id")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    @TableField("product_name")
    private String productName;

    @ApiModelProperty(value = "店铺ID")
    @TableField("shop_id")
    private String shopId;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    @ApiModelProperty(value = "修改人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;
} 
