package com.medusa.gruul.shops.api.constant;

/**
 * <AUTHOR>
 * @description 用户模块队列
 * @data: 2019/12/11
 */
public class QueueNameConstant {

    /**
     * 用户中心初始化队列
     */
    public static final String SHOPS_CENTER_QUEUE_INIT = "gruul.shops.center.queue.init";
    /**
     * 装修初始化队列
     */
    public static final String SHOPS_TEMPLATE_QUEUE_INIT = "gruul.shops.template.queue.init";

	/**
	 * 店铺引导页初始化队列
	 */
	public static  final  String  SHOP_GUIDE_PAGE_QUEUE_INIT = "gruul.shops.guide.page.init";
    /**
     * 订单支付成功后赠送抽奖机会
     */
    public static  final String ORDER_PAYED_PRIZE = "gruul.order.payed.prize";
    /**
     * 会员升级送抽奖机会
     */
    public static  final String SHOP_PRIZE_MEMBER_PRIZE = "gruul.shops.prize.member";

    /**
     * 中奖后发放奖品 优惠券
     */
    public static  final String SHOP_PRIZE_DISTRIBUTED_COUPON = "gruul.shop.prize.distributed.coupon";

    /**
     * 中奖后发放奖品 线上商品
     */
    public static  final String SHOP_PRIZE_DISTRIBUTED_PRODUCT = "gruul.shop.prize.distributed.product";


}
