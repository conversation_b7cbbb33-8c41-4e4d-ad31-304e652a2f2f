package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 佣金规则
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_commission_rule")
@ApiModel(value = "ShopCommissionRule对象", description = "佣金规则")
public class ShopCommissionRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 是否开启，0否1是
     */
    @ApiModelProperty(value = "是否开启，0否1是")
    @TableField("open_flag")
    private Integer openFlag;

    /**
     * 规则类型，100：固定金额，101：百分比
     */
    @ApiModelProperty(value = "规则类型，100：固定金额，101：百分比，102：以单个商品，103：根据会员等级")
    @TableField("rule_type")
    private Integer ruleType;

    /**
     * 上级金额或者百分比
     */
    @ApiModelProperty(value = "上级金额或者百分比")
    @TableField("parent_receive")
    private BigDecimal parentReceive;

    /**
     * 上上级金额或者百分比
     */
    @ApiModelProperty(value = "上上级金额或者百分比")
    @TableField("above_parent_receive")
    private BigDecimal aboveParentReceive;

    /**
     * 最小提现金额
     */
    @ApiModelProperty(value = "最小提现金额")
    @TableField("min_cash_amount")
    private BigDecimal minCashAmount;

    /**
     * 最大提现金额
     */
    @ApiModelProperty(value = "最大提现金额")
    @TableField("max_cash_amount")
    private BigDecimal maxCashAmount;

    /**
     * 提现手续费率%
     */
    @ApiModelProperty(value = "提现手续费率%")
    @TableField("cash_rate")
    private BigDecimal cashRate;

    /**
     * 每天提现次数
     */
    @ApiModelProperty(value = "每天提现次数")
    @TableField("cash_times")
    private Integer cashTimes;

    @ApiModelProperty(value = "最低消费")
    @TableField("min_pay_amount")
    private BigDecimal minPayAmount;

    /**
     * 佣金划转比例
     */
    @ApiModelProperty(value = "佣金划转比例")
    @TableField("commission_transfer_rate")
    private BigDecimal commissionTransferRate;
}
