package com.medusa.gruul.shops.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:10 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减规则vo")
public class ShopFullDonationRuleVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "满额类型:1-金额，2-件数，3-件数金额")
    private Integer fullType;

    @ApiModelProperty(value = "满件")
    private Integer pieceNum;

    @ApiModelProperty(value = "订单满额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "购买订单类型:1-首单，2-复购")
    private Integer buyType;

    @ApiModelProperty(value = "满减金额")
    private BigDecimal reductionAmount;

    @ApiModelProperty(value = "优惠券ID")
    private String couponIds;

    @ApiModelProperty(value = "赠送商品")
    private String skuIds;

    @ApiModelProperty(value = "满赠自身件数")
    private Integer selfNum;

    @ApiModelProperty(value = "规则展示标题")
    private String showTitle;
}
