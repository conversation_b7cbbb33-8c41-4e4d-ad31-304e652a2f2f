package com.medusa.gruul.shops.api.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;



/**
 * @Description: 枚举明细
 * @Author: qsx
 * @Date:   2022-03-09
 * @Version: V1.0
 */
@Data
@TableName("t_dict_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_dict_item对象", description="枚举明细")
public class DictItem extends BaseEntity {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
	private Long createUserId;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**字典id*/
    @ApiModelProperty(value = "字典id")
	private Long dictId;
	/**字典项文本*/
    @ApiModelProperty(value = "字典项文本")
	private String itemText;
	/**字典项值*/
    @ApiModelProperty(value = "字典项值")
	private String itemValue;
	/**描述*/
    @ApiModelProperty(value = "描述")
	private String description;
	/**排序*/
    @ApiModelProperty(value = "排序")
	private String sortOrder;
	/**状态（1启用 0不启用）*/
    @ApiModelProperty(value = "状态（1启用 0不启用）")
	private Integer status;
}
