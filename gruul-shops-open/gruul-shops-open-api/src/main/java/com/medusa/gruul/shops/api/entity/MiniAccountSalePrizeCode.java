package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_mini_account_sale_prize_code")
@ApiModel(value = "MiniAccountSalePrizeCode对象", description = "会员营销抽奖验证码记录表")
public class MiniAccountSalePrizeCode extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "抽奖活动ID")
    @TableField("prize_id")
    private Long prizeId;

    @ApiModelProperty(value = "t_mini_account_sale_prize表ID")
    @TableField("mini_account_prize_id")
    private Long miniAccountPrizeId;

    @ApiModelProperty(value = "验证码")
    @TableField("verify_code")
    private String verifyCode;

    @ApiModelProperty(value = "状态:100->未核销;101->已核销;200->已失效")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "核销时间")
    @TableField("verify_time")
    private LocalDateTime verifyTime;

    @ApiModelProperty(value = "核销人id")
    @TableField("verify_user_id")
    private Long verifyUserId;

    @ApiModelProperty(value = "核销人名称")
    @TableField("verify_nick_name")
    private String verifyNickName;



    @ApiModelProperty(value = "shopId")
    @TableField("shop_id")
    private String shopId;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    @ApiModelProperty(value = "修改人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;
}