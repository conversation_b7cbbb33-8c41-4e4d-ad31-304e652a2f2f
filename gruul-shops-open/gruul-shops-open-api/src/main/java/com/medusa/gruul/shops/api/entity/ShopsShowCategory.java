package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:29 2023/9/13
 */
@Data
@TableName("t_shops_show_category")
@ApiModel(value = "ShopsShowCategory对象", description = "商家展示分类")
public class ShopsShowCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 对应店铺id非shop_id
     */
    @ApiModelProperty(value = "对应店铺id非shop_id")
    @TableField("shops_partner_id")
    private Long shopsPartnerId;

    /**
     * 上级分类的编号：0表示一级分类
     */
    @ApiModelProperty(value = "上级分类的编号：0表示一级分类")
    @TableField("parent_id")
    private Long parentId;
    /**
     * 商家展示分类id
     */
    @ApiModelProperty(value = "商家展示分类id")
    @TableField("shops_category_id")
    private Long shopsCategoryId;
}
