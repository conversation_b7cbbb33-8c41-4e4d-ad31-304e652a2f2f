package com.medusa.gruul.shops.api.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.shops.api.entity.MiniAccountSalePrize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 奖项发放 Message
 */
@Data
@ApiModel(value = "新增或修改用户优惠券Message")
public class PrizeProductDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private CurUserDto curUserDto;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "抽奖活动ID")
    private Long prizeId;

    @ApiModelProperty(value = "下单赠予抽奖机会的订单")
    @TableField("order_id")
    private Long orderId;


    @ApiModelProperty(value = "抽奖明细ID")
    private Long prizeItemId;

    @ApiModelProperty(value = "是否奖品 0-否，1-是")
    private Integer prizeFlag;

    @ApiModelProperty(value = "奖品发放方式：1-线上邮寄，2-线下核销")
    private Integer verifyType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品类型：1-商城商品，2-优惠券")
    @TableField("prize_type")
    private Integer prizeType;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "优惠券ID")
    private Long couponId;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "中奖的奖品通过邮寄方式发出生成的订单")
    private Long postOrderId;

    @ApiModelProperty(value = "状态 0-未发放，1-已发放")
    private Integer status;


    @ApiModelProperty(value = "会员类型")
    private Long memberTypeId;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "shopId")
    private String shopId;

    @ApiModelProperty(value = "租户id")
    private String tenantId;


}
