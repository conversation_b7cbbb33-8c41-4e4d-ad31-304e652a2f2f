package com.medusa.gruul.shops.api.enums;

import com.medusa.gruul.shops.api.constant.ExchangeConstant;
import com.medusa.gruul.shops.api.constant.QueueNameConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum QueueEnum {

    /**
     * 用户中心初始化队列
     */
    QUEUE_SHOPS_CENTER_INIT(ExchangeConstant.SHOPS_EXCHANGE, QueueNameConstant.SHOPS_CENTER_QUEUE_INIT,
            QueueNameConstant.SHOPS_CENTER_QUEUE_INIT),
    /**
     * 店铺模板初始化队列
     */
    QUEUE_SHOPS_TEMPLATE_INIT(ExchangeConstant.SHOPS_EXCHANGE, QueueNameConstant.SHOPS_TEMPLATE_QUEUE_INIT,
            QueueNameConstant.SHOPS_TEMPLATE_QUEUE_INIT),

	QUEUE_SHOP_GUIDE_PAGE_INIT(ExchangeConstant.SHOPS_EXCHANGE, QueueNameConstant.SHOP_GUIDE_PAGE_QUEUE_INIT,
			QueueNameConstant.SHOP_GUIDE_PAGE_QUEUE_INIT),
    /**
     * 订单支付成功后 抽奖机会
     */
    QUEUE_ORDER_PAYED_PRIZE(ExchangeConstant.SHOPS_EXCHANGE, QueueNameConstant.ORDER_PAYED_PRIZE, QueueNameConstant.ORDER_PAYED_PRIZE),

    /**
     * 会员升级、注册 抽奖机会
     */
    QUEUE_SHOP_PRIZE_MEMBER_PRIZE(ExchangeConstant.SHOPS_EXCHANGE, QueueNameConstant.SHOP_PRIZE_MEMBER_PRIZE, QueueNameConstant.SHOP_PRIZE_MEMBER_PRIZE),

    /**
     * 抽奖成功后 分发奖品 优惠卷
     */
    QUEUE_SHOP_PRIZE_DISTRIBUTED_COUPON(ExchangeConstant.SHOPS_EXCHANGE,QueueNameConstant.SHOP_PRIZE_DISTRIBUTED_COUPON,QueueNameConstant.SHOP_PRIZE_DISTRIBUTED_COUPON),

    /**
     * 抽奖成功后 分发奖品 线上商品
     */
    QUEUE_SHOP_PRIZE_DISTRIBUTED_PRODUCT(ExchangeConstant.SHOPS_EXCHANGE,QueueNameConstant.SHOP_PRIZE_DISTRIBUTED_PRODUCT,QueueNameConstant.SHOP_PRIZE_DISTRIBUTED_PRODUCT),


    ;
    /**
     * 交换名称
     */
    private String exchange;
    /**
     * 队列名称
     */
    private String name;
    /**
     * 路由键
     */
    private String routeKey;

    QueueEnum(String exchange, String name, String routeKey) {
        this.exchange = exchange;
        this.name = name;
        this.routeKey = routeKey;
    }
}
