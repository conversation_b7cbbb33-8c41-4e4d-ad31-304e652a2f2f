package com.medusa.gruul.shops.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 奖项发放 Message t_mini_account_coupon
 * 参照 MiniAccountCouponDto
 * @Date: Created in 11:47 2024/8/27
 */
@Data
@ApiModel(value = "新增或修改用户优惠券Message")
public class PrizeCouponMessage {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "优惠券id")
    private Long couponId;

    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    private Integer status;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "shopId")
    private String shouId;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "redisKey")
    private String redisKey;
}
