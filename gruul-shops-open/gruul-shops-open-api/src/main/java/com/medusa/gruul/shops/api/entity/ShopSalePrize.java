package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 营销活动抽奖表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_sale_prize")
@ApiModel(value = "ShopSalePrize对象", description = "营销活动抽奖表")
public class ShopSalePrize extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "店铺ID")
    @TableField("shop_id")
    private String shopId;

    @ApiModelProperty(value = "活动名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "每人参与次数")
    @TableField("per_count")
    private Integer perCount;

    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "活动背景图URL")
    @TableField("back_image_url")
    private String backImageUrl;

    @ApiModelProperty(value = "参与会员等级，多个会员等级之间用英文逗号分隔")
    @TableField("member_level_ids")
    private String memberLevelIds;

    @ApiModelProperty(value = "指定参与商品：0-否，1-是")
    @TableField("product_flag")
    private Integer productFlag;

    @ApiModelProperty(value = "订单满额")
    @TableField("order_amount")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "直推会员数")
    @TableField("direct_member_qty")
    private Integer directMemberQty;

    @ApiModelProperty(value = "直推最低会员等级ID")
    @TableField("direct_low_member_level_id")
    private Long directLowMemberLevelId;

    @ApiModelProperty(value = "活动类型：1-大转盘，2-九宫格盲盒")
    @TableField("prize_type")
    private Integer prizeType;

    @ApiModelProperty(value = "状态：0->草稿；104->已生效;300->失效；400->停止")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "100->待审核；101->审核通过；200->审核不通过")
    @TableField("audit_status")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核人ID")
    @TableField("audit_platform_user_id")
    private Long auditPlatformUserId;

    @ApiModelProperty(value = "审核人")
    @TableField("audit_platform_user_name")
    private String auditPlatformUserName;

    @ApiModelProperty(value = "审核时间")
    @TableField("audit_time")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核意见")
    @TableField("audit_reason")
    private String auditReason;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "抽奖规则")
    @TableField("rule")
    private String rule;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @ApiModelProperty(value = "单据日期")
    @TableField("bill_date")
    private LocalDate billDate;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    @ApiModelProperty(value = "修改人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;
} 