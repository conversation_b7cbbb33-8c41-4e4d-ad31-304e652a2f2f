package com.medusa.gruul.shops.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 展示分类大类查询返回信息
 * @Date: Created in 11:09 2023/9/13
 */
@Data
@ApiModel(value = "ShopsCategoryVo对象", description = "展示分类大类查询返回信息")
public class ShopsCategoryVo {

    private Long id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "上机分类的编号：0表示一级分类")
    private Long parentId;

    @ApiModelProperty(value = "展示分类id(数据传输用)")
    private Long shopsCategoryId;


    @ApiModelProperty(value = "排序")
    private Integer sort;


    @ApiModelProperty(value = "分类级别：0->1级；1->2级")
    private Integer level;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "下级分类")
    private List<ShopsCategorySecondVo> shopsCategoryVos;
}
