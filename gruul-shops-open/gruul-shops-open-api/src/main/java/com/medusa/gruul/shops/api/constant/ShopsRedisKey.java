package com.medusa.gruul.shops.api.constant;

import com.medusa.gruul.shops.api.constant.RedisConstant;

/**
 * redis key 变量类
 * <AUTHOR>
 */
public class ShopsRedisKey {


    /**
     *  存储通惠证发行总量key
     * @Param id 通惠证id
     * @return
     */
    public static String getPassTicketTotalNumKey(String id){
        String codeKey = RedisConstant.PASS_TICKET_KEY.concat("totalNum").concat(":").concat(id);
        return codeKey;
    }

    /**
     * 用户显示优惠券弹窗
     * @param userId
     * @return
     */
    public static String getShowCouponAccountKey(String userId){
        String codeKey = RedisConstant.SHOW_COUPON_ACCOUNT.concat(userId);
        return codeKey;
    }
    /**
     * 抽奖机会
     * @param userShopId
     * @param prizeId
     * @return
     */
    public static String getPrizeKey(String userShopId,String prizeId){
        String codeKey = RedisConstant.PRIZE_KEY.concat(userShopId).concat(":").concat(prizeId);
        return codeKey;
    }

    /**
     * 抽奖-直推 prize+member+用户ID+活动ID ，单活动只能1 次
     * @param userShopId
     * @param prizeId
     * @return
     */
    public static String getPrizeDirectMemberKey(String userShopId,String prizeId){
        String codeKey = RedisConstant.PRIZE_DIRECT_MEMBER_KEY.concat(userShopId).concat(":").concat(prizeId);
        return codeKey;
    }
    /**
     * 抽奖-直推 prize+memQty+上级用户ID+活动ID，单活计算可抽奖次数
     * @param userShopId
     * @param prizeId
     * @return
     */
    public static String getPrizeDirectMemQtyKey(String userShopId,String prizeId){
        String codeKey = RedisConstant.PRIZE_DIRECT_MEM_QTY_KEY.concat(userShopId).concat(":").concat(prizeId);
        return codeKey;
    }

    /**
     * 抽奖机会 赠送的订单id
     * @param userShopId +orderId
     * @param prizeId
     * @return
     */
    public static String getPrizeOrderKey(String userShopId,String prizeId){
        String codeKey = RedisConstant.PRIZE_ORDER_KEY.concat(userShopId).concat(":").concat(prizeId);
        return codeKey;
    }
    public static String getPrizeMemberTypeKey(String userShopId,String prizeId){
        String codeKey = RedisConstant.PRIZE_MEMBER_TYPE_KEY.concat(userShopId).concat(":").concat(prizeId);
        return codeKey;
    }

    /**
     *  抽奖核销码
     * @param code
     * @return
     */
    public static String getPrizeCodeKey(String code){
        String codeKey = RedisConstant.PRIZE_CODE_KEY.concat(code);
        return codeKey;
    }
}
