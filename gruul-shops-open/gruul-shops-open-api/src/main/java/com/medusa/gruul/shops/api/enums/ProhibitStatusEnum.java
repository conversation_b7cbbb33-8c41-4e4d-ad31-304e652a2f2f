package com.medusa.gruul.shops.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:41 2023/8/17
 */
@Getter
public enum ProhibitStatusEnum {
    /**
     * 下架
     */
    NORMAL("0", "正常"),
    /**
     * 上架
     */
    DISABLE("1", "禁用");

    @EnumValue
    /**
     * 值
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;

    ProhibitStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
