package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR> by zq
 * @date created in 2019/11/15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("t_shops_partner")
@ApiModel(value = "shops partner 实体", description = "总店商铺表")
@Getter
@Setter
public class ShopsPartner extends BaseEntity {


    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;


    /**
     * 总店Id
     */
    @ApiModelProperty(value = "总店Id")
    @TableField("partner_id")
    private Long partnerId;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @TableField("phone")
    private String phone;




    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    @TableField("name")
    private String name;


    /**
     * 密码
     */
    @NotNull
    @ApiModelProperty(value = "密码")
    @TableField("pass")
    private String pass;

    /**
     * 邀请码
     */
    @ApiModelProperty(value = "邀请码")
    @TableField("invitation_code")
    private String invitationCode;


    /**
     * 地域
     */
    @ApiModelProperty(value = "地域")
    @TableField("region")
    private String region;


    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @TableField("area_code")
    private String areaCode;


    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    @TableField("province_code")
    private String provinceCode;


    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    @TableField("city_code")
    private String cityCode;


    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    @TableField("card_id_up")
    private String cardIdUp;


    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    @TableField("card_id_down")
    private String cardIdDown;


    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    @TableField("map_x")
    private Double mapX;


    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    @TableField("map_y")
    private Double mapY;


    /**
     * Modify模式  0加盟 1子公司
     */
    @ApiModelProperty(value = "合作模式  0加盟 1子公司 2运营中心")
    @TableField("partner_model")
    private String partnerModel;


    /**
     * 审批状态 0临时保存 审批状态 100 待审核 101 审核通过 200 驳回
     */
    @ApiModelProperty(value = "审批状态 0临时保存  100 待审核 101 审核通过 200 驳回")
    @TableField("approval_status")
    private String approvalStatus;


    /**
     * 禁用状态  0正常  1 禁用
     */
    @ApiModelProperty(value = "禁用状态  0正常  1 禁用")
    @TableField("prohibit_status")
    private String prohibitStatus;


    /**
     * 省name
     */
    @ApiModelProperty(value = "省name")
    @TableField("province_name")
    private String provinceName;


    /**
     * 市name
     */
    @ApiModelProperty(value = "市name")
    @TableField("city_name")
    private String cityName;


    /**
     * 区域name
     */
    @ApiModelProperty(value = "区域name")
    @TableField("area_name")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @TableField("address")
    private String address;
    /**
     * 平台用户id
     */
    @ApiModelProperty(value = "平台用户id")
    @TableField("platform_id")
    private Long platformId;

    @ApiModelProperty(value = "营业开始时间 HH:mm:ss")
    @TableField("business_begin_hours")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date businessBeginHours;

    @ApiModelProperty(value = "营业结束时间 HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @TableField("business_end_hours")
    private Date businessEndHours;




    /**
     * 店铺logo
     */
    @ApiModelProperty(value = "店铺logo")
    @TableField("logo")
    private String logo;

    /**
     * 是否为主店铺：1.是
     */
    @ApiModelProperty(value = "是否为主店铺：1.是")
    @TableField("main_flag")
    private Integer mainFlag;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    @TableField("business_license")
    private String businessLicense;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("shop_code")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("contacts")
    private String contacts;

    /**
     * 商家入驻表id
     */
    @ApiModelProperty(value = "商家入驻表id")
    @TableField("shops_settled_id")
    private Long shopsSettledId;

    /**
     * 营业状态：0.营业；1.歇业
     */
    @ApiModelProperty(value = "营业状态：0.营业；1.歇业")
    @TableField("trade_status")
    private Integer tradeStatus;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    @TableField("house_number")
    private String houseNumber;


    /**
     * 营业日期用,隔开
     */
    @ApiModelProperty(value = "营业日期用,隔开")
    @TableField("trade_date")
    private String tradeDate;
    /**
     * 会员申请人店铺ID
     */
    @ApiModelProperty(value = "会员申请人店铺ID")
    @TableField("shop_user_id")
    private String shopUserId;

}
