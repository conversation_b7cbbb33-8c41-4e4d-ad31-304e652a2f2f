package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通行票记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_pass_ticket")
@ApiModel(value = "ShopPassTicket对象", description = "通行票记录")
public class ShopPassTicket extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 票名
     */
    @ApiModelProperty(value = "票名")
    @TableField("ticket_name")
    private String ticketName;

    /**
     * 购买单价
     */
    @ApiModelProperty(value = "单价")
    @TableField("price")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    @TableField("useable_times")
    private Integer useableTimes;

    /**
     * 票类型:100->满减;101->折扣;
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    @TableField("ticket_type")
    private Integer ticketType;

    /**
     * 满额（存满100减20的100值）
     */
    @ApiModelProperty(value = "满额")
    @TableField("full_amount")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    @TableField("promotion")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期类型:100->指定时间;101->购买之日起计算;
     */
    @ApiModelProperty(value = "到期类型:100->指定时间;101->购买之日起计算;")
    @TableField("expired_type")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 过期天数，购买之日起，多少天过期
     */
    @ApiModelProperty(value = "过期天数，购买之日起，多少天过期")
    @TableField("expired_days")
    private Integer expiredDays;

    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    @TableField("status")
    private Integer status;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    @TableField("shop_flag")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    @TableField("product_flag")
    private Boolean productFlag;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField("approved_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime approvedTime;


    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    @TableField(value = "approved_user_name")
    private String approvedUserName;

    /**
     * 审核人id
     */
    @ApiModelProperty(value = "审核人id")
    @TableField(value = "approved_user_id")
    private Long approvedUserId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    @TableField("back_pic")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    @TableField("back_color")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    @TableField("rule")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    @TableField("total_num")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @TableField("display_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @TableField("display_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    @TableField("after_days_valid")
    private Integer afterDaysValid;

    /**
     * 审核状态:100->待审核;101->审核通过;200->驳回
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    @TableField("approved_status")
    private Integer approvedStatus;

}
