package com.medusa.gruul.shops.api.model;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:07 2023/9/13
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 展示分类二级分类vo
 */
@Data
@ApiModel(value = "ShopsCategorySecondVo对象", description = "展示分类二级分类查询返回信息")
public class ShopsCategorySecondVo {

    private Long id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "上级分类的编号：0表示一级分类")
    private Long parentId;

    @ApiModelProperty(value = "商品数量")
    private Integer productNumber;

    @ApiModelProperty(value = "展示分类id(数据传输用)")
    private Long shopsCategoryId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "展示图片")
    private String pic;

}
