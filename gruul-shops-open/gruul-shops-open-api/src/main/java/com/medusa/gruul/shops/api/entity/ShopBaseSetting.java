package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运营中心基础设置
 */
@Data
@TableName("t_shop_base_setting")
@ApiModel(value = "运营中心基础设置实体")
public class ShopBaseSetting extends BaseEntity {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 辐射距离(单位米)
     */
    @ApiModelProperty(value = "辐射距离(单位米)")
    @TableField("distance_scope")
    private Integer distanceScope;

    /**
     * 辐射距离作用(1-提醒, 2-限制)
     */
    @ApiModelProperty(value = "辐射距离作用(1-提醒, 2-限制)")
    @TableField("scope_effect")
    private Integer scopeEffect;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 修改人姓名
     */
    @ApiModelProperty(value = "修改人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    @TableField("shop_id")
    private String shopId;

} 