package com.medusa.gruul.shops.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.shops.api.model.ShopFullDonationPartnerVo;
import com.medusa.gruul.shops.api.model.ShopFullDonationProductVo;
import com.medusa.gruul.shops.api.model.ShopFullDonationRuleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:08 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减活动Vo")
public class ShopFullDonationVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "每人限制参数次数")
    private Integer perCount;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "参与会员等级")
    private String memberLevelIds;

    @ApiModelProperty(value = "指定参与商家:0-否，1-参与商家，2-不参与商家")
    private Integer shopsFlag;

    @ApiModelProperty(value = "参与商家名称")
    private String shopsName;


    @ApiModelProperty(value = "指定参与商品:0-否，1-参与商品，2-不参与商品")
    private Integer productFlag;

    @ApiModelProperty(value = "参与商品名称")
    private String productName;

    @ApiModelProperty(value = "赠送商品名称")
    private String giftProductName;

    @ApiModelProperty(value = "赠送优惠券名称")
    private String giftCouponName;

    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过；200->审核不通过；300->失效；400->停止")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "审核人ID")
    private Long auditPlatformUserId;

    @ApiModelProperty(value = "审核人名称")
    private String auditPlatformUserName;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核意见")
    private String auditReason;


    @ApiModelProperty(value = "满赠满减商家")
    private List<ShopFullDonationPartnerVo>shopFullDonationPartnerList;

    @ApiModelProperty(value = "满赠满减商品")
    private List<ShopFullDonationProductVo>shopFullDonationProductList;

    @ApiModelProperty(value = "满赠满减规则")
    private List<ShopFullDonationRuleVo>shopFullDonationRuleList;



}
