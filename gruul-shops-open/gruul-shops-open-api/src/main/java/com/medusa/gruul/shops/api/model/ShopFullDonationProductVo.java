package com.medusa.gruul.shops.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:10 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减商品vo")
public class ShopFullDonationProductVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    private String productName;
}
