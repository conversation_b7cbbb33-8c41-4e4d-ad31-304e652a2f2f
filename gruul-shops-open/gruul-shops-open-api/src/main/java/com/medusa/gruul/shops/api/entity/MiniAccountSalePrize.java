package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_mini_account_sale_prize")
@ApiModel(value = "MiniAccountSalePrize对象", description = "会员参与营销活动记录表")
public class MiniAccountSalePrize extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "userName")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "user_phone")
    @TableField("user_phone")
    private String userPhone;

    @ApiModelProperty(value = "抽奖活动ID")
    @TableField("prize_id")
    private Long prizeId;

    @ApiModelProperty(value = "下单赠予抽奖机会的订单")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "参与几率值")
    @TableField("user_rate")
    private BigDecimal userRate;

    @ApiModelProperty(value = "抽奖明细ID")
    @TableField("prize_item_id")
    private Long prizeItemId;

    @ApiModelProperty(value = "是否奖品 0-否，1-是")
    @TableField("prize_flag")
    private Integer prizeFlag;

    @ApiModelProperty(value = "奖品发放方式：1-线上邮寄，2-线下核销")
    @TableField("verify_type")
    private Integer verifyType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品类型：1-商城商品，2-优惠券")
    @TableField("prize_type")
    private Integer prizeType;

    @ApiModelProperty(value = "商品ID")
    @TableField("product_id")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    @TableField("sku_id")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    @TableField("product_name")
    private String productName;

    @ApiModelProperty(value = "优惠券ID")
    @TableField("coupon_id")
    private Long couponId;

    @ApiModelProperty(value = "奖品名称")
    @TableField("prize_name")
    private String prizeName;

    @ApiModelProperty(value = "中奖的奖品通过邮寄方式发出生成的订单")
    @TableField("post_order_id")
    private Long postOrderId;

    @ApiModelProperty(value = "状态 0-未发放，1-已发放")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "shopId")
    @TableField("shop_id")
    private String shopId;




    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    @ApiModelProperty(value = "修改人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

}