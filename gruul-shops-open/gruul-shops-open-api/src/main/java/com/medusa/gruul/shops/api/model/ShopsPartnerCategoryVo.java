package com.medusa.gruul.shops.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 店铺对应的分类视图
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShopsPartnerCategoryVo对象", description = "店铺对应的分类视图")
public class ShopsPartnerCategoryVo {

    @ApiModelProperty(value = "店铺id")
    private Long shopsPartnerId;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "展示分类id(数据传输用)")
    private Long shopsCategoryId;

}
