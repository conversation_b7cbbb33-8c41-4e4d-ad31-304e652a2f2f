package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:55 2025/7/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_full_donation_rule")
@ApiModel(value = "ShopFullDonationRule对象", description = "满赠满减规则")
public class ShopFullDonationRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;


    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;


    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;


    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_id")
    private Long mainId;

    @ApiModelProperty(value = "满额类型:1-金额，2-件数，3-件数金额")
    @TableField(value = "full_type")
    private Integer fullType;

    @ApiModelProperty(value = "满件")
    @TableField(value = "piece_num")
    private Integer pieceNum;

    @ApiModelProperty(value = "订单满额")
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "购买订单类型:1-首单，2-复购")
    @TableField(value = "buy_type")
    private Integer buyType;

    @ApiModelProperty(value = "满减金额")
    @TableField(value = "reduction_amount")
    private BigDecimal reductionAmount;

    @ApiModelProperty(value = "优惠券ID")
    @TableField(value = "coupon_ids")
    private String couponIds;

    @ApiModelProperty(value = "赠送商品")
    @TableField(value = "sku_ids")
    private String skuIds;


    @ApiModelProperty(value = "满赠自身件数")
    @TableField(value = "self_num")
    private Integer selfNum;

    @ApiModelProperty(value = "规则展示标题")
    @TableField(value = "show_title")
    private String showTitle;
}
