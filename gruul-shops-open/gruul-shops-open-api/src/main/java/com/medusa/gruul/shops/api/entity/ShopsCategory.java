package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: 店铺分类
 * @Date: Created in 18:09 2023/9/12
 */
@Data
@ApiModel(value = "ShopsCategory实体", description = "店铺分类表")
@TableName("t_shops_category")
public class ShopsCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    @ApiModelProperty(value = "分类名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "分类级别：0->1级；1->2级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "分类排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "上级分类的编号：0表示一级分类")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "展示图片")
    @TableField("pic")
    private String pic;

}
