package com.medusa.gruul.shops.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:13 2025/7/4
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopFullDonationParam 实体", description = "满减满赠活动 param")
@Data
public class ShopFullDonationParam extends QueryParam {

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过；200->审核不通过；300->失效；400->停止")
    private Integer status;

    /**
     * 会员等级id列表
     */
    @ApiModelProperty(value = "会员等级id列表")
    private List<String> memberLevelIdList;

    @ApiModelProperty(value = "用户shop_user_id")
    private String userId;

    @ApiModelProperty(value = "订单状态列表")
    private List<Integer> orderStatusList;

    @ApiModelProperty(value = "参与商家")
    private String shopName;

    @ApiModelProperty(value = "参与商品")
    private String productName;

    @ApiModelProperty(value = "赠送商品")
    private String giftProductName;

}
