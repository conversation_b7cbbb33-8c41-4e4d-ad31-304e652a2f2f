package com.medusa.gruul.shops.api.constant;

/**
 * redis常量
 */
public class RedisConstant {


    /**
     * 通行票key
     */
    public static final String PASS_TICKET_KEY = "passTicket:";

    /**
     * 显示优惠券
     */
    public static final String SHOW_COUPON_ACCOUNT = "show:coupon:account:";
    /**
     * 抽奖机会前缀 + userShopId + prizeId
     */
    public static final String PRIZE_KEY = "prize:";
    /**
     * 抽奖-直推 prize+member+用户ID+活动ID
     */
    public static final String PRIZE_DIRECT_MEMBER_KEY = "prize:direct:member:";
    /**
     * 抽奖-直推 prize+memQty+上级用户ID+活动ID，单活计算可抽奖次数
     */
    public static final String PRIZE_DIRECT_MEM_QTY_KEY = "prize:direct:memQty:";
    /**
     * 抽奖机会赠送的订单前缀 + userShopId + prizeId
     */
    public static final String PRIZE_ORDER_KEY = "prize:order:list:";
    /**
     * 抽奖机会会员类型前缀 + userShopId + prizeId
     */
    public static final String PRIZE_MEMBER_TYPE_KEY = "prize:member:type:list:";
    /**
     * 抽奖核销码
     */
    public static final String PRIZE_CODE_KEY = "prize:code:";

}
