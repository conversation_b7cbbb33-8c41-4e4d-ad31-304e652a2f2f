package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.shops.api.entity.ShopFullDonationRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:56 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减活动dto")
public class ShopFullDonationDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "每人限制参数次数")
    private Integer perCount;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "参与会员等级")
    private String memberLevelIds;

    @ApiModelProperty(value = "指定参与商家:0-否，1-参与商家，2-不参与商家")
    private Integer shopsFlag;

    @ApiModelProperty(value = "指定参与商品:0-否，1-参与商品，2-不参与商品")
    private Integer productFlag;

    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过；200->审核不通过；300->失效；400->停止")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "满赠满减商家")
    private List<ShopFullDonationPartnerDto>shopFullDonationPartnerList;

    @ApiModelProperty(value = "满赠满减商品")
    private List<ShopFullDonationProductDto>shopFullDonationProductList;

    @ApiModelProperty(value = "满赠满减规则")
    private List<ShopFullDonationRuleDto>shopFullDonationRuleList;

    @ApiModelProperty(value = "是否验证")
    private Integer vailFlag;




}
