package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.model.param.ApiShopsPartnerParam;
import com.medusa.gruul.shops.model.param.ShopsPartnerParam;
import com.medusa.gruul.shops.model.vo.ApiShopsPartnerVo;
import com.medusa.gruul.shops.model.vo.ShopsPartnerVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> by zq
 * @date created in 2019/11/15
 */
@Repository
public interface ShopsPartnerMapper extends BaseMapper<ShopsPartner> {

    /**
     * 获取总店
     *
     * @param platformId 平台id
     * @return shops
     */
    @SqlParser(filter = true)
    ShopsPartner selectByPlatformId(@Param("platformId") Long platformId);

    /**
     * 根据平台用户id获取店铺信息
     *
     * @return com.medusa.gruul.shops.api.entity.ShopsPartner
     */
    @SqlParser(filter = true)
    ShopsPartner selectByTenantIdAndPartnerIdIsNull();


    /**
     * 分页查询店铺信息
     * @param shopsPartnerVoPage
     * @param shopsPartnerParam
     * @return
     */
    IPage<ShopsPartnerVo> selectShopsPartnerPage(Page<ShopsPartnerVo> shopsPartnerVoPage, @Param("paramMap") ShopsPartnerParam shopsPartnerParam);

    /**
     * 分页查询店铺信息-搜索弹窗
     * @param shopsPartnerVoPage
     * @return
     */
    IPage<ShopsPartnerVo> selectShopsPartnerPageQuery(Page<ShopsPartnerVo> shopsPartnerVoPage,@Param("paramMap")ShopsPartnerParam shopsPartnerParam);
    /**
     * 获取最新一条记录
     * @return
     */
    ShopsPartner getNewOne();

    /**
     * 获取小程序商家列表
     * @param apiShopsPartnerParam
     * @return
     */
    List<ApiShopsPartnerVo> queryShopsPartnerList(@Param("paramMap")ApiShopsPartnerParam apiShopsPartnerParam);
}
