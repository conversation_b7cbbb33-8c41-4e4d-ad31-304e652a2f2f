package com.medusa.gruul.shops.model.vo;

import com.medusa.gruul.account.api.entity.MiniAccountAddress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员抽奖记录返回VO
 */
@Data
@ApiModel(value = "MiniAccountSalePrizeVo对象", description = "会员抽奖记录返回VO")
public class MiniAccountSalePrizeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户电话")
    private String userPhone;

    @ApiModelProperty(value = "抽奖活动ID")
    private Long prizeId;

    @ApiModelProperty(value = "活动名称")
    private String mainPrizeName;

    @ApiModelProperty(value = "奖项ID")
    private Long prizeItemId;

    @ApiModelProperty(value = "奖项名称")
    private String levelName;

    @ApiModelProperty(value = "是否奖品/是否中奖 0-否，1-是，")
    private Integer prizeFlag;
    
    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖品发放方式：1->线上邮寄; 2->线下核销")
    private Integer verifyType;

    @ApiModelProperty(value = "奖品类型：1->商城商品；2->优惠券")
    private Integer prizeType;

    @ApiModelProperty(value = "状态：0->未发放；1->已发放")
    private Integer status;

    @ApiModelProperty(value = "是否显示核销二维码")
    private Boolean isVerifyCode;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "中奖的奖品通过邮寄方式发出生成的订单")
    private Long postOrderId;

    @ApiModelProperty(value = "收货地址")
    private MiniAccountAddress addressInfo;

    @ApiModelProperty(value = "奖品有效期开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "奖品有效期结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品图片")
    private String productPic;

    @ApiModelProperty(value = "创建时间 - 中奖时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "核销人Id")
    private String verifyUserId;

    @ApiModelProperty(value = "核销时间")
    private LocalDateTime verifyTime;

    @ApiModelProperty(value = "核销人名称")
    private String verifyNickName;
} 