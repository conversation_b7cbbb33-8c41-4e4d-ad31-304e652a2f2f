package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通行票-商品
 */
@Data
@ApiModel(value = "通行票-商品dto")
public class ShopPassTicketProductDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 通行票id
     */
    @ApiModelProperty(value = "通行票id")
    private Long passTicketId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long productId;

    /**
     * 商品skuid
     */
    @ApiModelProperty(value = "商品skuid")
    private Long productSkuId;


}
