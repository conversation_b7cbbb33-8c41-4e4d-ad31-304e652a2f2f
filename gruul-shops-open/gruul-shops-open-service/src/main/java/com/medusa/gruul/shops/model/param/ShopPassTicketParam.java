package com.medusa.gruul.shops.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 通惠证查询实体
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopPassTicketParam 实体", description = "通惠证 param")
@Data
public class ShopPassTicketParam extends QueryParam {

    /**
     * 通惠证名称
     */
    @ApiModelProperty(value = "通惠证名称")
    private String ticketName;

    /**
     * 票类型:100->满减;101->折扣;
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    private Integer ticketType;

    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;


    @ApiModelProperty(value = "每个店铺使用次数")
    private Integer useableTimes;

    @ApiModelProperty(value = "生效开始时间")
    private Date startTimeBegin;

    @ApiModelProperty(value = "生效结束时间")
    private Date startTimeEnd;

    @ApiModelProperty(value = "过期开始时间")
    private Date endTimeBegin;

    @ApiModelProperty(value = "过期结束时间")
    private Date endTimeEnd;

    /**
     * 审核状态:100->待审核;101->审核通过;200->驳回
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    private Integer approvedStatus;

    /**
     * 1->生效时间升序；2->生效时间降序
     */
    @ApiModelProperty(value = "排序字段：1->生效时间升序；2->生效时间降序")
    private Integer startTimeSort;

    /**
     * 1->过期时间升序；2->过期时间降序
     */
    @ApiModelProperty(value = "排序字段：1->过期时间升序；2->过期时间降序")
    private Integer endTimeSort;

    /**
     * 1->售价升序；2->售价降序
     */
    @ApiModelProperty(value = "排序字段：1->售价升序；2->售价降序")
    private Integer priceSort;

    /**
     * 1->使用次数升序；2->使用次数降序
     */
    @ApiModelProperty(value = "排序字段：1->使用次数升序；2->使用次数降序")
    private Integer useableTimesSort;

    /**
     * 显示日期
     */
    @ApiModelProperty(value = "显示日期")
    private String displayDate;

    /**
     * 能显示通惠证的状态值:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     * 100和101 能显示出来
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private List<Integer> displayStatus;

}
