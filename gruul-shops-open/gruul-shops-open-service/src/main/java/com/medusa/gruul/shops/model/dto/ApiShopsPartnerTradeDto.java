package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:11 2023/9/13
 */
@Data
@ApiModel(value = "小程序店铺营业时间")
public class ApiShopsPartnerTradeDto {

    @ApiModelProperty(value = "id",required = true)
    @NotNull
    private Long id;

    /**
     * 营业开始时间
     */
    @ApiModelProperty(value = "营业开始时间 HH:mm:ss",required = true)
    @NotNull(message = "营业开始时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date businessBeginHours;

    /**
     * 营业结束时间
     */
    @ApiModelProperty(value = "营业结束时间 HH:mm:ss",required = true)
    @NotNull(message = "营业结束时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date businessEndHours;

    /**
     * 营业状态：0.营业；1.歇业
     */
    @ApiModelProperty(value = "营业状态：0.营业；1.歇业",required = true)
    @NotNull(message = "营业状态不能为空")
    private Integer tradeStatus;


    /**
     * 营业日期用,隔开
     */
    @ApiModelProperty(value = "营业日期用,隔开",required = true)
    @NotNull(message = "营业日期不能为空")
    private String tradeDate;
}
