package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通惠证-指定商品
 */
@Data
@ApiModel(value = "通惠证-指定商品Vo")
public class ShopPassTicketProductVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long productId;

    /**
     * 商品skuid
     */
    @ApiModelProperty(value = "商品skuid")
    private Long productSkuId;


}
