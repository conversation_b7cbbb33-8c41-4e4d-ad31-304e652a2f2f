package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:27 2025/4/2
 */
@Data
@ApiModel(value = "优惠券-指定发券对象dto")
public class ShopCouponAccountDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    /**
     * 发券对象id
     */
    @ApiModelProperty(value = "发券对象id")
    private String sourceId;

}

