package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "抽奖入参dto")
public class RafflePrizeDto {

    @NotNull(message = "抽奖活动ID不能为空")
    @ApiModelProperty(value = "抽奖活动ID")
    private Long prizeId;

    @ApiModelProperty(value = "店铺ID")
    private String shopId;

    @ApiModelProperty(value = "会员类型ID")
    private Long memberTypeId ;
}
