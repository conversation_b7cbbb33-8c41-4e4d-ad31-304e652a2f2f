package com.medusa.gruul.shops.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.enums.PartnerModelEnum;
import com.medusa.gruul.shops.api.enums.TradeStatusEnum;
import com.medusa.gruul.shops.api.model.ShopsPartnerCategoryVo;
import com.medusa.gruul.shops.api.enums.ProhibitStatusEnum;
import com.medusa.gruul.shops.mapper.ShopsCategoryMapper;
import com.medusa.gruul.shops.mapper.ShopsPartnerMapper;
import com.medusa.gruul.shops.mapper.ShopsShowCategoryMapper;
import com.medusa.gruul.shops.model.dto.*;
import com.medusa.gruul.shops.model.param.ApiShopsPartnerParam;
import com.medusa.gruul.shops.model.param.ShopsPartnerParam;
import com.medusa.gruul.shops.model.vo.ApiShopsPartnerVo;
import com.medusa.gruul.shops.model.vo.ShopsPartnerExcelVo;
import com.medusa.gruul.shops.model.vo.ShopsPartnerMapVo;
import com.medusa.gruul.shops.model.vo.ShopsPartnerVo;
import com.medusa.gruul.shops.properties.GlobalConstant;
import com.medusa.gruul.shops.service.IShopPassTicketService;
import com.medusa.gruul.shops.service.ShopsPartnerService;
import com.medusa.gruul.shops.service.ShopsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> by zq
 * @date created in 2020/01/14
 */
@Service(value = "shopsPartnerServiceImpl")
public class ShopsPartnerServiceImpl extends ServiceImpl<ShopsPartnerMapper, ShopsPartner> implements ShopsPartnerService {

    @Autowired
    private ShopsService shopsService;

    @Autowired
    private ShopsShowCategoryMapper shopsShowCategoryMapper;

    @Autowired
    private ShopsCategoryMapper shopsCategoryMapper;

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private IShopPassTicketService shopPassTicketService;


    /**
     * 获取店铺list
     *
     * @return list
     */
    @Override
    public Result listShopsPartner() {
        String shopId = ShopContextHolder.getShopId();
        ShopsPartner shopsPartner = this.getByShopId(Long.valueOf(shopId));
        if(shopsPartner!=null&&shopsPartner.getMainFlag()!=null&&shopsPartner.getMainFlag()==1){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        List<ShopsPartner> shopsPartners = this.baseMapper.selectList(new QueryWrapper<ShopsPartner>()
                .eq("approval_status", ApproveStatusEnum.APPROVED.getStatus())
                .eq("prohibit_status", GlobalConstant.STRING_ZERO));
        ShopContextHolder.setShopId(shopId);
        return Result.ok(shopsPartners);
    }


    /**
     * 获取店铺
     *
     * @return shops
     */
    @Override
    public ShopsPartner oneByShopId() {
        return baseMapper.selectOne(new QueryWrapper<ShopsPartner>()
                .eq("approval_status", GlobalConstant.STRING_ONE)
                .eq("prohibit_status", GlobalConstant.STRING_ZERO));
    }

    @Override
    public ShopsPartner getShopsPartner() {
        return baseMapper.selectOne(new QueryWrapper<ShopsPartner>());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ShopsPartner> saveShopsPartner(String pass, String phone, Long platformId) {
        //查询默认账号是否存在存在;存在则不做操作直接返回
        ShopsPartner shopsPartner = this.baseMapper.selectByTenantIdAndPartnerIdIsNull();
        if (ObjectUtil.isNotNull(shopsPartner)) {
            return Result.ok(shopsPartner);
        }
        shopsPartner = new ShopsPartner();
        shopsPartner.setPass(pass);
        shopsPartner.setPhone(phone);
        shopsPartner.setInvitationCode(generateInvitationCode());
        shopsPartner.setPlatformId(platformId);
        shopsPartner.setProhibitStatus(GlobalConstant.STRING_ZERO);
        shopsPartner.setApprovalStatus(ApproveStatusEnum.AUDIT.getStatus()+"");
        int insert = this.baseMapper.insert(shopsPartner);
        if (insert < 1 || null == shopsPartner.getId()) {
            throw new ServiceException(String.format("insert [shopPartner] fail! status is not  : %s, pass : %s, phone : %s", pass, phone));
        }
        Shops shops = new Shops();
        if (!shopsService.save(shops)) {
            throw new ServiceException(String.format("insert [shop] fail! status is not  : %s : %s", shopsPartner.getId()));
        }
        return Result.ok(shopsPartner);
    }

    @Override
    public ShopsPartner getByPlatformId(Long platformId) {
        return this.baseMapper.selectByPlatformId(platformId);
    }

    @Override
    public ShopsPartner getByShopId(Long shopId) {
        LambdaQueryWrapper<ShopsPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsPartner::getShopId,shopId);
        ShopsPartner shopsPartner = this.baseMapper.selectOne(wrapper);
        return shopsPartner;
    }
    @Override
    public PageUtils<ShopsPartnerVo> searchAuthBusinessInfoQueryList(ShopsPartnerParam shopsPartnerParam) {
        ShopsPartner shopsPartner = getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        String shopId = ShopContextHolder.getShopId();
        //主店铺可查看所有店铺
        if(mainFlag==1){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
            List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
            shopIds.add(shopId);
            shopsPartnerParam.setShopIds(shopIds);
        }

        IPage<ShopsPartnerVo> shopsPartnerVoIPage = this.baseMapper.selectShopsPartnerPageQuery(new Page<>(shopsPartnerParam.getCurrent(), shopsPartnerParam.getSize()),shopsPartnerParam);
        List<ShopsPartnerVo> records = shopsPartnerVoIPage.getRecords();
        if(records!=null&&records.size()>0){
            for (ShopsPartnerVo shopsPartnerVo : records) {
                String name = "";
                Long id = shopsPartnerVo.getId();
                List<String> nameList = shopsCategoryMapper.getShopsCategoryName(id);
                if(nameList!=null&&nameList.size()>0){
                    for (String str : nameList) {
                        if(name.length()>0){
                            name+="，";
                        }
                        name += str;
                    }
                }
                shopsPartnerVo.setCategoryName(name);
            }

        }
        ShopContextHolder.setShopId(shopId);
        return new PageUtils(shopsPartnerVoIPage);
    }
    @Override
    public PageUtils<ShopsPartnerVo> searchAuthBusinessInfo(ShopsPartnerParam shopsPartnerParam) {
        String shopId = ShopContextHolder.getShopId();
        ShopsPartner shopsPartner = this.getByShopId(Long.valueOf(shopId));
        if(shopsPartner!=null&&shopsPartner.getMainFlag()!=null&&shopsPartner.getMainFlag()==1){
            // 主店铺可查看所有店铺
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        IPage<ShopsPartnerVo> shopsPartnerVoIPage = this.baseMapper.selectShopsPartnerPageQuery(new Page<>(shopsPartnerParam.getCurrent(), shopsPartnerParam.getSize()), shopsPartnerParam);
        List<ShopsPartnerVo> records = shopsPartnerVoIPage.getRecords();
        if(records!=null&&records.size()>0){
            for (ShopsPartnerVo shopsPartnerVo : records) {
                String name = "";
                Long id = shopsPartnerVo.getId();
                List<String> nameList = shopsCategoryMapper.getShopsCategoryName(id);
                if(nameList!=null&&nameList.size()>0){
                    for (String str : nameList) {
                        if(name.length()>0){
                            name+="，";
                        }
                        name += str;
                    }
                }
                shopsPartnerVo.setCategoryName(name);
            }
        }
        ShopContextHolder.setShopId(shopId);
        return new PageUtils(shopsPartnerVoIPage);
    }





    @Override
    public List<ShopsPartner> getShopsPartnerList(String name) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaQueryWrapper<ShopsPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsPartner::getApprovalStatus,ApproveStatusEnum.APPROVED.getStatus());
        wrapper.eq(ShopsPartner::getProhibitStatus,GlobalConstant.STRING_ZERO);
        if(StringUtils.isNotEmpty(name)){
            wrapper.like(ShopsPartner::getName,name);
        }
        wrapper.last("limit 10");
        List<ShopsPartner> list = this.baseMapper.selectList(wrapper);
        ShopContextHolder.setShopId(oldShopId);
        return list;
    }

    @Override
    public ShopsPartner addShopsPartner(ShopsPartnerDto shopsPartnerDto) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        String phone = shopsPartnerDto.getPhone();
        LambdaQueryWrapper<ShopsPartner>wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(ShopsPartner::getPhone,phone);
        ShopsPartner oldShopsPartner = this.getOne(wrapper1);
        if(oldShopsPartner!=null){
            throw new ServiceException("手机号已经存在！");
        }

        ShopsPartner one = this.baseMapper.getNewOne();
        String tenantId = TenantContextHolder.getTenantId();
        String shopId = "";
        if(one!=null){
            shopId = one.getShopId();
            shopId = getCode(shopId,tenantId);
        }else{
            shopId = tenantId + "00001";
        }
        shopsPartnerDto.setShopId(shopId);
        ShopsPartner shopsPartner = new ShopsPartner();
        BeanUtils.copyProperties(shopsPartnerDto,shopsPartner);
        if(StringUtils.isEmpty(shopsPartner.getApprovalStatus())){
            shopsPartner.setApprovalStatus(ApproveStatusEnum.APPROVED.getStatus()+"");
        }
        shopsPartner.setProhibitStatus(ProhibitStatusEnum.NORMAL.getStatus());
        this.baseMapper.insert(shopsPartner);
        ShopContextHolder.setShopId(oldShopId);
        return shopsPartner;
    }

    @Override
    public void deleteById(Long id) {
        ShopsPartner shopsPartner = this.getById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家不存在，不能删除！");
        }

        Result<Boolean> result = remoteMiniInfoService.userIsExistByShopId(shopsPartner.getShopId());
        Boolean b = result.getData();
        if(b){
            throw new ServiceException("该商家存在用户，不能删除！");
        }
        this.removeById(shopsPartner.getId());

    }

    @Override
    public ShopsPartner editShopsPartner(ShopsPartnerDto shopsPartnerDto) {
        ShopsPartner shopsPartner =  this.getById(shopsPartnerDto.getId());
        if(shopsPartner==null){
            throw new ServiceException("商家不存在，不能删除！");
        }
        LambdaQueryWrapper<ShopsPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsPartner::getPhone,shopsPartnerDto.getPhone());
        wrapper.ne(ShopsPartner::getId,shopsPartnerDto.getId());
        List<ShopsPartner> list = list(wrapper);
        if(list!=null&&list.size()>0){
            throw new ServiceException("手机号已经存在！");
        }
        BeanUtils.copyProperties(shopsPartnerDto,shopsPartner);
        this.updateById(shopsPartner);
        return shopsPartner;
    }

    @Override
    public ShopsPartner getShopsPartnerMain() {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaQueryWrapper<ShopsPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsPartner::getMainFlag,1).last("limit 1");
        ShopsPartner shopsPartner = this.getOne(wrapper);
        ShopContextHolder.setShopId(shopId);
        return shopsPartner;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShopsPartner(ShopsPartnerUpdateDto shopsPartnerUpdateDto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Long id = shopsPartnerUpdateDto.getId();
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家信息不存在！");
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getCardIdDown())){
            shopsPartner.setCardIdDown(shopsPartnerUpdateDto.getCardIdDown());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getCardIdUp())){
            shopsPartner.setCardIdUp(shopsPartnerUpdateDto.getCardIdUp());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getBusinessLicense())){
            shopsPartner.setBusinessLicense(shopsPartnerUpdateDto.getBusinessLicense());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getAreaCode())){
            shopsPartner.setAreaCode(shopsPartnerUpdateDto.getAreaCode());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getAreaName())){
            shopsPartner.setAreaName(shopsPartnerUpdateDto.getAreaName());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getCityCode())){
            shopsPartner.setCityCode(shopsPartnerUpdateDto.getCityCode());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getCityName())){
            shopsPartner.setCityName(shopsPartnerUpdateDto.getCityName());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getProvinceCode())){
            shopsPartner.setProvinceCode(shopsPartnerUpdateDto.getProvinceCode());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getProvinceName())){
            shopsPartner.setProvinceName(shopsPartnerUpdateDto.getProvinceName());
        }
        if(StringUtils.isNotEmpty(shopsPartnerUpdateDto.getLogo())){
            shopsPartner.setLogo(shopsPartnerUpdateDto.getLogo());
        }
        List<Long> shopsCategoryIds = shopsPartnerUpdateDto.getShopsCategoryIds();
        LambdaQueryWrapper<ShopsShowCategory>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsShowCategory::getShopsPartnerId,shopsPartner.getId());
        shopsShowCategoryMapper.delete(wrapper);
        if(shopsCategoryIds!=null&&shopsCategoryIds.size()>0){
            for (Long shopsCategoryId : shopsCategoryIds) {
                if(shopsCategoryId!=null){
                    ShopsCategory shopsCategory = shopsCategoryMapper.selectById(shopsCategoryId);
                    if(shopsCategory==null){
                        throw new ServiceException("商家分类不存在！");
                    }
                    if(shopsCategory.getParentId()!=0){
                        ShopsShowCategory shopsShowCategoryParent = new ShopsShowCategory();
                        shopsShowCategoryParent.setParentId(0L);
                        shopsShowCategoryParent.setShopsCategoryId(shopsCategory.getParentId());
                        shopsShowCategoryParent.setShopsPartnerId(shopsPartner.getId());
                        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                        shopsShowCategoryParent.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                        shopsShowCategoryParent.setCreateUserName(pcUserInfoDto.getNikeName());
                        shopsShowCategoryMapper.insert(shopsShowCategoryParent);
                        ShopsShowCategory shopsShowCategory = new ShopsShowCategory();
                        shopsShowCategory.setParentId(shopsCategory.getParentId());
                        shopsShowCategory.setShopsCategoryId(shopsCategory.getId());
                        shopsShowCategory.setShopsPartnerId(shopsPartner.getId());
                        shopsShowCategory.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                        shopsShowCategory.setCreateUserName(pcUserInfoDto.getNikeName());
                        shopsShowCategoryMapper.insert(shopsShowCategory);
                    }
                }

            }
        }
        this.updateById(shopsPartner);

        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public void stopShopsPartner(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家不存在！");
        }
        shopsPartner.setProhibitStatus(ProhibitStatusEnum.DISABLE.getStatus());
        this.updateById(shopsPartner);
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public void startShopsPartner(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家不存在！");
        }
        shopsPartner.setProhibitStatus(ProhibitStatusEnum.NORMAL.getStatus());
        this.updateById(shopsPartner);
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shopsPartnerSetMainFlag(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaUpdateWrapper<ShopsPartner> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ShopsPartner::getMainFlag,0);
        this.baseMapper.update(null,wrapper);
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家不存在！");
        }
        shopsPartner.setMainFlag(1);
        this.baseMapper.updateById(shopsPartner);
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public void apiUpdate(ApiShopsPartnerDto apiShopsPartnerDto) {
        Long id = apiShopsPartnerDto.getId();
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner == null){
            throw new ServiceException("商家不存在！");
        }
        LambdaQueryWrapper<ShopsPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsPartner::getPhone,apiShopsPartnerDto.getPhone());
        wrapper.ne(ShopsPartner::getId,apiShopsPartnerDto.getId());
        List<ShopsPartner> list = list(wrapper);
        if(list!=null&&list.size()>0){
            throw new ServiceException("手机号已经存在！");
        }
        BeanUtils.copyProperties(apiShopsPartnerDto,shopsPartner);
        this.baseMapper.updateById(shopsPartner);
    }

    @Override
    public void apiUpdateLogo(ApiShopsPartnerLogoDto apiShopsPartnerLogoDto) {
        Long id = apiShopsPartnerLogoDto.getId();
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner == null){
            throw new ServiceException("商家不存在！");
        }
        BeanUtils.copyProperties(apiShopsPartnerLogoDto,shopsPartner);
        this.baseMapper.updateById(shopsPartner);
    }

    @Override
    public void apiUpdateTrade(ApiShopsPartnerTradeDto apiShopsPartnerTradeDto) {
        Long id = apiShopsPartnerTradeDto.getId();
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner == null){
            throw new ServiceException("商家不存在！");
        }
        BeanUtils.copyProperties(apiShopsPartnerTradeDto,shopsPartner);
        if(apiShopsPartnerTradeDto.getTradeStatus()== TradeStatusEnum.START.getType()){
            shopsPartner.setTradeStatus(TradeStatusEnum.START.getType());
        }
        if(apiShopsPartnerTradeDto.getTradeStatus()== TradeStatusEnum.END.getType()){
            shopsPartner.setTradeStatus(TradeStatusEnum.END.getType());
        }
        this.baseMapper.updateById(shopsPartner);
    }

    private String generateInvitationCode() {
        int randomInt = RandomUtil.randomInt(10000, 99999);
        ShopsPartner shopsPartner = this.baseMapper.selectOne(new QueryWrapper<ShopsPartner>().eq("invitation_code", randomInt));
        if (shopsPartner != null) {
            return generateInvitationCode();
        }
        return String.valueOf(randomInt);
    }


    /**
     * 字符类型+1
     * @param originalValue
     * @param identifier
     * @return
     */
    private static String getCode(String originalValue, String identifier){
        String num = originalValue.substring(identifier.length());
        int n = num.length();
        if ("".equals(num)) {
            num = "0";
        }
        int nums = Integer.parseInt(num) + 1;
        String newNum = String.valueOf(nums);
        n = Math.min(n, newNum.length());
        return originalValue.subSequence(0, originalValue.length() - n) + newNum;

    }

    /**
     * 导入商家
     *
     * @param file 商家xlsx文件
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importExcelShopsPartner(MultipartFile file) {
        if (file.getSize() == 0) {
            throw new ServiceException("文件表格异常", SystemCode.DATA_EXISTED_CODE);
        }
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        boolean success = false;
        if (".xlsx".equals(suffix)) {
            try {
                InputStream stream = file.getInputStream();
                List<ShopsPartnerExcelDto> entityList = HuToolExcelUtils.importExcelByStream(stream, ShopsPartnerExcelDto.class, 1, 2);
                List<ShopsPartner> partnerList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(entityList)) {
                    String oldShopId = ShopContextHolder.getShopId();
                    // 取同一个租户最大的shopId
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                    ShopsPartner one = this.baseMapper.getNewOne();
                    String tenantId = TenantContextHolder.getTenantId();
                    final String[] shopId = {""};
                    if(one != null){
                        shopId[0] = one.getShopId();
                    }
                    // 存储传过来的电话，判断是否有重复
                    Set<String> phoneSet = new HashSet<>();

                    entityList.stream().forEach(e -> {
                        String phone = e.getPhone();
                        LambdaQueryWrapper<ShopsPartner>wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(ShopsPartner::getPhone,phone);
                        ShopsPartner oldShopsPartner = this.getOne(wrapper1);
                        if(oldShopsPartner!=null){
                            throw new ServiceException( phone + "手机号已经存在！");
                        }
                        if(phoneSet.contains(phone)){
                            throw new ServiceException( phone + "手机号在表格中重复！");
                        }
                        phoneSet.add(phone);
                        if(null == one && StrUtil.isBlank(shopId[0])){
                            shopId[0] = tenantId + "00001";
                        }else{
                            shopId[0] = getCode(shopId[0],tenantId);
                        }

                        e.setShopId(shopId[0]);
                        ShopsPartner shopsPartner = new ShopsPartner();
                        BeanUtils.copyProperties(e,shopsPartner);
                        if(StringUtils.isEmpty(shopsPartner.getApprovalStatus())){
                            shopsPartner.setApprovalStatus(ApproveStatusEnum.APPROVED.getStatus()+"");
                        }
                        shopsPartner.setProhibitStatus(ProhibitStatusEnum.NORMAL.getStatus());
                        shopsPartner.setMainFlag(CommonConstants.NUMBER_ZERO);
                        partnerList.add(shopsPartner);
                    });
                    success = this.saveBatch(partnerList);
                    ShopContextHolder.setShopId(oldShopId);
                }

            } catch (IOException e) {
                e.printStackTrace();
                throw new ServiceException("文件解析失败！", SystemCode.DATA_ADD_FAILED_CODE);
            }
        } else {
            throw new ServiceException("文件格式有误,请重新上传.xlsx文件!", SystemCode.PARAM_TYPE_ERROR_CODE);
        }
        return success ? Result.ok("导入成功") : Result.ok("导入失败");
    }

    @Override
    public ShopsPartnerVo queryById(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        ShopsPartner shopsPartner = this.baseMapper.selectById(id);
        if(shopsPartner==null){
            throw new ServiceException("商家信息不存在！");
        }
        ShopsPartnerVo shopsPartnerVo = new ShopsPartnerVo();
        BeanUtils.copyProperties(shopsPartner,shopsPartnerVo);
        LambdaUpdateWrapper<ShopsShowCategory>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ShopsShowCategory::getShopsPartnerId,shopsPartner.getId());
        wrapper.ne(ShopsShowCategory::getParentId,0);
        List<ShopsShowCategory> list = shopsShowCategoryMapper.selectList(wrapper);
        List<Long> shopsCategoryIds = list.stream().map(ShopsShowCategory::getShopsCategoryId).collect(Collectors.toList());
        shopsPartnerVo.setShopsCategoryIds(shopsCategoryIds);
        ShopContextHolder.setShopId(shopId);
        return shopsPartnerVo;
    }

    /**
     * 根据shopId查询商家信息
     * @param shopId
     * @return
     */
    @Override
    public ShopsPartnerVo queryByShopId(String shopId) {
        String contextShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaQueryWrapper<ShopsPartner> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.eq(ShopsPartner::getShopId, shopId);
        ShopsPartner shopsPartner = this.getOne(shopWrapper);
        if(null == shopsPartner){
            throw new ServiceException("数据不存在！", SystemCode.DATA_NOT_EXIST.getCode());
        }

        ShopsPartnerVo shopsPartnerVo = new ShopsPartnerVo();
        BeanUtils.copyProperties(shopsPartner, shopsPartnerVo);

        List<Long> idList = new ArrayList<>();
        idList.add(shopsPartner.getId());
        List<ShopsPartnerCategoryVo> partnerVoList = this.shopsCategoryMapper.getManyShopsCategoryName(idList);

        List<Long> shopsCategoryIds = partnerVoList.stream().map(ShopsPartnerCategoryVo::getShopsCategoryId).collect(Collectors.toList());
        shopsPartnerVo.setShopsCategoryIds(shopsCategoryIds);

        String names = partnerVoList.stream().map(ShopsPartnerCategoryVo::getName).collect(Collectors.joining(","));
        shopsPartnerVo.setShopsCategoryName(names);
        if(StrUtil.isNotBlank(shopsPartnerVo.getTradeDate())){
            String tradeDate = shopsPartnerVo.getTradeDate();
            String formatTradeDate = "";
            String []tradeDates = shopsPartnerVo.getTradeDate().split(",");
            if (tradeDates.length == 7) {
                formatTradeDate = "周一至周日";
            }else if (tradeDates.length == 6) {
                if (tradeDate.indexOf("周一") == -1) {
                    formatTradeDate = "周二至周日";
                } else if (tradeDate.indexOf("周二") == -1) {
                    formatTradeDate = "周一，周三至周日";
                } else if (tradeDate.indexOf("周三") == -1) {
                    formatTradeDate = "周一，周二，周四至周日";
                } else if (tradeDate.indexOf("周四") == -1) {
                    formatTradeDate = "周一至周三，周五至周日";
                } else if (tradeDate.indexOf("周五") == -1) {
                    formatTradeDate = "周一至周四，周六，周日";
                } else if (tradeDate.indexOf("周六") == -1) {
                    formatTradeDate = "周一至周五，周日";
                } else {
                    formatTradeDate = "周一至周六";
                }

            }else if (tradeDates.length == 5) {
                if (tradeDate.indexOf("周一") == -1 && tradeDate.indexOf("周二") == -1) {
                    formatTradeDate = "周三至周日";
                } else if (tradeDate.indexOf("周六") == -1 && tradeDate.indexOf("周日") == -1) {
                    formatTradeDate = "周一至周五";
                } else if (tradeDate.indexOf("周一") == -1 && tradeDate.indexOf("周日") == -1) {
                    formatTradeDate = "周二至周六";
                }else{
                    formatTradeDate=tradeDate;
                }
            }else{
                formatTradeDate=tradeDate;
            }
            shopsPartnerVo.setFormatTradeDate(formatTradeDate);
        }

        // 查询可用通惠证
        List<ShopPassTicket> ticketList = this.shopPassTicketService.queryByShopPartnerId(shopsPartner.getId());
        String ticketNames = ticketList.stream().map(ShopPassTicket::getTicketName).collect(Collectors.joining(","));
        shopsPartnerVo.setUsableShopTicketName(ticketNames);
        ShopContextHolder.setShopId(contextShopId);
        return shopsPartnerVo;
    }

    @Override
    public List<ApiShopsPartnerVo> queryShopsPartnerList(ApiShopsPartnerParam apiShopsPartnerParam) {
        List<ApiShopsPartnerVo>list = this.baseMapper.queryShopsPartnerList(apiShopsPartnerParam);
        if(list!=null&&list.size()>0){
            Integer count = shopPassTicketService.countShopFlag();
            for (ApiShopsPartnerVo apiShopsPartnerVo : list) {
                if(apiShopsPartnerVo.getMapX()!=null&&apiShopsPartnerVo.getMapY()!=null&&apiShopsPartnerParam.getLatitude()!=null&&apiShopsPartnerParam.getLongitude()!=null){
                    double distance = DistanceUtil.distanceByLngLat(apiShopsPartnerParam.getLongitude(), apiShopsPartnerParam.getLatitude(), apiShopsPartnerVo.getMapX(), apiShopsPartnerVo.getMapY());
                    apiShopsPartnerVo.setDistance(distance);
                    String s = DistanceUtil.formatDistance(distance);
                    apiShopsPartnerVo.setDistanceStr(s);
                }
                if(apiShopsPartnerVo.getDistance() == null){
                    apiShopsPartnerVo.setDistance(new Double("0"));
                    apiShopsPartnerVo.setDistanceStr("0m");
                }

                if(count>0){
                    apiShopsPartnerVo.setShopPassTicketFlag(1);
                }else{
                    Integer count2 = shopPassTicketService.countShopFlagByShopsPartnerId(apiShopsPartnerVo.getId());
                    if(count2>0){
                        apiShopsPartnerVo.setShopPassTicketFlag(1);
                    }else{
                        apiShopsPartnerVo.setShopPassTicketFlag(0);
                    }
                }
                Long id = apiShopsPartnerVo.getId();

                List<String> strList = shopsCategoryMapper.getShopsCategoryName(id);
                if(strList!=null&&strList.size()>0){
                    apiShopsPartnerVo.setShopsCategoryName(strList.get(0));
                }
                List<String> parentStrList = shopsCategoryMapper.getParentShopsCategoryName(id);
                if(parentStrList!=null&&parentStrList.size()>0){
                    apiShopsPartnerVo.setShopsParentCategoryName(parentStrList.get(0));
                }
            }
            if(apiShopsPartnerParam.getDistance()!=null){
                list= list.stream().filter(apiShopsPartnerVo -> apiShopsPartnerVo.getDistance() <= apiShopsPartnerParam.getDistance()).collect(Collectors.toList());
            }

            if(apiShopsPartnerParam.getSort()!=null&&apiShopsPartnerParam.getSort()==1){
                list = list.stream().sorted(Comparator.comparing(ApiShopsPartnerVo::getDistance)).collect(Collectors.toList());
            }
        }
        return list;
    }
    @Override
    public List<ShopsPartnerMapVo> getOperationCenterMapData(Double longitude, Double latitude) {
        // 查询所有已审核通过且启用的运营中心
        LambdaQueryWrapper<ShopsPartner> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopsPartner::getApprovalStatus, ApproveStatusEnum.APPROVED.getStatus())
                .eq(ShopsPartner::getProhibitStatus, ProhibitStatusEnum.NORMAL.getStatus())
                .eq(ShopsPartner::getPartnerModel, PartnerModelEnum.OPERATION_CENTER.getType())
                .isNotNull(ShopsPartner::getMapX)
                .isNotNull(ShopsPartner::getMapY);

        List<ShopsPartner> operationCenters = this.list(queryWrapper);

        // 转换为VO对象
        List<ShopsPartnerMapVo> result = new ArrayList<>(operationCenters.size());
        for (ShopsPartner center : operationCenters) {
            ShopsPartnerMapVo vo = new ShopsPartnerMapVo();
            BeanUtils.copyProperties(center, vo);
            // 如果传入了用户位置，计算距离
            if (longitude != null && latitude != null && center.getMapX() != null && center.getMapY() != null) {
                double distance = DistanceUtil.distanceByLngLat(longitude, latitude, center.getMapX(), center.getMapY());
                vo.setDistance(distance);
            }
            result.add(vo);
        }
        // 如果有位置信息，按距离排序
        if (longitude != null && latitude != null) {
            result.sort(Comparator.comparing(ShopsPartnerMapVo::getDistance, Comparator.nullsLast(Double::compareTo)));
        }
        return result;
    }

    @Override
    public List<ShopsPartnerMapVo> getOperationCenterMapData() {
        return SpringContextHolder.getBean(ShopsPartnerService.class).getOperationCenterMapData(null, null);
    }

    @Override
    public Integer getMainFlag() {
        ShopsPartner shopsPartner = this.getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        return mainFlag;
    }

    @Override
    public void exportShopsPartner(ShopsPartnerParam param) {
        HuToolExcelUtils.exportParamToMax(param);
        // 获取数据
        PageUtils<ShopsPartnerVo> pageUtils = this.searchAuthBusinessInfo(param);
        List<ShopsPartnerVo> dataList = pageUtils.getList();

        // 导出Excel
        HuToolExcelUtils.exportData(dataList, "商家信息", (ShopsPartnerVo source) -> {
            ShopsPartnerExcelVo target = new ShopsPartnerExcelVo();
            target.setMainFlag(source.getMainFlag() != null && source.getMainFlag() == 1 ? "是" : "否");
            target.setStatus("0".equals(source.getProhibitStatus()) ? "启用" : "停用");
            target.setCreateTime(source.getCreateTime() != null ? DateUtil.format(source.getCreateTime(), "yyyy-MM-dd HH:mm:ss") : "");
            return target;
        });
    }
}
