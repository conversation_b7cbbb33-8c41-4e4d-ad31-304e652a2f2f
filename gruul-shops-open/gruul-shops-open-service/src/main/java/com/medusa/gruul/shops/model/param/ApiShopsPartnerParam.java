package com.medusa.gruul.shops.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 小程序店铺 param
 * @Date: Created in 11:38 2023/10/8
 */
@ApiModel(value = "ApiShopsPartnerParam 实体", description = "小程序店铺 param")
@Data
public class ApiShopsPartnerParam {

    @ApiModelProperty(value = "商家分类id")
    private String shopsCategoryId;

    @ApiModelProperty(value = "城市code")
    private String cityCode;


    @ApiModelProperty(value = "纬度")
    private Double latitude;


    @ApiModelProperty(value = "经度")
    private Double longitude;


    @ApiModelProperty(value = "店面名称")
    private String name;

    @ApiModelProperty(value = "距离:单位m")
    private Long distance;

    @ApiModelProperty(value = "距离优先：1")
    private Integer sort;



}
