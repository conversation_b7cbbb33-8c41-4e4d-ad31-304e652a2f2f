package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:47 2024/8/26
 */
@Data
@ApiModel(value = "优惠券-商家dto")
public class ShopCouponPartnerDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private Long couponId;

    /**
     * 商家(店铺)id
     */
    @ApiModelProperty(value = "商家(店铺)id")
    private Long shopsPartnerId;
}
