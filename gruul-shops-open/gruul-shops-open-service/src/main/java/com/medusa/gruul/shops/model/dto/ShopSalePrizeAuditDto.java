package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 营销活动抽奖审核参数
 */
@Data
@ApiModel(value = "ShopSalePrizeAuditDto对象", description = "营销活动抽奖审核参数")
public class ShopSalePrizeAuditDto {

    @ApiModelProperty(value = "活动ID", required = true)
    @NotNull(message = "活动ID不能为空")
    private List<Long> ids;

    @ApiModelProperty(value = "审核状态：101->审核通过；200->审核不通过", required = true)
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核意见")
    private String auditReason;
} 