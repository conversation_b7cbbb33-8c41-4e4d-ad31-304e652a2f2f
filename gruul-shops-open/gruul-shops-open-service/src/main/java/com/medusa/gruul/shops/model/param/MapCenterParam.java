package com.medusa.gruul.shops.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 地图中心点请求参数
 */
@Data
@ApiModel(value = "地图中心点请求参数", description = "用于小程序地图展示时传递位置信息")
public class MapCenterParam {

    @ApiModelProperty(value = "当前用户经度 Y")
    private Double longitude;

    @ApiModelProperty(value = "当前用户纬度 X")
    private Double latitude;
} 