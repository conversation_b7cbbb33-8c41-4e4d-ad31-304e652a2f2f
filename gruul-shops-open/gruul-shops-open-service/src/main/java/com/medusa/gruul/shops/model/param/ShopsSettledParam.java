package com.medusa.gruul.shops.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 商家入驻param
 * @Date: Created in 17:18 2023/8/30
 */
@ApiModel(value = "ShopsSettledParam 实体", description = "商家入驻param")
@Data
public class ShopsSettledParam extends QueryParam {

    @ApiModelProperty(value = "商家名字")
    private  String name;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 审批状态 0临时保存  100 待审核 101 审核通过 200 驳回
     */
    @ApiModelProperty(value = "审批状态 0临时保存  100 待审核 101 审核通过 200 驳回")
    private String approvalStatus;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    private String startTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    private String endTime;

}
