package com.medusa.gruul.shops.model.vo;

import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.shops.api.model.AccountCouponVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员营销抽奖验证码记录返回VO
 */
@Data
@ApiModel(value = "核销记录Vo", description = "会员营销抽奖验证码记录返回VO")
public class MiniPrizeCodeVerifyVo extends MiniAccountSalePrizeCodeVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "核销记录ID")
    private Long codeId;

    @ApiModelProperty(value = "奖品图片")
    private String prizeImg;

    @ApiModelProperty(value = "状态:100->未核销;101->已核销;200->已失效")
    private Integer codeStatus;

    @ApiModelProperty(value = "核销时间")
    private LocalDateTime codeCreateTime;;

    @ApiModelProperty(value = "商品信息")
    private ProductVo product;

    @ApiModelProperty(value = "优惠券信息")
    private AccountCouponVo coupon;

} 