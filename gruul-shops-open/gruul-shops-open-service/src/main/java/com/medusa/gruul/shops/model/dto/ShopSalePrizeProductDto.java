package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 营销活动抽奖商品参数
 */
@Data
@ApiModel(value = "ShopSalePrizeProductDto对象", description = "营销活动抽奖商品参数")
public class ShopSalePrizeProductDto {

    @ApiModelProperty(value = "主键ID，新增时为空")
    private Long id;

    @ApiModelProperty(value = "主表ID，新增时为空")
    private Long mainId;

    @ApiModelProperty(value = "商品ID", required = true)
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID", required = true)
    @NotNull(message = "商品规格ID不能为空")
    private Long skuId;

    @ApiModelProperty(value = "商品名称", required = true)
    @NotBlank(message = "商品名称不能为空")
    private String productName;
} 