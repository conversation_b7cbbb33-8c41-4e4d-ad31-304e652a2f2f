package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:34 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减活动操作dto")
public class ShopFullDonationAuditDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过；200->审核不通过；300->失效；400->停止")
    private Integer status;

    @ApiModelProperty(value = "审核意见")
    private String auditReason;

}
