package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 商家入驻dto
 * @Date: Created in 16:34 2023/8/30
 */
@Data
@ApiModel(value = "商家入驻dto")
public class ShopsSettledDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    @NotNull(message = "商家名称不能为空")
    private String shopName;


    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @NotNull(message = "统一社会信用代码不能为空")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @NotNull(message = "联系人不能为空")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String phone;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    @NotNull(message = "省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    @NotNull(message = "市编码不能为空")
    private String cityCode;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @NotNull(message = "区域编码不能为空")
    private String areaCode;


    /**
     *省名
     */
    @ApiModelProperty(value = "省名")
    private String provinceName;

    /**
     * 市名
     */
    @ApiModelProperty(value = "市名")
    private String cityName;

    /**
     * 区域名
     */
    @ApiModelProperty(value = "区域名")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @NotNull(message = "详细地址不能为空")
    private String address;

    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    @NotNull(message = "地图X不能为空")
    private String mapX;

    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    @NotNull(message = "地图Y不能为空")
    private String mapY;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    @NotNull(message = "营业执照不能为空")
    private String businessLicense;

    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    @NotNull(message = "身份证正面不能为空")
    private String cardIdUp;


    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    @NotNull(message = "身份证反面不能为空")
    private String cardIdDown;

    /**
     * 店铺环境
     */
    @ApiModelProperty(value = "店铺环境")
    @NotNull(message = "店铺环境不能为空")
    private String storeEnvironment;
    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    @NotNull(message = "门牌号不能为空")
    private String houseNumber;

    /**
     * 合作模式
     */
    @ApiModelProperty(value = "合作模式")
    private String partnerModel;

    /**
     * 是否继续提交
     */
    @ApiModelProperty(value = "是否继续提交")
    private Boolean isContinue;
}
