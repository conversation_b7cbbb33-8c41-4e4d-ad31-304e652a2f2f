package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员营销抽奖验证码记录返回VO
 */
@Data
@ApiModel(value = "MiniAccountSalePrizeCodeVo对象", description = "会员营销抽奖验证码记录返回VO")
public class MiniAccountSalePrizeCodeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "核销码")
    private String verifyCode;

    @ApiModelProperty(value = "核销二维码")
    private String qrCode;

    @ApiModelProperty(value = "状态:100->未核销;101->已核销;200->已失效")
    private Integer status;

    @ApiModelProperty(value = "核销方式 1-线上邮寄，2-线下核销")
    private Integer verifyType;

    @ApiModelProperty(value = "奖品类型 1-商城商品，2-优惠券")
    private Integer prizeType;


    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖项名称")
    private String levelName;
} 