package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 小程序店铺dto
 * @Date: Created in 19:16 2023/9/13
 */
@Data
@ApiModel(value = "小程序店铺dto")
public class ApiShopsPartnerDto {

    @ApiModelProperty(value = "id",required = true)
    @NotNull
    private Long id;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称",required = true)
    @NotBlank(message = "商家名称不能为空")
    private String name;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人",required = true)
    @NotBlank(message = "联系人不能为空")
    private String contacts;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码",required = true)
    @NotBlank(message = "区域编码不能为空")
    private String areaCode;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码",required = true)
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码",required = true)
    @NotBlank(message = "市编码不能为空")
    private String cityCode;

    /**
     * 省name
     */
    @ApiModelProperty(value = "省name",required = true)
    @NotBlank(message = "省name不能为空")
    private String provinceName;

    /**
     * 市name
     */
    @ApiModelProperty(value = "市name",required = true)
    @NotBlank(message = "市name不能为空")
    private String cityName;

    /**
     * 区域name
     */
    @ApiModelProperty(value = "区域name",required = true)
    @NotBlank(message = "区域name不能为空")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址",required = true)
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话",required = true)
    @NotBlank(message = "电话不能为空")
    private String phone;

    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X",required = true)
    @NotNull(message = "地图X不能为空")
    private Double mapX;

    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y",required = true)
    @NotNull(message = "地图Y不能为空")
    private Double mapY;


    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号",required = true)
    @NotNull(message = "门牌号不能为空")
    private String houseNumber;
}
