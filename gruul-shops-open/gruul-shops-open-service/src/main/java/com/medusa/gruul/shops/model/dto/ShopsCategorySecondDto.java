package com.medusa.gruul.shops.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.goods.api.entity.ShowCategory;
import com.medusa.gruul.shops.api.entity.ShopsCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 商家二级分类dto
 * @Date: Created in 18:38 2023/9/12
 */
@Data
@ApiModel(value = "商家二级分类dto")
public class ShopsCategorySecondDto {

    private Long id;

    @ApiModelProperty(value = "上级分类的编号：0表示一级分类")
    @NotNull
    private Long parentId;

    @ApiModelProperty(value = "分类名称",required = true)
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "分类级别：0->1级；1->2级",required = true)
    @NotNull
    private Integer level;

    @ApiModelProperty(value = "分类排序")
    private Integer sort;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    public ShopsCategory coverShowCategory() {
        ShopsCategory shopsCategory = new ShopsCategory();
        BeanUtil.copyProperties(this, shopsCategory);
        shopsCategory.setLevel(1);
        return shopsCategory;
    }

}
