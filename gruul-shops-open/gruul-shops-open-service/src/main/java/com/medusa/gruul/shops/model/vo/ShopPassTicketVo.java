package com.medusa.gruul.shops.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.shops.api.entity.ShopPassTicketPartner;
import com.medusa.gruul.shops.api.entity.ShopPassTicketProduct;
import com.medusa.gruul.shops.model.dto.ShopPassTicketPartnerDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketProductDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 通惠证
 */
@Data
@ApiModel(value = "通惠证Vo")
public class ShopPassTicketVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 票名
     */
    @ApiModelProperty(value = "票名")
    private String ticketName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    private Integer useableTimes;

    /**
     * 票类型
     */
    @ApiModelProperty(value = "票类型")
    private Integer ticketType;

    /**
     * 满额
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期类型
     */
    @ApiModelProperty(value = "到期类型")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 过期天数
     */
    @ApiModelProperty(value = "过期天数")
    private Integer expiredDays;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;

    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    private Integer approvedStatus;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    private Boolean productFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    private Integer afterDaysValid;

    /**商家明细*/
    @ApiModelProperty(value = "商家明细")
    private List<ShopPassTicketPartner> partnerList;

    /**商品sku明细*/
    @ApiModelProperty(value = "商品sku明细")
    private List<ShopPassTicketProduct> productList;

}
