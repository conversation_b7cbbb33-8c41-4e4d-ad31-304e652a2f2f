package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:59 2025/4/2
 */
@Data
@ApiModel(value = "优惠券指定发券对象Vo")
public class ShopCouponAccountVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    @ApiModelProperty(value = "发券对象id")
    private String sourceId;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "客户数量")
    private Integer accountNum;


    @ApiModelProperty(value = "客户昵称")
    private String nikeName;

    @ApiModelProperty(value = "客户电话号码")
    private String phone;

    @ApiModelProperty(value = "客户会员等级")
    private String memberLevel;





}
