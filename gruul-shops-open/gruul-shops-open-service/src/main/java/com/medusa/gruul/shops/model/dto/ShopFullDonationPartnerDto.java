package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:59 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减商家dto")
public class ShopFullDonationPartnerDto {


    @ApiModelProperty(value = "商家ID")
    private Long shopsPartnerId;

}
