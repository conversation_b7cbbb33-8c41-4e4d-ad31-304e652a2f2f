package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.shops.api.entity.ShopsSettled;
import com.medusa.gruul.shops.model.param.ShopsSettledParam;
import com.medusa.gruul.shops.model.vo.ShopsSettledVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: 商家入驻Mapper类
 * @Date: Created in 16:39 2023/8/30
 */
@Repository
public interface ShopsSettledMapper extends BaseMapper<ShopsSettled> {

    /**
     * 查询商家入驻列表
     * @param myTeamMiniAccountVoPage
     * @param shopsSettledParam
     * @return
     */
    IPage<ShopsSettledVo> getShopsSettledVo(Page<ShopsSettledVo> myTeamMiniAccountVoPage, @Param("paramMap")ShopsSettledParam shopsSettledParam);

    /**
     * 根据shopUserId查询商家入驻信息
     * @param shopUserId
     * @return
     */
    List<ShopsSettledVo> getShopsSettledVoByShopUserId(@Param("shopUserId")String shopUserId);
}
