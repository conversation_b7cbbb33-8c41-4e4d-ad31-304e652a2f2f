package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家信息Excel导出VO
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ApiModel(value = "ShopsPartnerExcelVo", description = "商家信息Excel导出VO")
public class ShopsPartnerExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /*    *//**
     * 序号
     *//*
    @ApiModelProperty(value = "序号")
    private Integer index;*/

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String name;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String phone;

    /**
     * 所属分类
     */
    @ApiModelProperty(value = "所属分类")
    private String categoryName;

    /**
     * 加入时间
     */
    @ApiModelProperty(value = "加入时间")
    private String createTime;

    /**
     * 主店铺
     */
    @ApiModelProperty(value = "主店铺")
    private String mainFlag;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
}
