package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 通惠证
 */
@Data
@ApiModel(value = "通惠证dto")
public class ShopPassTicketDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Size(max = 128, message = "名称最大长度为128位")
    @ApiModelProperty(value = "名称")
    private String ticketName;

    /**
     * 购买单价
     */
    @ApiModelProperty(value = "购买单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    @NotNull(message = "每个店铺使用次数不能为空")
    private Integer useableTimes;

    /**
     * 票类型
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    private Integer ticketType;

    /**
     * 满额
     */
    @ApiModelProperty(value = "满额")
    @NotNull(message = "满额不能为空")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣
     */
    @ApiModelProperty(value = "减额或者折扣")
    @NotNull(message = "减额或者折扣不能为空")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 到期类型
     */
    @ApiModelProperty(value = "到期类型:100->指定时间;101->购买之日起计算;")
    @NotNull(message = "到期类型不能为空")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 过期天数
     */
    @ApiModelProperty(value = "过期天数")
    private Integer expiredDays;


    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    private Boolean productFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    @TableField("total_num")
    @NotNull(message = "发行量不能为空")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    @TableField("after_days_valid")
    private Integer afterDaysValid;

    /**商家明细*/
    @ApiModelProperty(value = "商家明细")
    private List<ShopPassTicketPartnerDto> partnerList;

    /**商品sku明细*/
    @ApiModelProperty(value = "商品sku明细")
    private List<ShopPassTicketProductDto> productList;
}
