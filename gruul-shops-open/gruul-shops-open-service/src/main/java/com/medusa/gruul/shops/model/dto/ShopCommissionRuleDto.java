package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 佣金规则
 * <AUTHOR>
 */
@Data
@ApiModel(value = "佣金规则dto")
public class ShopCommissionRuleDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 是否开启，0否1是
     */
    @ApiModelProperty(value = "是否开启，0否1是")
    private Integer openFlag;

    /**
     * 规则类型，100：固定金额，101：百分比
     */
    @ApiModelProperty(value = "规则类型，100：固定金额，101：百分比，102：以单个商品，103：根据会员等级")
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;

    /**
     * 上级金额或者百分比
     */
    @ApiModelProperty(value = "上级金额或者百分比")
    @NotNull(message = "上级数据不能为空")
    private BigDecimal parentReceive;

    /**
     * 上上级金额或者百分比
     */
    @ApiModelProperty(value = "上上级金额或者百分比")
    @NotNull(message = "上上级数据不能为空")
    private BigDecimal aboveParentReceive;

    /**
     * 最小提现金额
     */
    @ApiModelProperty(value = "最小提现金额")
    @NotNull(message = "最小提现金额不能为空")
    private BigDecimal minCashAmount;

    /**
     * 最大提现金额
     */
    @ApiModelProperty(value = "最大提现金额")
    @NotNull(message = "最大提现金额不能为空")
    private BigDecimal maxCashAmount;

    /**
     * 提现手续费率%
     */
    @ApiModelProperty(value = "提现手续费率%")
    @NotNull(message = "提现手续费率不能为空")
    private BigDecimal cashRate;

    /**
     * 每天提现次数
     */
    @ApiModelProperty(value = "每天提现次数")
    @NotNull(message = "每天提现次数不能为空")
    private Integer cashTimes;

    @ApiModelProperty(value = "最低消费")
    private BigDecimal minPayAmount;

    /**
     * 佣金划转比例
     */
    @ApiModelProperty(value = "佣金划转比例")
    private BigDecimal commissionTransferRate;

}
