package com.medusa.gruul.shops.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运营中心地图数据VO
 */
@Data
@ApiModel(value = "运营中心地图数据VO", description = "用于小程序地图展示运营中心")
public class ShopsPartnerMapVo {

    @ApiModelProperty(value = "商家ID")
    private Long id;

    @ApiModelProperty(value = "商家名称")
    private String name;

    @ApiModelProperty(value = "合作模式  0加盟 1子公司 2运营中心")
    private String partnerModel;

    @ApiModelProperty(value = "纬度")
    private Double mapX;

    @ApiModelProperty(value = "经度")
    private Double mapY;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "商家logo")
    private String logo;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "距离（米）")
    private Double distance;
} 