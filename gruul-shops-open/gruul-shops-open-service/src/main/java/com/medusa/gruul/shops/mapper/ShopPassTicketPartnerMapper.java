package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.entity.ShopPassTicketPartner;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/08/21
 */
@Repository
public interface ShopPassTicketPartnerMapper extends BaseMapper<ShopPassTicketPartner> {



}
