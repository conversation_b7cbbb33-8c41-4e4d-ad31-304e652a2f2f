package com.medusa.gruul.shops.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 营销活动抽奖参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShopSalePrizeDto对象", description = "营销活动抽奖参数")
public class ShopSalePrizeDto {

    @ApiModelProperty(value = "主键ID，新增时为空")
    private Long id;

    @ApiModelProperty(value = "商铺ID")
    private String shopId;

    @ApiModelProperty(value = "活动名称", required = true)
    @NotBlank(message = "活动名称不能为空")
    private String name;

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态")
    private Integer status;

    @ApiModelProperty(value = "每人参与次数", required = true)
    @NotNull(message = "每人参与次数不能为空")
    private Integer perCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "活动背景图URL", required = true)
    @NotBlank(message = "活动背景图URL不能为空")
    private String backImageUrl;

    @ApiModelProperty(value = "参与会员等级，多个会员等级之间用英文逗号分隔")
    private String memberLevelIds;

    @ApiModelProperty(value = "指定参与商品：0-否，1-是", required = true)
    @NotNull(message = "指定参与商品不能为空")
    private Integer productFlag;

    @ApiModelProperty(value = "订单满额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "直推会员数")
    private Integer directMemberQty;

    @ApiModelProperty(value = "直推最低会员等级ID")
    private Long directLowMemberLevelId;

    @ApiModelProperty(value = "活动类型：1-大转盘，2-九宫格盲盒", required = true)
    @NotNull(message = "活动类型不能为空")
    private Integer prizeType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @ApiModelProperty(value = "单据日期")
    private LocalDate billDate;

    @ApiModelProperty(value = "抽奖规则")
    private String rule;

    @ApiModelProperty(value = "参与商品列表，当productFlag=1时必填")
    private List<ShopSalePrizeProductDto> productList;

    @ApiModelProperty(value = "奖项列表", required = true)
    @NotEmpty(message = "奖项列表不能为空")
    @Valid
    private List<ShopSalePrizeItemDto> prizeItemList;
} 