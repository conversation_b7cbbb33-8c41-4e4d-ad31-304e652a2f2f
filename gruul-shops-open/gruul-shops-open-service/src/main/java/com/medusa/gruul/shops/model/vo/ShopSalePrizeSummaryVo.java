package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 营销活动抽奖汇总表VO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShopSalePrizeSummaryVo对象", description = "营销活动抽奖汇总表VO")
public class ShopSalePrizeSummaryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动ID")
    private Long mainPrizeId;

    @ApiModelProperty(value = "奖项ID")
    private Long prizeItemId;

    @ApiModelProperty(value = "活动方案名称")
    private String mainPrizeName;

    @ApiModelProperty(value = "核销类型,1-线上邮寄，2-线下核销")
    private Integer verifyType;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖项名称")
    private String levelName;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "中奖次数")
    private Integer winningCount;

    @ApiModelProperty(value = "中奖人数")
    private Integer winningPersonCount;

    @ApiModelProperty(value = "总抽奖人数")
    private Integer totalParticipantCount;

    @ApiModelProperty(value = "核销人数(仅线下核销)")
    private Integer verifyPersonCount;


}
