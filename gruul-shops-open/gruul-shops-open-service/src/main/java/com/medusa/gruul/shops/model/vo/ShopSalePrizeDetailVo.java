package com.medusa.gruul.shops.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 营销活动抽奖详情返回VO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShopSalePrizeDetailVo对象", description = "营销活动抽奖详情返回VO")
public class ShopSalePrizeDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "每人参与次数")
    private Integer perCount;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "活动背景图URL")
    private String backImageUrl;

    @ApiModelProperty(value = "参与会员等级，多个会员等级之间用英文逗号分隔")
    private String memberLevelIds;

    @ApiModelProperty(value = "指定参与商品：0-否，1-是")
    private Integer productFlag;

    @ApiModelProperty(value = "订单满额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "直推会员数")
    private Integer directMemberQty;

    @ApiModelProperty(value = "直推最低会员等级ID")
    private Long directLowMemberLevelId;

    @ApiModelProperty(value = "活动类型：1-大转盘，2-九宫格盲盒")
    private Integer prizeType;

    @ApiModelProperty(value = "状态：0->草稿；100->待审核；101 未生效 104->已生效；300->失效；400->停止")
    private Integer status;
    @ApiModelProperty(value = "审核状态：100->待审核；101->审核通过；200->审核不通过；")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核人ID")
    private Long auditPlatformUserId;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核意见")
    private String auditReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @ApiModelProperty(value = "单据日期")
    private LocalDate billDate;

    @ApiModelProperty(value = "抽奖规则")
    private String rule;

    @ApiModelProperty(value = "制单人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "指定参与商铺")
    private List<ShopSalePrizeProductVo> productList;

    @ApiModelProperty(value = "奖项列表")
    private List<ShopSalePrizeItemVo> prizeItemList;
} 