package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.entity.ShopPassTicketRule;
import com.medusa.gruul.shops.api.model.TicketVo;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/09/20
 */
@Repository
public interface ShopPassTicketRuleMapper extends BaseMapper<ShopPassTicketRule> {

}
