package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.shops.api.entity.ShopCouponCategory;
import com.medusa.gruul.shops.api.entity.ShopCouponProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:41 2024/8/26
 */
@Data
@ApiModel(value = "优惠证证dto")
public class ShopCouponDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 优惠券名称
     */
    @NotBlank(message = "优惠券名称不能为空")
    @Size(max = 100, message = "优惠券名称最大长度为100位")
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券面额
     */
    @ApiModelProperty(value = "优惠券面额")
    @NotNull(message = "优惠券面额不能为空")
    private BigDecimal promotion;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "显示开始时间不能为空")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "显示结束时间不能为空")
    private Date displayEndTime;


    /**
     * 订单满xx元可使用
     */
    @ApiModelProperty(value = "订单满xx元可使用")
    @NotNull(message = "订单满xx元可使用不能为空")
    private BigDecimal fullAmount;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    @NotNull(message = "有效期开始时间不能为空")
    private Date startTime;

    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    @NotNull(message = "有效期结束时间不能为空")
    private Date endTime;

    /**
     * 领取次数
     */
    @ApiModelProperty(value = "领取次数")
    @NotNull(message = "领取次数不能为空")
    private Integer receiveTimes;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券")
    @NotNull(message = "优惠券类型不能为空")
    private Integer couponType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**商家明细*/
    @ApiModelProperty(value = "商家明细")
    private List<ShopCouponPartnerDto> partnerList;

    /**
     * 商品明细
     */
    @ApiModelProperty(value = "商品明细")
    private List<ShopCouponProductDto>products;

    /**
     * 品类明细
     */
    @ApiModelProperty(value = "品类明细")
    private List<ShopCouponCategoryDto>categorys;


    /**
     * 指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;
     */
    @ApiModelProperty(value = "指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;")
    private Integer grantType;

    /**
     * 是否弹窗，0否1是
     */
    @ApiModelProperty(value = "是否弹窗，0否1是")
    private Boolean isAlert;

    /**
     * 指定发券对象明细
     */
    @ApiModelProperty(value = "指定发券对象明细")
    private List<ShopCouponAccountDto>accounts;
}
