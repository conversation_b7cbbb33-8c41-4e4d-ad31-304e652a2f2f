package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: 店铺更新dto
 * @Date: Created in 16:23 2023/9/13
 */
@Data
@ApiModel(value = "店铺更新dto")
public class ShopsPartnerUpdateDto {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;


    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    private String cardIdUp;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    private String cardIdDown;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 商家分类id
     */
    @ApiModelProperty(value = "商家分类id")
    private List<Long> shopsCategoryIds;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 省name
     */
    @ApiModelProperty(value = "省name")
    private String provinceName;

    /**
     * 市name
     */
    @ApiModelProperty(value = "市name")
    private String cityName;

    /**
     * 区域name
     */
    @ApiModelProperty(value = "区域name")
    private String areaName;

    /**
     * 店铺logo
     */
    @ApiModelProperty(value = "店铺logo")
    private String logo;
}
