package com.medusa.gruul.shops.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 营销活动抽奖查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopSalePrizeQueryParam对象", description = "营销活动抽奖查询参数")
public class ShopSalePrizeQueryParam extends QueryParam {

    @ApiModelProperty(value = "状态：0->草稿；104->已生效;300->失效；400->停止")
    private Integer status;

    @ApiModelProperty(value = "100->待审核；101->审核通过；200->审核不通过")
    private Integer auditStatus;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "制单人名称")
    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "活动开始时间范围-开始")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "活动开始时间范围-结束")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "活动类型：1-大转盘，2-九宫格盲盒")
    private Integer prizeType;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @ApiModelProperty(value = "单据日期范围-开始")
    private LocalDate billDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @ApiModelProperty(value = "单据日期范围-结束")
    private LocalDate billDateEnd;

    @ApiModelProperty(value = "排序类型")
    private String sortType;

    @ApiModelProperty(value = "排序方向：asc, desc")
    private String sortOrder;
}