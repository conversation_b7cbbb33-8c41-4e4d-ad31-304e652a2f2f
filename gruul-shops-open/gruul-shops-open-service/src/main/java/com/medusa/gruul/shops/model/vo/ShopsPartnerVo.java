package com.medusa.gruul.shops.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR> by zq
 * @date created in 2019/11/15
 */
@Data
@ApiModel(value = "shops partner vo 实体", description = "商铺数据 vo")
public class ShopsPartnerVo implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 总店Id
     */
    @ApiModelProperty(value = "总店Id")
    private Long partnerId;


    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;




    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    private String name;


    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String pass;


    /**
     *  地域
     */
    @ApiModelProperty(value = "地域")
    private String region;


    /**
     *  区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;


    /**
     *  省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;


    /**
     *  市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;


    /**
     *  身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    private String cardIdUp;


    /**
     *  身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    private String cardIdDown;


    /**
     *  地图X
     */
    @ApiModelProperty(value = "地图X")
    private Double mapX;


    /**
     *  地图Y
     */
    @ApiModelProperty(value = "地图Y")
    private Double mapY;


    /**
     *  Modify模式  0加盟 1子公司
     */
    @ApiModelProperty(value = "Modify模式  0加盟 1子公司")
    private String partnerModel;


    /**
     * 审批状态 0审核中 1通过 2拒绝
     */
    @ApiModelProperty(value = "审批状态 0审核中 1通过 2拒绝")
    private String approvalStatus;


    /**
     * 禁用状态  0正常  1 禁用
     */
    @ApiModelProperty(value = "禁用状态  0正常  1 禁用")
    private String prohibitStatus;


    /**
     *  省name
     */
    @ApiModelProperty(value = "省name")
    private String provinceName;


    /**
     *  市name
     */
    @ApiModelProperty(value = "市name")
    private String cityName;


    /**
     *  区域name
     */
    @ApiModelProperty(value = "区域name")
    private String areaName;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 平台用户id
     */
    @ApiModelProperty(value = "平台用户id")
    private Long platformId;

    @ApiModelProperty(value = "营业开始时间")
    @JsonFormat(pattern = "HH:mm")
    private Date businessBeginHours;

    @ApiModelProperty(value = "营业结束时间")
    @JsonFormat(pattern = "HH:mm")
    private Date businessEndHours;

    /**
     * 店铺logo
     */
    @ApiModelProperty(value = "店铺logo")
    private String logo;


    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String categoryName;
    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    /**
     * 营业日期用,隔开
     */
    @ApiModelProperty(value = "营业日期用,隔开")
    private String tradeDate;


    /**
     * 主账户数
     */
    @ApiModelProperty(value = "主账户数")
    private Integer accountMainNum;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    /**
     *商家分类id
     */
    @ApiModelProperty(value = "商家分类id")
    private List<Long> shopsCategoryIds;

    /**
     *商家分类名称
     */
    @ApiModelProperty(value = "商家分类名称")
    private String shopsCategoryName;

    /**
     * 格式化的营业日期，用于前端展示
     */
    @ApiModelProperty(value = "格式化的营业日期，用于前端展示")
    private String formatTradeDate;

    /**
     * 可用通惠证名称
     */
    @ApiModelProperty(value = "可用通惠证名称")
    private String usableShopTicketName;

    /**
     * 营业状态：0.营业；1.歇业
     */
    @ApiModelProperty(value = "营业状态：0.营业；1.歇业")
    private Integer tradeStatus;

    /**
     * 是否为主店铺：1.是
     */
    @ApiModelProperty(value = "是否为主店铺：1.是")
    private Integer mainFlag;
}
