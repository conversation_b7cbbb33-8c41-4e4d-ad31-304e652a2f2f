package com.medusa.gruul.shops.model.param;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AccountSalePrizeParam对象", description = "活动抽奖记录查询参数")
public class AccountSalePrizeParam extends QueryParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "是否中奖：0->否；1->是")
    private Integer prizeFlag;

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "奖项名称")
    private String levelName;

    @ApiModelProperty(value = "活动名称")
    private String mainPrizeName;

    @ApiModelProperty(value = "奖品发放方式：1->线上邮寄; 2->线下核销")
    private Integer verifyType;

    @ApiModelProperty(value = "奖品类型：1->商城商品；2->优惠券")
    private Integer prizeType;

    @ApiModelProperty(value = "状态：0->未发放；1->已发放")
    private Integer status;

    @ApiModelProperty(value = "shopId")
    private String shopId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "创建开始时间 - 也为中奖时间")
    private LocalDateTime createStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "创建结束时间")
    private LocalDateTime createEndTime;


    private String userName;

    private String userPhone;

    private String productName;
    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "核销人名称")
    private String verifyNickName;

}
