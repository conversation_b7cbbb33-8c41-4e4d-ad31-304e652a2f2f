package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.shops.api.entity.ShopSalePrizeProduct;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeProductVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 营销活动抽奖商品表 Mapper 接口
 * <AUTHOR>
 */
public interface ShopSalePrizeProductMapper extends BaseMapper<ShopSalePrizeProduct> {

    /**
     * 根据主表ID查询商品列表
     *
     * @param mainId 主表ID
     * @return 商品列表
     */
    List<ShopSalePrizeProductVo> getListByMainId(@Param("mainId") Long mainId);
} 