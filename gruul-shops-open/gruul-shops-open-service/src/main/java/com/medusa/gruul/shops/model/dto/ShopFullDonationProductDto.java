package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:00 2025/7/4
 */
@Data
@ApiModel(value = "满赠满减商品dto")
public class ShopFullDonationProductDto {


    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    private String productName;


}
