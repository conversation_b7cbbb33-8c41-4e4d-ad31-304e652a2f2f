package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author: plh
 * @Date: Created in 17:18 2023/8/17
 */
@Data
@ApiModel(value = "店铺dto")
public class ShopsPartnerDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    /**
     * 电话
     */
    @NotBlank(message = "电话不能为空")
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    @NotBlank(message = "名字不能为空")
    private String name;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String pass;

    /**
     * 邀请码
     */
    @ApiModelProperty(value = "邀请码")
    private String invitationCode;

    /**
     * 地域
     */
    @ApiModelProperty(value = "地域")
    @NotBlank(message = "地域不能为空")
    private String region;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @NotBlank(message = "区域编码不能为空")
    private String areaCode;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    @NotBlank(message = "市编码不能为空")
    private String cityCode;

    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    @NotBlank(message = "身份证正面不能为空")
    private String cardIdUp;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    @NotBlank(message = "身份证反面不能为空")
    private String cardIdDown;

    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    @NotNull(message = "地图X不能为空")
    private Double mapX;

    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    @NotNull(message = "地图Y不能为空")
    private Double mapY;

    /**
     * Modify模式  0加盟 1子公司
     */
    @ApiModelProperty(value = "Modify模式  0加盟 1子公司")
    @NotBlank(message = "Modify模式不能为空")
    private String partnerModel;


    /**
     * 省name
     */
    @ApiModelProperty(value = "省name")
    @NotBlank(message = "省name不能为空")
    private String provinceName;

    /**
     * 市name
     */
    @ApiModelProperty(value = "市name")
    @NotBlank(message = "市name不能为空")
    private String cityName;

    /**
     * 区域name
     */
    @ApiModelProperty(value = "区域name")
    @NotBlank(message = "区域name不能为空")
    private String areaName;

    /**
     * 营业开始时间
     */
    @ApiModelProperty(value = "营业开始时间 HH:mm:ss")
    @NotNull(message = "营业开始时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date businessBeginHours;

    /**
     * 营业结束时间
     */
    @ApiModelProperty(value = "营业结束时间 HH:mm:ss")
    @NotNull(message = "营业结束时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date businessEndHours;

    /**
     * 店铺logo
     */
    @ApiModelProperty(value = "店铺logo")
    @NotBlank(message = "店铺logo不能为空")
    private String logo;


    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String address;


    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 商家入驻表id
     */
    @ApiModelProperty(value = "商家入驻表id")
    private Long shopsSettledId;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    /**
     * 营业日期用,隔开
     */
    @ApiModelProperty(value = "营业日期用,隔开")
    private String tradeDate;
    /**
     * 会员申请人店铺ID
     */
    @ApiModelProperty(value = "会员申请人店铺ID")
    @TableField("shop_user_id")
    private String shopUserId;

}
