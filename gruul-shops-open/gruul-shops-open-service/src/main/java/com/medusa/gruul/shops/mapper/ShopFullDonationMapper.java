package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shops.api.entity.ShopFullDonation;
import com.medusa.gruul.shops.api.model.ShopFullDonationParam;
import com.medusa.gruul.shops.api.model.ShopFullDonationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:30 2025/7/4
 */
@Mapper
public interface ShopFullDonationMapper extends BaseMapper<ShopFullDonation> {

    /**
     * 分页查询满减满赠活动
     * @param page
     * @param param
     * @return
     */
    IPage<ShopFullDonationVo> getShopFullDonation(IPage page, @Param("params")ShopFullDonationParam param);

    /**
     * 根据id查询满减满赠活动
     * @param id
     * @return
     */
    ShopFullDonationVo getShopFullDonationById(@Param("id") Long id);

    /**
     * 查询有效的满减满赠活动
     * @param param
     * @return
     */
    List<ShopFullDonationVo> getValidShopFullDonation(@Param("params")ShopFullDonationParam param);

}
