package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;
import com.medusa.gruul.shops.api.model.TicketVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/08/21
 */
@Repository
public interface ShopPassTicketMapper extends BaseMapper<ShopPassTicket> {

    /**
     * 分页查询通票记录
     * @param page
     * @param shopPassTicketParam
     * @return
     */
    IPage<ShopPassTicket> queryList(IPage page , @Param("shopPassTicketParam") ShopPassTicketParam shopPassTicketParam);

    /**
     * 分页查询显示的通票记录
     * @param page
     * @param shopPassTicketParam
     * @return
     */
    IPage<ShopPassTicket> selectDisplayList(IPage page , @Param("shopPassTicketParam") ShopPassTicketParam shopPassTicketParam);
    /**
     * 获取单个sku关联商品
     *
     * @param ticketIds
     * @return ItemVo
     */
    List<TicketVo> queryTicketItemVoByIds(@Param("ticketIds") List<Long> ticketIds);

    /**
     * 查看当前是否有所有商户参与的通惠证
     * @return
     */
    Integer countShopFlag();

    /**
     * 通过店铺id查看当前是否有可使用通惠证
     * @param shopsPartnerId
     * @return
     */
    Integer countShopFlagByShopsPartnerId(Long shopsPartnerId);
}
