package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 营销活动抽奖明细详情返回VO（包含活动名称）
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopSalePrizeItemDetailVo对象", description = "营销活动抽奖明细详情返回VO")
public class ShopSalePrizeItemDetailVo extends ShopSalePrizeItemVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动名称")
    private String mainPrizeName;

    @ApiModelProperty(value = "活动类型")
    private Integer mainPrizeType;
    /**
     * 主表制单人
     */
    @ApiModelProperty(value = "制单人")
    private String createUserName;

    @ApiModelProperty(value = "会员等级")
    private String memberLevelIds;



    @ApiModelProperty(value = "参与商品")
    private String participateInTheProduct;
}
