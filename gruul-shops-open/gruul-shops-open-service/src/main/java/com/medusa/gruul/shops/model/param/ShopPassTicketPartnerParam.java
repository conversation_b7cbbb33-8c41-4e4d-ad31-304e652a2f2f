package com.medusa.gruul.shops.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通行票-商家查询实体类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopPassTicketPartnerParam 实体", description = "通行票-商家 param")
@Data
public class ShopPassTicketPartnerParam extends QueryParam {

    /**
     * 通行票id
     */
    @ApiModelProperty(value = "通行票id")
    private Long passTicketId;

}
