package com.medusa.gruul.shops.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:45 2024/8/23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopCouponParam 实体", description = "优惠券 param")
@Data
public class ShopCouponParam extends QueryParam {

    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;
    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;
    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    private Date displayStartTime;
    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    private Date displayEndTime;

    @ApiModelProperty(value = "生效开始时间")
    private Date startTimeBegin;

    @ApiModelProperty(value = "生效结束时间")
    private Date startTimeEnd;
    /**
     * 新人券，0否1是
     */
    @ApiModelProperty(value = "新人券，0否1是")
    private Integer newPeopleFlag;

    /**
     * 领取次数
     */
    @ApiModelProperty(value = "领取次数")
    private Integer receiveTimes;

    /**
     * 有效期天数
     */
    @ApiModelProperty(value = "有效期天数")
    private Integer useDate;
    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券")
    private Integer couponType;

    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券；4.满返券")
    private Integer showCouponType;
    /**
     * 审核状态:100->待审核;101->审核通过;200->驳回
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    private Integer approvedStatus;

    /**
     * 能显示优惠券的状态值:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     * 100和101 能显示出来
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private List<Integer> displayStatus;

    /**
     * 显示日期
     */
    @ApiModelProperty(value = "显示日期")
    private String displayDate;
}
