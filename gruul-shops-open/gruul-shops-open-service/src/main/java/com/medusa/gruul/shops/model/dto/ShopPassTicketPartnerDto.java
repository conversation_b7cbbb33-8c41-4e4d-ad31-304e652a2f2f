package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 通行票-商家
 */
@Data
@ApiModel(value = "通行票-商家dto")
public class ShopPassTicketPartnerDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 通行票id
     */
    @ApiModelProperty(value = "通行票id")
    private Long passTicketId;

    /**
     * 商家(店铺)id
     */
    @ApiModelProperty(value = "商家(店铺)id")
    private Long shopsPartnerId;


}
