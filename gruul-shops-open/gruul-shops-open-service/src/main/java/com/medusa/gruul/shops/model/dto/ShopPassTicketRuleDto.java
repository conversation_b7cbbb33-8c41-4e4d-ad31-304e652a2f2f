package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 通惠证规则
 */
@Data
@ApiModel(value = "通惠证规则dto")
public class ShopPassTicketRuleDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    @NotNull(message = "规则不能为空")
    private String rule;

}
