package com.medusa.gruul.shops.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: plh
 * @Date: Created in 17:03 2023/8/17
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ShopsPartnerParam 实体", description = "店铺 param")
@Data
public class ShopsPartnerParam extends QueryParam {

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    private String name;


    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 加入开始时间
     */
    @ApiModelProperty(value = "加入开始时间")
    private String startTime;

    /**
     * 加入结束时间
     */
    @ApiModelProperty(value = "加入结束时间")
    private String endTime;

    /**
     * 商家分类id
     */
    @ApiModelProperty(value = "商家分类id")
    private Long shopsCategoryId;

    /**
     * 禁用状态  0正常  1 禁用
     */
    @ApiModelProperty(value = "禁用状态  0正常  1 禁用")
    private String prohibitStatus;

    /**
     * 允许平台查看的店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private List<String> shopIds;
}
