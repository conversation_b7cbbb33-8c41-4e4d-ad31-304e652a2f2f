package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:09 2025/4/2
 */
@Data
@ApiModel(value = "优惠券-品类dto")
public class ShopCouponCategoryDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    /**
     * 品类id
     */
    @ApiModelProperty(value = "品类id")
    private String categoryId;

}
