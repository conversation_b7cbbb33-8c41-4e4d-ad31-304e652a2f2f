package com.medusa.gruul.shops.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 小程序店铺Logo dto
 * @Date: Created in 19:49 2023/9/13
 */
@Data
@ApiModel(value = "小程序店铺Logo dto")
public class ApiShopsPartnerLogoDto {

    @ApiModelProperty(value = "id",required = true)
    @NotNull
    private Long id;

    /**
     * Logo
     */
    @ApiModelProperty(value = "Logo",required = true)
    @NotNull
    private String Logo;

}
