<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.ShopSalePrizeProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.shops.api.entity.ShopSalePrizeProduct">
        <id column="id" property="id" />
        <result column="main_id" property="mainId" />
        <result column="product_id" property="productId" />
        <result column="sku_id" property="skuId" />
        <result column="product_name" property="productName" />
        <result column="shop_id" property="shopId" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user_id" property="createUserId"  />
        <result column="create_user_name" property="createUserName"  />
        <result column="create_time" property="createTime"  />
        <result column="last_modify_user_id" property="lastModifyUserId"  />
        <result column="last_modify_user_name" property="lastModifyUserName"  />
        <result column="update_time" property="updateTime"  />
        <result column="is_deleted" property="deleted"  />
    </resultMap>

    <!-- 根据主表ID查询商品列表结果映射 -->
    <resultMap id="ProductVoResultMap" type="com.medusa.gruul.shops.model.vo.ShopSalePrizeProductVo">
        <id column="id" property="id" />
        <result column="main_id" property="mainId" />
        <result column="product_id" property="productId" />
        <result column="sku_id" property="skuId" />
        <result column="product_name" property="productName" />
    </resultMap>


    <select id="getListByMainId" resultMap="ProductVoResultMap">
        SELECT
            id, main_id, product_id, sku_id, product_name
        FROM
            t_shop_sale_prize_product
        WHERE
            main_id = #{mainId}
            AND is_deleted = 0
        ORDER BY id Desc
    </select>
</mapper> 