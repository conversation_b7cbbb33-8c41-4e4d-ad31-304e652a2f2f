
DROP TABLE IF EXISTS `t_mini_account_pass_ticket`;
CREATE TABLE `t_mini_account_pass_ticket`  (
  `id` bigint NOT NULL COMMENT 'id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态：0->未删除；1->已删除',
	`create_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
   `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
   `last_modify_user_id` bigint NULL DEFAULT NULL COMMENT '最近一次修改人id',
   `last_modify_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近一次修改人姓名',
	 `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
	 `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
	 `pass_ticket_id` bigint NULL DEFAULT NULL COMMENT '通行票id',
	 `status` int NULL DEFAULT NULL COMMENT '状态:100->未用;101->已用;200->已失效',
	 `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
	 `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员通行票记录表' ROW_FORMAT = DYNAMIC;