SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `t_shop_full_donation_rule`;
CREATE TABLE `t_shop_full_donation_rule`  (
  `id` bigint NOT NULL COMMENT 'id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态：0->未删除；1->已删除',
  `create_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `last_modify_user_id` bigint NULL DEFAULT NULL COMMENT '最近一次修改人id',
  `last_modify_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近一次修改人姓名',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
  `shop_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
	`main_id` BIGINT NULL DEFAULT NULL COMMENT '主表ID',
	`full_type` INT NULL DEFAULT NULL COMMENT '满额类型:1-金额，2-件数，3-件数金额',
	`piece_num` INT NULL DEFAULT NULL COMMENT '满件',
	`order_amount` decimal(16,2) NULL DEFAULT NULL COMMENT '订单满额',
	`buy_type` TINYINT NULL DEFAULT NULL COMMENT '购买订单类型',
	`reduction_amount` decimal(16,2) NULL DEFAULT NULL COMMENT '满减金额',
	`coupon_ids` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '优惠券ID',	
	`sku_ids` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '赠送商品',
	`self_num` INT NULL DEFAULT NULL COMMENT '满赠自身件数',
	`show_title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则展示标题',	
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '满赠满减规则' ROW_FORMAT = DYNAMIC;
SET FOREIGN_KEY_CHECKS = 1;
