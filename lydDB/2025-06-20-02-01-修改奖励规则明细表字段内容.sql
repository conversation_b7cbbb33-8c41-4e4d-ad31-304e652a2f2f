ALTER TABLE t_reward_scheme_det MODIFY member_level_id VARCHAR(1024) COMMENT '会员等级ID';

ALTER TABLE t_reward_scheme_det add up_member_level_id VARCHAR(1024) COMMENT '条件升级等级ID';

ALTER TABLE t_reward_scheme_det add direct_member_count int COMMENT '条件直推指标';

ALTER TABLE t_reward_scheme_det add differ_cal_type TINYINT COMMENT '级差计算方式:1-订单总额，2-分佣率';

ALTER TABLE t_reward_scheme_det add commission_title VARCHAR(64) COMMENT '分佣标题';

ALTER TABLE t_reward_scheme_det drop COLUMN same_level_flag;

ALTER TABLE t_reward_scheme_det drop COLUMN differ_flag;

ALTER TABLE t_reward_scheme_det MODIFY one_commission_rate VARCHAR(64) COMMENT '一级下级比例';

ALTER TABLE t_reward_scheme_det MODIFY two_commission_rate VARCHAR(64) COMMENT '二级下级比例';

ALTER TABLE t_reward_scheme_det MODIFY one_commission_amount VARCHAR(64) COMMENT '一级下级固定金额';

ALTER TABLE t_reward_scheme_det MODIFY two_commission_amount VARCHAR(64) COMMENT '二级下级固定金额';

ALTER TABLE t_reward_scheme_det add self_commission TINYINT COMMENT '自身下单分佣:0-否，1-是';

ALTER TABLE t_reward_scheme_det add commission_amount_source TINYINT COMMENT '分佣金额来源:1-订单实际支付金额，2-佣金金额，3-订单金额';

ALTER TABLE t_reward_scheme_det MODIFY salesman_flag tinyint COMMENT '业务员分佣:0-否，1-是';

