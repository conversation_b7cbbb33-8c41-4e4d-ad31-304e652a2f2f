package com.medusa.gruul.api.model.dto;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
;


/**
 * @Description: 表字段信息
 * @Author: qsx
 * @Date:   2022-03-18
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改onl_cgform_fieldDTO")
public class OnlCgformFieldDto extends QueryParam {
    
	/**主键ID*/
    @ApiModelProperty(value = "主键ID")
	private String id;
	/**表ID*/
    @ApiModelProperty(value = "表ID")
	private String cgformHeadId;
	/**字段名字*/
    @ApiModelProperty(value = "字段名字")
	private String dbFieldName;
	/**字段备注*/
    @ApiModelProperty(value = "字段备注")
	private String dbFieldTxt;
	/**原字段名*/
    @ApiModelProperty(value = "原字段名")
	private String dbFieldNameOld;
	/**是否主键 0否 1是*/
    @ApiModelProperty(value = "是否主键 0否 1是")
	private Integer dbIsKey;
	/**是否允许为空0否 1是*/
    @ApiModelProperty(value = "是否允许为空0否 1是")
	private Integer dbIsNull;
	/**数据库字段类型*/
    @ApiModelProperty(value = "数据库字段类型")
	private String dbType;
	/**数据库字段长度*/
    @ApiModelProperty(value = "数据库字段长度")
	private Integer dbLength;
	/**小数点*/
    @ApiModelProperty(value = "小数点")
	private Integer dbPointLength;
	/**表字段默认值*/
    @ApiModelProperty(value = "表字段默认值")
	private String dbDefaultVal;
	/**字典code*/
    @ApiModelProperty(value = "字典code")
	private String dictField;
	/**字典表*/
    @ApiModelProperty(value = "字典表")
	private String dictTable;
	/**字典Text*/
    @ApiModelProperty(value = "字典Text")
	private String dictText;
	/**表单控件类型*/
    @ApiModelProperty(value = "表单控件类型")
	private String fieldShowType;
	/**跳转URL*/
    @ApiModelProperty(value = "跳转URL")
	private String fieldHref;
	/**表单控件长度*/
    @ApiModelProperty(value = "表单控件长度")
	private Integer fieldLength;
	/**表单字段校验规则*/
    @ApiModelProperty(value = "表单字段校验规则")
	private String fieldValidType;
	/**字段是否必填*/
    @ApiModelProperty(value = "字段是否必填")
	private String fieldMustInput;
	/**扩展参数JSON*/
    @ApiModelProperty(value = "扩展参数JSON")
	private String fieldExtendJson;

	private java.lang.String fieldDefaultValue;
	/**是否查询条件0否 1是*/
    @ApiModelProperty(value = "是否查询条件0否 1是")
	private java.lang.Integer isQuery;
	/**表单是否显示0否 1是*/
    @ApiModelProperty(value = "表单是否显示0否 1是")
	private java.lang.Integer isShowForm;
	/**列表是否显示0否 1是*/
    @ApiModelProperty(value = "列表是否显示0否 1是")
	private java.lang.Integer isShowList;
	/**是否是只读（1是 0否）*/
    @ApiModelProperty(value = "是否是只读（1是 0否）")
	private java.lang.Integer isReadOnly;
	/**查询模式*/
    @ApiModelProperty(value = "查询模式")
	private java.lang.String queryMode;
	/**外键主表名*/
    @ApiModelProperty(value = "外键主表名")
	private java.lang.String mainTable;
	/**外键主键字段*/
    @ApiModelProperty(value = "外键主键字段")
	private java.lang.String mainField;
	/**排序*/
    @ApiModelProperty(value = "排序")
	private java.lang.Integer orderNum;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**自定义值转换器*/
    @ApiModelProperty(value = "自定义值转换器")
	private java.lang.String converter;
	/**查询默认值*/
    @ApiModelProperty(value = "查询默认值")
	private java.lang.String queryDefVal;
	/**查询配置字典text*/
    @ApiModelProperty(value = "查询配置字典text")
	private java.lang.String queryDictText;
	/**查询配置字典code*/
    @ApiModelProperty(value = "查询配置字典code")
	private java.lang.String queryDictField;
	/**查询配置字典table*/
    @ApiModelProperty(value = "查询配置字典table")
	private java.lang.String queryDictTable;
	/**查询显示控件*/
    @ApiModelProperty(value = "查询显示控件")
	private java.lang.String queryShowType;
	/**是否启用查询配置1是0否*/
    @ApiModelProperty(value = "是否启用查询配置1是0否")
	private java.lang.String queryConfigFlag;
	/**查询字段校验类型*/
    @ApiModelProperty(value = "查询字段校验类型")
	private java.lang.String queryValidType;
	/**查询字段是否必填1是0否*/
    @ApiModelProperty(value = "查询字段是否必填1是0否")
	private java.lang.String queryMustInput;
	/**是否支持排序1是0否*/
    @ApiModelProperty(value = "是否支持排序1是0否")
	private java.lang.String sortFlag;
}
