package com.medusa.gruul.api.mapper;

import java.util.List;

import com.medusa.gruul.api.api.entity.FieldRelationship;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 字段关系
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
public interface FieldRelationshipMapper extends BaseMapper<FieldRelationship> {
    /**
     * 根据接口ID删除字段
     *
     * @param externalSystemItemId
     */
    void deleteByItemId(Long externalSystemItemId);
}
