package com.medusa.gruul.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.api.api.entity.ExternalSystemItem;
import com.medusa.gruul.api.api.entity.FieldRelationship;
import com.medusa.gruul.api.api.entity.OnlCgformField;
import com.medusa.gruul.api.mapper.FieldRelationshipMapper;
import com.medusa.gruul.api.model.dto.FieldRelationshipDto;
import com.medusa.gruul.api.service.IExternalSystemItemService;
import com.medusa.gruul.api.service.IFieldRelationshipService;
import com.medusa.gruul.api.service.IOnlCgformFieldService;
import com.medusa.gruul.api.web.enums.ExternalSystemItemEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 字段关系
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
@Service
public class FieldRelationshipServiceImpl extends ServiceImpl<FieldRelationshipMapper, FieldRelationship> implements IFieldRelationshipService {
    @Autowired
    private IFieldRelationshipService fieldRelationshipService;
    @Autowired
    private IExternalSystemItemService externalSystemItemService;
    @Autowired
    private IOnlCgformFieldService onlCgformFieldService;

    @Override
    public List<FieldRelationship> queryPageList(FieldRelationshipDto fieldRelationshipDto) {
        //获取外部系统子表信息
        ExternalSystemItem externalSystemItem=externalSystemItemService.getById(fieldRelationshipDto.getExternalSystemItemId());
        //获取表字段信息
        LambdaQueryWrapper<OnlCgformField> fieldLambdaQueryWrapper=new LambdaQueryWrapper<OnlCgformField>();
        if("2".equals(fieldRelationshipDto.getTableType())){
            fieldLambdaQueryWrapper.eq(OnlCgformField::getCgformHeadId, Long.toString(fieldRelationshipDto.getLocalTableId()));
        }else {
            fieldLambdaQueryWrapper.eq(OnlCgformField::getCgformHeadId, Long.toString(externalSystemItem.getLocalTableId()));
        }
        List<OnlCgformField> onlCgformFieldList=onlCgformFieldService.list(fieldLambdaQueryWrapper);

        //获取字段关系信息
        LambdaQueryWrapper<FieldRelationship> fieldRelationshipLambdaQueryWrapper=new LambdaQueryWrapper<FieldRelationship>();
        fieldRelationshipLambdaQueryWrapper.eq(FieldRelationship::getExternalSystemItemId,externalSystemItem.getId());
        List<FieldRelationship> fieldRelationshipList=fieldRelationshipService.list(fieldRelationshipLambdaQueryWrapper);
        List<FieldRelationship>  FieldRelationshipVo=new ArrayList<FieldRelationship>();
        //如果是子表默认为添加的子表信息
        if("2".equals(fieldRelationshipDto.getTableType())){
            fieldRelationshipList=new ArrayList<>();
        }
        if(fieldRelationshipList==null||fieldRelationshipList.size()==0){
            Map<String, FieldRelationship> fieldRelationshipMap=new HashMap<String, FieldRelationship>();
            if(CollectionUtil.isNotEmpty(fieldRelationshipList)){
                //本系统对外部系统
                if(ExternalSystemItemEnum.SYSTEM.getStatus()==externalSystemItem.getContrastType()){
                    fieldRelationshipMap = fieldRelationshipList.stream().collect(Collectors.toMap(FieldRelationship::getSourceField, v -> v));
                }else{
                    fieldRelationshipMap = fieldRelationshipList.stream().collect(Collectors.toMap(FieldRelationship::getTargetField, v -> v));
                }
            }
            //根据表字段名字查询字段关系信息
            for(OnlCgformField onlCgformField:onlCgformFieldList){
                FieldRelationship fieldRelationship=new FieldRelationship();
                if(fieldRelationshipMap.get(onlCgformField.getDbFieldName())!=null){
                    fieldRelationship=fieldRelationshipMap.get(onlCgformField.getDbFieldName());
                }else {
                    //本系统对外部系统
                    if(ExternalSystemItemEnum.SYSTEM.getStatus()==externalSystemItem.getContrastType()){
                        fieldRelationship.setSourceField(onlCgformField.getDbFieldName());
                    }else{
                        fieldRelationship.setTargetField(onlCgformField.getDbFieldName());
                    }

                    fieldRelationship.setExternalSystemItemId(fieldRelationshipDto.getExternalSystemItemId());
                }
                FieldRelationshipVo.add(fieldRelationship);
            }
        }else {
            FieldRelationshipVo.addAll(fieldRelationshipList);
        }

        return FieldRelationshipVo;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void retain(FieldRelationshipDto fieldRelationshipDto) {
        //获取字段关系信息
        List<FieldRelationship> fieldRelationshipList=fieldRelationshipDto.getFieldRelationshipList();
        // 如果是子表字段直接添加
        if("2".equals(fieldRelationshipDto.getTableType()) ){
            fieldRelationshipService.saveOrUpdateBatch(fieldRelationshipList);
        }else {
            //如果是主表字段先删除后添加
            LambdaQueryWrapper<FieldRelationship> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(FieldRelationship::getExternalSystemItemId,fieldRelationshipDto.getExternalSystemItemId());
            fieldRelationshipService.deleteByItemId(fieldRelationshipDto.getExternalSystemItemId());
            fieldRelationshipService.saveBatch(fieldRelationshipList);
        }
//       //用于修改
//        List<FieldRelationship> updateFieldRelationshipList=fieldRelationshipList.stream().filter(item -> item.getId() != null).collect(Collectors.toList());
//        //用于添加
//        List<FieldRelationship> addFieldRelationshipList=fieldRelationshipList.stream().filter(item -> item.getId() == null).collect(Collectors.toList());
//        fieldRelationshipService.saveBatch(addFieldRelationshipList);
//        fieldRelationshipService.updateBatchById(updateFieldRelationshipList);
    }
    @Override
    public void deleteByItemId(Long externalSystemItemId){
        this.baseMapper.deleteByItemId(externalSystemItemId);
    }
}
