package com.medusa.gruul.api.task;

import ch.qos.logback.core.db.dialect.DBUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Session;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.api.api.entity.ExternalSystem;
import com.medusa.gruul.api.api.entity.ExternalSystemItem;
import com.medusa.gruul.api.api.entity.ExternalSystemTemp;
import com.medusa.gruul.api.api.entity.FieldRelationship;
import com.medusa.gruul.api.service.IExternalSystemItemService;
import com.medusa.gruul.api.service.IExternalSystemService;
import com.medusa.gruul.api.service.IExternalSystemTempService;
import com.medusa.gruul.api.service.IFieldRelationshipService;
import com.medusa.gruul.api.util.SpringContextUtil;
import com.medusa.gruul.api.web.enums.ExternalSystemItemEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.constant.ShoppingCartRedisKey;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.sun.corba.se.spi.orbutil.threadpool.Work;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.ParseException;
import org.apache.http.client.config.RequestConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @description: ReceiveData.java
 * @author: qsx
 * @date: 20122/03/21 16:00
 */
@Slf4j
@Component
public class SendAndReceiveDataXxlJob {
    private static Logger logger = LoggerFactory.getLogger(SendAndReceiveDataXxlJob.class);

    @Autowired
    private IExternalSystemService externalSystemService;
    @Autowired
    private IExternalSystemItemService externalSystemItemService;
    @Autowired
    private IFieldRelationshipService fieldRelationshipService;
    @Autowired
    private IExternalSystemTempService externalSystemTempService;
    @Autowired
    private final JdbcTemplate jdbcTemplate;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    private final RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(3 * 1000).setConnectTimeout(3 * 1000).build();

    public SendAndReceiveDataXxlJob(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    private Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    private static Pattern humpPattern = Pattern.compile("[A-Z]");


    /**
     * 数据接收、发送定时任务
     */
    @XxlJob("SendAndReceiveDataJobHandler")
    public ReturnT updateSkuTask() throws Exception {
        log.info("-----------数据接收、发送开启-----------");
        ReturnT returnT=new ReturnT();
        returnT.setCode(200);
        returnT.setContent("测试！！！");
        returnT.setMsg("成功！");
        try {

            //获取外部系统
            List<ExternalSystem> externalSystemList=externalSystemService.list();
            //获取外部系统id
            List<Long> externalSystemIdList = externalSystemList.stream().map(ExternalSystem::getId).collect(Collectors.toList());
            //获取外部系统子表信息
            LambdaQueryWrapper<ExternalSystemItem> itemLambdaQueryWrapper=new LambdaQueryWrapper<>();
            if(CollectionUtils.isNotEmpty(externalSystemIdList)){
                itemLambdaQueryWrapper.in(ExternalSystemItem::getExternalSystemId,externalSystemIdList);
            }
            List<ExternalSystemItem> externalSystemItemList=externalSystemItemService.getByExternalSystemIds(externalSystemIdList);
            //获取外部系统子表Id
            List<Long> externalSystemItemIdList = externalSystemItemList.stream().map(ExternalSystemItem::getId).collect(Collectors.toList());
            //获取字段关系表(主表类型)
            LambdaQueryWrapper<FieldRelationship> fieldLambdaQueryWrapper=new LambdaQueryWrapper<>();
            if(CollectionUtils.isNotEmpty(externalSystemItemIdList)){
                fieldLambdaQueryWrapper.in(FieldRelationship::getExternalSystemItemId,externalSystemItemIdList);
            }
            fieldLambdaQueryWrapper.eq(FieldRelationship::getTableType,ExternalSystemItemEnum.TABLE_PARENT.getStatus());
            List<FieldRelationship> fieldRelationshipList=fieldRelationshipService.list(fieldLambdaQueryWrapper);
            for(ExternalSystem externalSystem:externalSystemList){
                //获取token
                String token=getToken(externalSystem);
                String url=externalSystem.getAddress();
                Map paramsMap=new HashMap();
                paramsMap.put("access_token",token);
                List<ExternalSystemItem> esiList=externalSystemItemList.stream().filter(t->externalSystem.getId().equals(t.getExternalSystemId())).collect(Collectors.toList());
                for(ExternalSystemItem esi:esiList){
                    //判断接口是否启用
                    if(ExternalSystemItemEnum.DEACTIVATE.getStatus()==(new Integer(esi.getIsUse())) ){
                        continue;
                    }
                    //获取数据最后修改时间
                    LambdaQueryWrapper<ExternalSystemTemp> lambdaQueryWrapper=new LambdaQueryWrapper<ExternalSystemTemp>();
                    lambdaQueryWrapper.eq(ExternalSystemTemp::getExternalSystemItemId,esi.getId())
                            .orderByDesc(ExternalSystemTemp::getLastSendDate)
                            .last("LIMIT 1");
                    ExternalSystemTemp externalSystemTemp=externalSystemTempService.getOne(lambdaQueryWrapper);
                    long interval =0;
                    if(StrUtil.isNotBlank(esi.getTimeInterval())){
                        interval = Long.parseLong(esi.getTimeInterval())*1000;
                    }

                    //判断时间是否发送请求
                    long curTime = System.currentTimeMillis();
                    if (externalSystemTemp!=null && externalSystemTemp.getLastSendDate()!=null){
                        long lastSendDate =externalSystemTemp.getLastSendDate().getTime();
                        long sendDataDate = lastSendDate+interval;

                        //不刷新  没有到发送时间
                        if (sendDataDate>curTime){
                            continue;
                        }
                    }
                    //获取实体类class
                    Class entityPath=null;
                    //主表的默认值
                    Map<String,Map> defaultMap=getDefault(esi.getDefaultValue());

                    //获取与外部系统子表的字段关系
                    List<FieldRelationship> fieldList=fieldRelationshipList.stream().filter(t->esi.getId().equals(t.getExternalSystemItemId())).collect(Collectors.toList());

                    //判断该接口是本系统对外部系统或者外部系统对本系统
                    if(ExternalSystemItemEnum.SYSTEM.getStatus()==esi.getContrastType()){
                        //本系统对外部系统

                        String[] sqlList=esi.getSqlStatement().split("@");
                        //主表的sql
                        String sql=sqlList[0];

                        List dataList=selectData(sql,entityPath);
                        //获取是与子表关联的字段信息
                        List<FieldRelationship>  subfields= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(all -> all).collect(Collectors.toList());
                        //获取本系统字段
                        List<String>  subSourcefields= fieldRelationshipList.stream().map(all -> all.getSourceField()).collect(Collectors.toList());
                        //存子表数据
                        Map<String,Object> subDataMap=new HashMap<>();
                        for(int z=1;sqlList.length>z;z++){

                            String[] sqls =sqlList[z].split(":");
                            //获取实体类class
                            Class subEntityPath=null;
                            String subSql=sqls[1];
                            List data=selectData(subSql,subEntityPath);
                            subDataMap.put(sqls[0],data);
                        }

                        //获取 子表的字段关系
                        List<String> field=new ArrayList<>();
                        //获取关联子表的字段信息（本系统的字段）
                        field= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(FieldRelationship::getSourceField).collect(Collectors.toList());
                        //获取是关联子表的字段
                        List<FieldRelationship>  subfield= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(all -> all).collect(Collectors.toList());

                        LambdaQueryWrapper<FieldRelationship> fieldRelationshipLambdaQueryWrapper=new LambdaQueryWrapper<FieldRelationship>();
                        fieldRelationshipLambdaQueryWrapper.in(FieldRelationship::getParentTableAssociatedFields,field)
                                .eq(FieldRelationship::getTableType,ExternalSystemItemEnum.TABLE_SUB.getStatus())
                                .eq(FieldRelationship::getExternalSystemItemId,esi.getId());
                        //获取子表的字段映射信息
                        List<FieldRelationship> subFieldRelationshipList= fieldRelationshipService.list(fieldRelationshipLambdaQueryWrapper);
                        // 用本系统的字段为key,vuel为子表字段关系的List
                        Map<String,List> subFieldmap=new HashMap<>();
                        for(String f:field){
                            List<FieldRelationship> fieldRelationships=subFieldRelationshipList.stream()
                                    .filter(fiel -> f.equals(fiel.getParentTableAssociatedFields()) ).collect(Collectors.toList());
                            for(FieldRelationship fiel:fieldRelationships){
                                //转驼峰
                                fiel.setSourceField(fiel.getSourceField());
                            }
                            subFieldmap.put(f,fieldRelationships);
                        }

                        HashMap<String, Object> values=new HashMap<String, Object>();
                        //外部系统的路径
                        String route=url+esi.getAddress();
                        //获取外部系统临时表中的数据
                        List<ExternalSystemTemp> externalSystemTempList=getTempData(externalSystem.getId(),esi.getId());
                        //获取外部系统临时表的存的本系统数据的Id
                        List<Long> localDataIdList=externalSystemTempList.stream().map(ExternalSystemTemp::getLocalDataId).collect(Collectors.toList());
                        //将外部系统临时表封装称 外部数据id为key的map
                        Map<Long, ExternalSystemTemp> externalSystemTempMap=new HashMap<>();
                        if(CollectionUtil.isNotEmpty(localDataIdList)){
                            externalSystemTempMap = externalSystemTempList.stream().collect(Collectors.toMap(ExternalSystemTemp::getLocalDataId, v -> v));
                        }
                        //用于添加或者修改的外部系统临时表中的数据
                        List<ExternalSystemTemp> addOrUpdateExternalSystemTempList=new ArrayList<>();
                        for(int i=0; i<dataList.size(); i++){

                            // 将 实体类 转换为 Map
                            Map map = JSON.parseObject(JSON.toJSONString(dataList.get(i)), Map.class);
                            //子表字段
                            List<Map> vals =new ArrayList<>();
                            values=putData(map,0,fieldList,vals);
                            //获取子类
                            for(FieldRelationship sub:subfields){
                                List subList=new ArrayList();
                                List subDadaList=new ArrayList();
                                List<FieldRelationship>  subFieldList=subFieldmap.get(sub.getSourceField());
                                if(subDataMap.get(sub.getSourceField())!=null){
                                    subList=(List)subDataMap.get(sub.getSourceField());
                                    //获取实体类class
                                    Class subEntityPath=null;
                                    for(int q=0;q<subList.size();q++){
                                        //将数据转化为map形式
                                        Map dataMap=(Map)subList.get(q);
                                        //数据一样为两条关联的数据
                                        if(dataMap.get(sub.getSubField()).equals(map.get(sub.getParentField()))){
                                            dataMap=putData(dataMap,0,subFieldList,vals);
                                            subDadaList.add(dataMap);
                                        }
                                    }
                                    values.put(sub.getTargetField(),subDadaList);
                                }
                            }

                            paramsMap.putAll(values);
                            String str= "";
                            try {
                                //判断是通过get还是post请求；
                                if("get".equals(esi.getRequest())){
                                    str= HttpUtil.get(route,paramsMap);
                                }else if("post".equals(esi.getRequest())){
                                    str= HttpUtil.post(route,paramsMap);
                                }else{
                                    continue;
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            JSONObject object=JSON.parseObject(str);
//                           接收数据
                            Map jsonToMap  =  JSONObject.parseObject(object.toJSONString());
                            Map rsData = jsonToMap;
                            Object objData = rsData;

                            String a=esi.getDataField();
                            String[] sp=a.split(",");
                            if(sp.length>1){
                                for(int j=0; sp.length>j;j++){
                                    if(j==(sp.length-1)){
                                        objData=rsData.get(sp[j]);
                                    }else {
                                        rsData =(Map) rsData.get(sp[j]);
                                    }
                                }
                            }else {
                                objData = rsData.get(sp[0]);
                            }
                            //获取对方的id
                            Map dadaMap=(Map)objData;
                            String id=dadaMap.get("id").toString();
                            if(StrUtil.isEmpty(id)){
                                log.info("对方未返回id，不添加对应关系！");
                                continue;
                            }
                            //用于记录外部系统数据
                            ExternalSystemTemp externalSystemTemp1=new ExternalSystemTemp();
                            //判断是否发送过该条信息
                            if(externalSystemTempMap.get( Long.parseLong(map.get("id").toString()))!=null){
                                externalSystemTemp1=externalSystemTempMap.get( Long.parseLong(map.get("id").toString()));
                            }else {
                                externalSystemTemp1.setLocalDataId(Long.parseLong(map.get("id").toString()));
                                externalSystemTemp1.setExternalSystemId(externalSystem.getId());
                                externalSystemTemp1.setExternalSystemItemId(esi.getId());
                            }
                            externalSystemTemp1.setExternalDataId(id);
                            externalSystemTemp1.setLastSendDate(new Date());
                            addOrUpdateExternalSystemTempList.add(externalSystemTemp1);
                        }
                        //更新外部系统临时表中的数据
                        externalSystemTempService.saveOrUpdateBatch(addOrUpdateExternalSystemTempList);

                    }else if(ExternalSystemItemEnum.EXTERNAL.getStatus()==esi.getContrastType()){
                        //外部系统对本系统
                        String route=url+esi.getAddress();
                        // 测试用的
//                        paramsMap.put("orderNum","DH-O-20210604-175600");

                        String str= "";
                        try {
                            //判断是通过get还是post请求；
                            if("get".equals(esi.getRequest())){
                                str= HttpUtil.get(route,paramsMap);
                            }else if("post".equals(esi.getRequest())){
                                str= HttpUtil.post(route,paramsMap);
                            }else{
                                continue;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        JSONObject object=JSON.parseObject(str);
                        Map jsonToMap  =  JSONObject.parseObject(object.toJSONString());
                        Map rsData = jsonToMap;
                        Object objData = rsData;

                        String a=esi.getDataField();
                        String[] sp=a.split(",");
                        if(sp.length>1){
                            for(int i=0; sp.length>i;i++){
                                if(i==(sp.length-1)){
                                    objData=rsData.get(sp[i]);
                                }else {
                                    rsData =(Map) rsData.get(sp[i]);
                                }
                            }
                        }else {
                            objData = rsData.get(sp[0]);
                        }
                        HashMap<String, Object> values=new HashMap<String, Object>();
                        //获取外部系统临时表中的数据
                        List<ExternalSystemTemp> externalSystemTempList=getTempData(externalSystem.getId(),esi.getId());
                        //获取外部系统临时表的存的外部数据的Id
                        List<String> externalDataIdList=externalSystemTempList.stream().map(ExternalSystemTemp::getExternalDataId).collect(Collectors.toList());
                        //获取外部系统临时表的存的本系统数据的Id
                        List<Long> localDataIdList=externalSystemTempList.stream().map(ExternalSystemTemp::getLocalDataId).collect(Collectors.toList());
                        //将外部系统临时表封装称 外部数据id为key的map
                        Map<String, ExternalSystemTemp> externalSystemTempMap=new HashMap<>();
                        if(CollectionUtil.isNotEmpty(externalDataIdList)){
                            externalSystemTempMap = externalSystemTempList.stream().collect(Collectors.toMap(ExternalSystemTemp::getExternalDataId, v -> v));
                        }

                        //用于新增的外部系统数据记录表
                        List<ExternalSystemTemp> addExternalSystemTempList=new ArrayList<>();
                        //用于修改的外部系统数据记录表
                        List<ExternalSystemTemp> updateExternalSystemTempList=new ArrayList<>();
                        //用于修改的外部系统数据记录表
                        List<ExternalSystemTemp> updatExternalSystemTempList=new ArrayList<>();
                        Class typeListName=Class.forName("java.util.List");

                        //存子表数据
                        Map<String,Object> subDataMap=new HashMap<>();
                        // 存放父表子表关联字段
                        Map<String,String> relationMap=new HashMap<>();
                        //获取是子表类型的字段信息
                        List<FieldRelationship>  subfields= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(all -> all).collect(Collectors.toList());
                        for(FieldRelationship field:subfields){
                            //存放父表与子表关联的父表的字段
                            relationMap.put(field.getTargetField()+"Parent",field.getParentField());
                            //存放父表与子表关联的子表的字段
                            relationMap.put(field.getTargetField()+"Sub",field.getSubField());
                        }
                        //需要删除的外部系统临时表
                        List<Long> delExternalSystemTempId=new ArrayList<>();
                        //用于添加外部传来的新数据
                        List addList=new ArrayList<>();
                        //用于修改外部传来的新数据
                        List updataList=new ArrayList<>();
                        if(  objData instanceof List){
                            //数据转化
                            List<Map> mapList = JSON.parseArray(objData.toString(), Map.class);
                            for(Map map :mapList){
                                getData(map,fieldList,fieldRelationshipList,entityPath,externalSystemTempMap,addList,updataList,addExternalSystemTempList,updatExternalSystemTempList,externalSystem.getId(),esi,subDataMap,relationMap,delExternalSystemTempId);
                            }
                        }else {
                            Map map = (Map)objData;
                            getData(map,fieldList,fieldRelationshipList,entityPath,externalSystemTempMap,addList,updataList,addExternalSystemTempList,updatExternalSystemTempList,externalSystem.getId(),esi,subDataMap,relationMap,delExternalSystemTempId);
                        }
                        String[] subTbl=esi.getSubTableName().split("@");
                        Map<String,String> subTableMap=new HashMap<>();
                        for(String tbl:subTbl){
                            String[] strList=tbl.split(":");
                            subTableMap.put(strList[0],strList[1]);
                        }
                        //添加或者修改数据
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String dateStr = sdf.format(date);
                        //成功的次数
                        int success=0;
                        //失败的次数
                        int fail=0;
                        //主表执行添加的sql语句
                        for(Object fatherData:addList){
                            Map<String, Object> fatherDataMap = (Map)fatherData;
                            //如果id为空则跳过
                            if(StrUtil.isEmpty(fatherDataMap.get("id").toString())){
                                continue;
                            }
                            String sql="INSERT INTO "+esi.getTableName()+"(";
                            String val=" VALUES ( ";
                            for (String key : fatherDataMap.keySet()) {
                                sql+=key+",";
                                val+="'"+fatherDataMap.get(key)+"',";
                            }
                            if(!defaultMap.get("insert").isEmpty()){
                                //默认值
                                Map valMap=defaultMap.get("insert");
                                for (Object key : valMap.keySet()) {
                                    if("new Date()".equals(valMap.get(key.toString()))){
                                        sql+=key.toString()+",";
                                        val+="'"+dateStr+"',";
                                    }else {
                                        sql+=key.toString()+",";
                                        val+="'"+valMap.get(key.toString())+"',";
                                    }
                                }
                            }
                            sql=sql.substring(0,sql.length()-1);
                            val=val.substring(0,val.length()-1);
                            sql+=")";
                            val+=");";
                            sql+=val;
                            int num=insertData(sql);
                            if(num>0){
                                success+=1;
                            }else {
                                fail+=1;
                            }
                        }
                        log.info(esi.getTableName()+"表成功添加:"+success+"条数据,添加失败："+fail+"条数据");
                        //修改本系统数据
                        success=0;
                        fail=0;
                        //主表执行修改的sql语句
                        for(Object fatherData:updataList){
                            Map<String, Object> fatherDataMap = (Map)fatherData;
                            //如果id为空则跳过
                            if(StrUtil.isEmpty(fatherDataMap.get("id").toString())){
                                continue;
                            }
                            String sql="UPDATE "+esi.getTableName()+" set ";
                            for (String key : fatherDataMap.keySet()) {
                                sql+=key+"='"+fatherDataMap.get(key)+"',";
                            }
                            if(!defaultMap.get("update").isEmpty()){
                                //默认值
                                Map valMap=defaultMap.get("update");
                                for (Object key : valMap.keySet()) {
                                    // 如果值为new Date()为赋值现在的时间
                                    if("new Date()".equals(valMap.get(key.toString()))){
                                        sql+=key+"='"+dateStr+"',";
                                    }else {
                                        sql+=key+"='"+fatherDataMap.get(key)+"',";
                                    }
                                }
                            }
                            sql=sql.substring(0,sql.length()-1);
                            sql+=" WHERE id='"+fatherDataMap.get("id")+"'";

                            int num=insertData(sql);
                            int delNum=0;
                            //删除该条数据的子表数据
                            for(FieldRelationship subField:subfields){
                                String del="UPDATE  "+subTableMap.get(subField.getTargetField())+" set is_deleted='1' WHERE "+subField.getSubField()+" = '"+fatherDataMap.get(subField.getParentField())+"'";
                                delNum=insertData(del);
                                log.info("删除语句："+del);
                            }
                            if(num>0){
                                success+=1;
                            }else {
                                fail+=1;
                            }
                        }
                        log.info(esi.getTableName()+"表成功修改:"+success+"条数据,修改失败："+fail+"条数据");
                        success=0;
                        fail=0;
                        //获取子表默认值
                        String[] subDefaultVals=esi.getSubDefaultValue().split("@");
                        Map<String,String> subDefaultValMap=new HashMap<>();
                        for(String subDefaultVal:subDefaultVals){
                            String[] strList=subDefaultVal.split("%");
                            subDefaultValMap.put(strList[0],strList[1]);
                        }
                        //添加子表数据
                        for (String subKey : subTableMap.keySet()) {
                            success=0;
                            fail=0;
                            if(subDataMap.get(subKey)!=null){
                                for(Object sub:(List)subDataMap.get(subKey)){
                                    Map<String, Object> fatherDataMap = (Map)sub;
                                    String sql="INSERT INTO "+subTableMap.get(subKey)+"(id,";
                                    String val=" VALUES (CONCAT(REPLACE(unix_timestamp(current_timestamp(3)),'.',''),FLOOR(1 + (RAND() * 100001))), ";
                                    for (String key : fatherDataMap.keySet()) {
                                        sql+=key+",";
                                        val+="'"+fatherDataMap.get(key)+"',";
                                    }
                                    //添加子表的默认值
                                    if(StrUtil.isNotEmpty(subDefaultValMap.get(subKey))){
                                        Map<String,Map>  sumDefaultMap=getDefault(subDefaultValMap.get(subKey));
                                        if(!sumDefaultMap.get("insert").isEmpty()){
                                            //默认值
                                            Map valMap=sumDefaultMap.get("insert");
                                            for (Object key : valMap.keySet()) {
                                                if("new Date()".equals(valMap.get(key.toString()))){
                                                    sql+=key.toString()+",";
                                                    val+="'"+dateStr+"',";
                                                }else {
                                                    sql+=key.toString()+",";
                                                    val+="'"+valMap.get(key.toString())+"',";
                                                }
                                            }
                                        }
                                    }
                                    sql=sql.substring(0,sql.length()-1);
                                    val=val.substring(0,val.length()-1);
                                    sql+=")";
                                    val+=");";
                                    sql+=val;
                                    int num=insertData(sql);
                                    log.info("子表数据"+sql);
                                    if(num>0){
                                        success+=1;
                                    }else {
                                        fail+=1;
                                    }

                                }
                            }
                            log.info(subTableMap.get(subKey)+"表成功添加:"+success+"条数据,添加失败："+fail+"条数据");
                        }
                        //批量添加数据关系
                        externalSystemTempService.saveBatch(addExternalSystemTempList);
                        //批量更新数据关系
                        externalSystemTempService.updateBatchById(updatExternalSystemTempList);
                        //批量删除数据关系
                        externalSystemTempService.removeByIds(delExternalSystemTempId);
                        log.info("---------------------外部系统接收、发送数据结束----------------------");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            returnT.setCode(500);
            returnT.setMsg(e.toString());
        }
        return returnT;
    }

    public List selectData(String sql,Class clas) {
        log.debug("给外部系统发送的查询语句："+sql);
        List list = jdbcTemplate.queryForList(sql);
        return list;
    }
    //向数据库添加数据
    public int insertData(String sql) {
        int num = jdbcTemplate.update(sql);
        return num;
    }
    //查数据库存在的记录
    public int getCount(String sql) {
        int num = jdbcTemplate.queryForObject(sql,Integer.class);
        return num;
    }
    //获取默认值 对  insert:create_time=new Date(),update_time=new Date()#update:update_time=new Date()  这样类型的默认值进行切割
    public Map<String,Map> getDefault(String defaultVal) {
        String[] defaultSplit=defaultVal.split("#");
        if (defaultSplit.length<1){
            return null;
        }
        Map<String,Map> defaultMap=new HashMap<>();
        for(String ds:defaultSplit){
            String[] strList=ds.split(":");
            if(strList.length<2){
                continue;
            }
            String[] data=strList[1].split(",");
            Map<String,String> defaultValMap=new HashMap<>();
            for(String da:data){
                String [] val=da.split("=");
                defaultValMap.put(val[0],val[1]);
            }
            defaultMap.put(strList[0],defaultValMap);
        }
        return defaultMap;
    }

    /**
     * Object转Map
     * <AUTHOR>
     * @date 2022/3/25 16:50
     * @param obj
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    public static Map<String, Object> getObjectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        JSONObject.toJSONString(obj);
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            if (value == null){
                value = "";
            }
            map.put(fieldName, value);
        }
        return map;
    }
    /**
     *
     * @description: 实体类转Map
     * @author: Jeff
     * @date: 2019年10月29日
     * @param object
     * @return
     */
    public static Map<Object, Object> entityToMap(Object object) {
        Map<Object, Object> map = new HashMap<>();
        for (Field field : object.getClass().getDeclaredFields()) {
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(), o);
                field.setAccessible(flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return map;
    }

    /**
     * Map转Object
     *
     * <AUTHOR>
     * @date 2022/3/25 16:50
     * @param map
     * @param beanClass
     * @return java.lang.Object
     */
    public static Object mapToObject(Map<Object, Object> map, Class<?> beanClass) throws Exception {
        if (map == null) {
            return null;
        }
        Object obj=JSONObject.parseObject(JSONObject.toJSONString(map), beanClass);
        return obj;
    }

    /**多个（列表）对象的所有键值*/
    public static List<Object> getValuesByKey(List<Object> object, String key) {
        List<Object> list = new ArrayList<Object>();
        for (Object obj : object) {
            // 得到类对象
            Class userCla = (Class) obj.getClass();
            /* 得到类中的所有属性集合 */
            Field[] fs = userCla.getDeclaredFields();
            for (int i = 0; i < fs.length; i++) {
                Field f = fs[i];
                f.setAccessible(true);
                // 设置些属性是可以访问的
                try {
                    if (f.getName().endsWith(key)) {
                        list.add(f.get(obj));
                    }
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("多个（列表）对象的某个键的值列表====" + list.toString());
        return list;
    }
    /**单个对象的某个键的值*/
    public static Object getValueByKey(Object obj, String key) {
        // 得到类对象
        Class userCla = (Class) obj.getClass();
        /* 得到类中的所有属性集合 */
        Field[] fs = userCla.getDeclaredFields();
        for (int i = 0; i < fs.length; i++) {
            Field f = fs[i];
            f.setAccessible(true);
            // 设置些属性是可以访问的
            try {
                if (f.getName().endsWith(key)) {
                    //System.out.println("单个对象的某个键的值==反射==" + f.get(obj));
                    return f.get(obj);
                }
            }
            catch (IllegalArgumentException e) {
                e.printStackTrace();
            }
            catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        // 没有查到时返回空字符串
        return "";
    }

    /**
     * 将String 转换为Data
     * strTime的时间格式必须要与formatType的时间格式相同
     * <AUTHOR>
     * @date 2022/3/25 16:51
     * @param strTime
     * @param formatType
     * @return java.util.Date
     */
    public static Date stringToDate(String strTime, String formatType)
            throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat(formatType);
        Date date = null;
        try {
            date = formatter.parse(strTime);
        } catch (java.text.ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * map类型转Object类型
     *
     * <AUTHOR>
     * @date 2022/3/28 15:42
     * @param clazz
     * @param map
     * @return T
     */
    private <T> T buildObject(Class clazz, Map<String, Object> map) throws IllegalAccessException, InstantiationException {
        Object resultModel = clazz.newInstance();
        resultModel=JSON.parseObject(JSON.toJSONString(map), clazz);
        return (T) resultModel;
    }

    /**
     * @Description: 查找外部系统临时表中的数据
     * @Param: [data, contrastType]
     * @return: com.medusa.gruul.api.api.entity.ExternalSystemTemp
     * @Author: qsx
     * @Date: 2022/3/22
     */
    public List<ExternalSystemTemp> getTempData(Long externalSystemId, Long externalSystemItemId){
        LambdaQueryWrapper<ExternalSystemTemp> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExternalSystemTemp::getExternalSystemId,externalSystemId)
                .eq(ExternalSystemTemp::getExternalSystemItemId,externalSystemItemId);
        List<ExternalSystemTemp> list=externalSystemTempService.list(lambdaQueryWrapper);
        return list;
    }
    /**
     * values 将数据的字段改成本系统需要的字段
     * map 传进来的数据
     * fieldList 字段关系
     * fieldRelationshipList 字段关系
     * externalSystemTempMap 将外部系统临时表封装称 外部数据id为key的map
     * addList 用于添加外部传来的新数据
     * updataList 用于修改外部传来的新数据
     * addExternalSystemTempList 用于新增的外部系统数据记录表
     * updatExternalSystemTempList 用于修改的外部系统数据记录表
     * externalSystem 外部接口系统表 ID
     * esi 外部接口接口表ID
     * relationMap 父表与子表的关联字段
     * delExternalSystemTempId 需要删除的外部系统临时表
     * 获取添加或者修改的数据
     */

    public  void getData(Map<String, Object> map,List<FieldRelationship> fieldList,List<FieldRelationship> fieldRelationshipList,Class entityPath, Map<String, ExternalSystemTemp> externalSystemTempMap, List addList, List updataList,List<ExternalSystemTemp> addExternalSystemTempList,List<ExternalSystemTemp> updatExternalSystemTempList,Long externalSystemId,ExternalSystemItem esi, Map<String,Object> subDataMap,Map<String,String> relationMap,List<Long> delExternalSystemTempId){
        try {
            //子表字段
            List<Map> vals =new ArrayList<>();
            HashMap<String, Object> values=new HashMap<>();
            //将数据的字段改成本系统需要的字段
            values=putData(map,1,fieldList,vals);

            //获取是关联子表的字段信息
            List<String>  subfields= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(all -> all.getTargetField()).collect(Collectors.toList());

            //存子表与父表关联的父表数据数据
            Map<String,String> subRelationDadaMap=new HashMap<>();

            int number=0;
            //获取本数据库已删除数据的id对应关系表的id（id对应关系表的id）
            if( externalSystemTempMap.get(map.get("id").toString())!=null){
                ExternalSystemTemp updateExternalSystemTemp=externalSystemTempMap.get(map.get("id").toString());
                String sql="SELECT count(*) FROM "+ esi.getTableName()+" WHERE is_deleted='0' and id='"+updateExternalSystemTemp.getLocalDataId()+"'";
                number=getCount(sql);
                if(0==number){
                    delExternalSystemTempId.add(updateExternalSystemTemp.getId());
                }
            }

            //判断对方传过来的数据，我方是否接收过，接收过则更新，没有则添加
            if( externalSystemTempMap.get(map.get("id").toString())!=null&&number>0){
                ExternalSystemTemp updateExternalSystemTemp=externalSystemTempMap.get(map.get("id").toString());
                updateExternalSystemTemp.setLastSendDate(new Date());
                values.put("id",updateExternalSystemTemp.getLocalDataId());
                //获取主表与子表关联字段
                for(String field:subfields){
                    if( relationMap.get(field+"Parent")!=null){
                        String parentField= relationMap.get(field+"Parent");
                        subRelationDadaMap.put( field, values.get(parentField).toString());
                    }
                }
                //获取子表的数据
                getSubData(map, fieldList,  esi, updateExternalSystemTemp.getLocalDataId(),subDataMap,relationMap,subRelationDadaMap);
                updataList.add(values);
                updatExternalSystemTempList.add(updateExternalSystemTemp);
            }else{
                //新增数据
                Long id = remoteMiniAccountService.getId();
                values.put("id",id);
                //获取主表与子表关联字段
                for(String field:subfields){
                    if( relationMap.get(field+"Parent")!=null){
                        String parentField= relationMap.get(field+"Parent");
                        subRelationDadaMap.put( field, values.get(parentField).toString());
                    }
                }
                //获取子表的数据
                getSubData(map, fieldList,  esi, id,subDataMap,relationMap,subRelationDadaMap);
                addList.add(values);
                //用于记录外部传过来的信息
                ExternalSystemTemp newExternalSystemTemp=new ExternalSystemTemp();
                newExternalSystemTemp.setExternalDataId(map.get("id").toString());
                //newExternalSystemTemp.setLocalDataId(Long.valueOf(id));
                newExternalSystemTemp.setLocalDataId((Long)values.get("id"));
                newExternalSystemTemp.setLastSendDate(new Date());
                newExternalSystemTemp.setExternalSystemId(externalSystemId);
                newExternalSystemTemp.setExternalSystemItemId(esi.getId());
                addExternalSystemTempList.add(newExternalSystemTemp);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * values 将数据的字段改成本系统需要的字段
     * map 传进来的数据
     * fieldList 字段关系
     * fieldRelationshipList 字段关系
     * externalSystemTempMap 将外部系统临时表封装称 外部数据id为key的map
     * addList 用于添加外部传来的新数据
     * updataList 用于修改外部传来的新数据
     * addExternalSystemTempList 用于新增的外部系统数据记录表
     * updatExternalSystemTempList 用于修改的外部系统数据记录表
     * externalSystem 外部接口系统表 ID
     * esi 外部接口接口表ID
     * relationMap 父表与子表关联的字段
     * 获取添加或者修改的数据
     */

    public  void getSubData(Map<String, Object> map,List<FieldRelationship> fieldRelationshipList, ExternalSystemItem esi,Long parentId,Map<String,Object> subDataMap,Map<String,String> relationMap,Map<String,String> subRelationDadaMap){
        try {
            //存子表数据
            Map<String,Object> dataMap=new HashMap<>();
            //子表字段
            List<Map> vals =new ArrayList<>();
            //将数据的字段改成本系统需要的字段
//            values=putData(map,1,fieldList,vals);
            List<String> field=new ArrayList<>();
            //获取关联子表的字段（本系统的字段）
            field= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(FieldRelationship::getTargetField).collect(Collectors.toList());
            //获取是子表类型的字段信息
            List<FieldRelationship>  subfield= fieldRelationshipList.stream().filter( f -> f.getFieldType()==ExternalSystemItemEnum.SUB_TABLE.getStatus()).map(all -> all).collect(Collectors.toList());
            //获取子表字段映射信息
            LambdaQueryWrapper<FieldRelationship> fieldRelationshipLambdaQueryWrapper=new LambdaQueryWrapper<FieldRelationship>();
            if(CollectionUtils.isNotEmpty(field)){
                fieldRelationshipLambdaQueryWrapper.in(FieldRelationship::getParentTableAssociatedFields,field);
            }
            fieldRelationshipLambdaQueryWrapper.eq(FieldRelationship::getTableType,ExternalSystemItemEnum.TABLE_SUB.getStatus())
                    .eq(FieldRelationship::getExternalSystemItemId,esi.getId());
            //获取子表的字段映射信息
            List<FieldRelationship> subFieldRelationshipList= fieldRelationshipService.list(fieldRelationshipLambdaQueryWrapper);
            // 用本系统的字段为key,vuel为子表字段关系的List
            Map<String,List> subFieldmap=new HashMap<>();
            for(String f:field){
                List<FieldRelationship> fieldRelationships=subFieldRelationshipList.stream()
                        .filter(fiel -> f.equals(fiel.getParentTableAssociatedFields()) ).collect(Collectors.toList());
                subFieldmap.put(f,fieldRelationships);
            }

            for(FieldRelationship  s:subfield){
                List addOrUpdateList=new ArrayList();
                Object obj=map.get(s.getSourceField());
                //获取子类路径
                String entityPath="";
                Class subEntityPath=null;
                List<FieldRelationship>  fieldRelationships=subFieldmap.get(s.getTargetField());
//               判断子表是list类型还是实体类  分开处理，
//                if(  obj instanceof List){
                //数据转化
                List<Map> mapList = JSON.parseArray(obj.toString(), Map.class);
                for(Map m :mapList){
                    Object subObject=setSubData(  m, fieldRelationships, subEntityPath, parentId,relationMap,subRelationDadaMap);
                    addOrUpdateList.add(subObject);
                }
                //判断同一个key是否加过数据
                if(subDataMap.get(s.getTargetField())!=null){
                    addOrUpdateList.addAll((List)subDataMap.get(s.getTargetField()));
                    subDataMap.put(s.getTargetField(),addOrUpdateList);
                }else {
                    subDataMap.put(s.getTargetField(),addOrUpdateList);
                }
//                }else {
//                    Map ma = (Map)obj;
//                    Object subObject=setSubData(  ma, fieldRelationships, subEntityPath , parentId,relationMap,subRelationDadaMap);
//                    //判断map是否存有数据
//                    if(subDataMap.get(s.getTargetField())!=null){
//                        //判断map存放的是不是list
//                        if(  subDataMap.get(s.getTargetField()) instanceof List){
//                            list.add(subObject);
//                            list.addAll((List)subDataMap.get(s.getTargetField()));
//                        }else {
//                            list.add(subDataMap.get(s.getTargetField()));
//                            list.add(subObject);
//                        }
//                        subDataMap.put(s.getTargetField(),list);
//                    }else {
//                        subDataMap.put(s.getTargetField(),subObject);
//                    }
//                }
            }

        }catch (Exception e) {

            e.printStackTrace();
        }

    }


    /**
     * values 将数据的字段改成本系统需要的字段
     * map 传进来的数据
     * fieldList 字段关系
     * addList 用于添加外部传来的新数据
     * updataList 用于修改外部传来的新数据
     * parentId 父类id
     * 获取添加或者修改的数据
     */

    public  Map setSubData(Map<String, Object> map,List<FieldRelationship> fieldList,Class subEntityPath, Long parentId,Map<String,String> relationMap,Map<String,String> subRelationDadaMap){
//        Object subEntity=new Object();
        Map subDataMap=new HashMap();
        try {
            //子表字段
            List<Map> vals =new ArrayList<>();
            //将数据的字段改成本系统需要的字段
            HashMap<String, Object> values=putData(map,1,fieldList,vals);
            //如果对方没有传回我方ID就认为是新增数据！！！
            subDataMap=values;
            //添加与父表的关联
            //判断与父表的关联是否为空
            if(CollectionUtils.isNotEmpty(fieldList) &&StrUtil.isNotEmpty(fieldList.get(0).getParentTableAssociatedFields())){
                //判断与父表关系存的子表字段是否为空
                if(StrUtil.isNotEmpty(relationMap.get(fieldList.get(0).getParentTableAssociatedFields())+"Sub")){
                    //将数据库字段切换成实体类字段（驼峰）
                    String f=relationMap.get(fieldList.get(0).getParentTableAssociatedFields()+"Sub");
                    subDataMap.put(f,subRelationDadaMap.get(fieldList.get(0).getParentTableAssociatedFields()));
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return subDataMap;
    }


    /**
     * @Description: 通过对照关系实现数据转换
     * @Param: [data, contrastType]
     * @return: java.util.HashMap<java.lang.String,java.lang.Object>
     * @Author: qsx
     * @Date: 2022/3/22
     */
    public HashMap<String, Object> putData(Map<String, Object> data,int contrastType,List<FieldRelationship> fieldRelationship,List<Map> vals){
        //源字段作为key  目标字段为value 字段形式为tableName.field(主表)
        Map<String, String> fields = fieldRelationship.stream().filter(t -> t.getSourceField()!=null && t.getTargetField()!=null && t.getSourceField().length()>0 && t.getFieldType()!=ExternalSystemItemEnum.SUB_TABLE.getStatus()).collect(Collectors.toMap(FieldRelationship::getSourceField,FieldRelationship::getTargetField));

        //转换后数据容器
        HashMap<String, Object> values = new HashMap<>(data.size());
        //转数据
        changeData(values,data,fields,contrastType,"",null);
        return values;
    }

    /**
     * @Description:数据转换
     * @Param: [values, data, fields, contrastType, listName, forIndex]转换后数据 主数据 字段关系 对照关系 字段前缀名 循环次数
     * @return: void
     * @Author: qsx
     * @Date: 2022/3/22
     */
    public void changeData(HashMap<String, Object> values,Map<String, Object> data,Map<String, String> fields,int contrastType,String listName,Integer forIndex){

        for (Map.Entry<String, Object> dataEntry : data.entrySet()) {
            String dataKey = dataEntry.getKey();
            Object dataVal = dataEntry.getValue();
            //有下级处理
            if (dataVal instanceof ArrayList){
                //数据转化
                ArrayList<HashMap<String, Object>> list =(ArrayList<HashMap<String, Object>>)dataVal;
                //获取字段前缀名称 tableName.field -> tableName
                listName = dataKey;
                for (int j = 0; j < list.size(); j++) {
                    changeData(values, list.get(j), fields,contrastType,listName,j);
                }
            }else if (dataVal instanceof Map){
                //数据转化
                Map<String, Object> mapData = (Map<String, Object>)dataVal;
                changeData(values, mapData, fields,contrastType,listName,0);
            }

            //以下为存数据块
            String  field = fields.get(dataKey);
            if (field==null){
                continue;
            }
            //去掉前缀
            String keyName =  "";
            if (field.indexOf(".")>0){
                keyName =  field.substring(field.indexOf(".")+1);
            }else {
                keyName = field;
            }
            if (StringUtils.isNotBlank(listName) && forIndex!=null){
                //存入子集
                String newListName = contrastType==0?listName:"TABLENAME_"+listName;//创建名 为了易达的数据形式
                //创建容器
                if (values.get(newListName)==null){
                    values.put(newListName,new ArrayList<Map<String, Object>>());//预先创建装载子表信息的对象
                }
                if (((ArrayList<HashMap<String, Object>>)values.get(newListName)).get(forIndex)==null){
                    ((ArrayList<HashMap<String, Object>>)values.get(newListName)).add(forIndex,new HashMap<>());
                }
                ((ArrayList<HashMap<String, Object>>)values.get(newListName)).get(forIndex).put(keyName,dataVal);
            }else {
                //存入主表
                values.put(keyName,dataVal);
            }

        }
    }
    /**
     *
     * <AUTHOR>
     * @date 2022/3/22 15:51
     * @param externalSystem
     * @return java.lang.String
     */

    public String getToken(ExternalSystem externalSystem){
        String tokenUrl=externalSystem.getToken().substring(externalSystem.getToken().indexOf("(")+1,externalSystem.getToken().indexOf(")"));
        String[] split = externalSystem.getToken().split("\\(");
        String[] splitWto = split[0].split("\\.");
        Map<String,Object> paramsMap=new HashMap<String,Object>();
        paramsMap.put("userName", externalSystem.getUserName());
        paramsMap.put("password", externalSystem.getPassword());
        paramsMap.put("client_id", externalSystem.getClientId());
        paramsMap.put("client_secret", externalSystem.getClientSecret());
        paramsMap.put("grant_type", externalSystem.getGrantType());
        paramsMap.put("scope", externalSystem.getScope());
        String  tokenStr="";
        String tokenObj = "";
        // 获取第三方的token
        try {
            tokenStr= HttpUtil.get(tokenUrl,paramsMap);
            //解析返回
            JSONObject object=JSON.parseObject(tokenStr);
//            log.info("tokenStr->"+JSON.toJSONString(tokenStr));
            Map jsonToMap  =  JSONObject.parseObject(object.toJSONString());

            Map map=jsonToMap;
            for(int i=0;i<splitWto.length;i++) {
                if(i!=splitWto.length-1){
                    map=(Map)map.get(splitWto[i]);
                }else{
                    tokenObj=(String)map.get(splitWto[i]);
                }
            }
        } catch (Exception e) {
            tokenObj="外部系统token获取失败";
            e.printStackTrace();
        }

//        log.info("token-->"+tokenObj);
        return tokenObj;
    }

    /**
     * 驼峰转下划线
     * @param str
     * @return
     */
    public static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString().toUpperCase();
    }



    /***
     * 下划线命名转为驼峰命名(将数据字段名转换成实体类字段名)
     */
    public static String underlineToHump(String para){
        if(StringUtils.isEmpty(para)){
            return "";
        }
        StringBuilder result=new StringBuilder();
        String[] split=para.split("_");
        for(String s:split){
            if (!para.contains("_")) {
                result.append(s);
                continue;
            }
            if(result.length()==0){
                result.append(s.toLowerCase());
            }else{
                result.append(s.substring(0, 1).toUpperCase());
                result.append(s.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }





}
