package com.medusa.gruul.api.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.api.api.entity.FieldRelationship;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.api.model.dto.FieldRelationshipDto;

import java.util.List;

/**
 * @Description: 字段关系
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
public interface IFieldRelationshipService extends IService<FieldRelationship> {
    /**
     * 查询字段关系
     *
     * @param fieldRelationshipDto
     * @return com.medusa.gruul.api.api.entity.FieldRelationship
     */
    List<FieldRelationship> queryPageList(FieldRelationshipDto fieldRelationshipDto);

    /**
     * 保存字段
     *
     * @param fieldRelationshipDto
     */
    void retain(FieldRelationshipDto fieldRelationshipDto);

    /**
     * 根据接口ID删除字段
     *
     * @param externalSystemItemId
     */
    void deleteByItemId(Long externalSystemItemId);
}
