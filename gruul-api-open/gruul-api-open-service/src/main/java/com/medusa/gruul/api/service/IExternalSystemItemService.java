package com.medusa.gruul.api.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.api.api.entity.ExternalSystemItem;
import com.medusa.gruul.api.model.dto.ExternalSystemItemDto;
import com.medusa.gruul.api.model.vo.ExternalSystemItemVo;

import java.util.List;


/**
 * @Description: 外部系统子表
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
public interface IExternalSystemItemService extends IService<ExternalSystemItem> {
    IPage<ExternalSystemItemVo> selectList(ExternalSystemItemDto externalSystemItemDto);
    /**
     * @param externalSystemIdList 外部系统idList
     *
     * @return com.medusa.gruul.api.api.entity.ExternalSystemItem 外部系统子表
     */
    List<ExternalSystemItem> getByExternalSystemIds(List<Long> externalSystemIdList);

    List<ExternalSystemItem> newList();
    /**
     * @param externalSystemItem 外部系统添加的数据
     *
     */
    String externalAdd(ExternalSystemItem externalSystemItem);
}
