package com.medusa.gruul.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.api.api.entity.ExternalSystem;
import com.medusa.gruul.api.api.entity.ExternalSystemItem;
import com.medusa.gruul.api.model.dto.ExternalSystemItemDto;
import com.medusa.gruul.api.model.vo.ExternalSystemItemVo;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 外部系统子表
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
public interface ExternalSystemItemMapper extends BaseMapper<ExternalSystemItem> {
    /**
     * 获取外部系统子表列表
     *
     * @param page 分页数据
     * @param externalSystemItemDto        查询条件参数
     * @return com.medusa.gruul.api.model.vo.ExternalSystemItemVo
     */
    IPage<ExternalSystemItemVo> selectList(IPage page , @Param("externalSystemItemDto") ExternalSystemItemDto externalSystemItemDto);

    /**
     * 获取外部系统子表列表
     *
     * @param externalSystemIdList        查询条件参数
     * @return com.medusa.gruul.api.model.vo.ExternalSystemItemVo
     */
    List<ExternalSystemItem> getByExternalSystemIds( @Param("externalSystemIdList") List<Long> externalSystemIdList);

    List<ExternalSystemItem> listAll();

    List<ExternalSystem> newListWto();

    /**
     * @param externalSystemItem 外部系统添加的数据
     *
     */
    void itemAdd(ExternalSystemItem externalSystemItem);


}
