server:
  port: 10110

spring:
  application:
    name: api-open
  cloud:
    nacos:
      discovery:
        #nacos的ip
        server-addr: localhost:8848
        #自己电脑的ip
        ip: localhost
        namespace: ae67bdfd-0fa7-4f10-8a5f-15761be07d7b
      #        register-enabled: false
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ae67bdfd-0fa7-4f10-8a5f-15761be07d7b
        file-extension: yml
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  autoconfigure:
    exclude: org.springframework.cloud.gateway.config.GatewayAutoConfiguration,org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration
  profiles:
    active: open
logging:
  file: /log/mall/api-open/application.log
  pattern:
    file: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  level:
    com.alibaba.nacos: error
    root: debug
