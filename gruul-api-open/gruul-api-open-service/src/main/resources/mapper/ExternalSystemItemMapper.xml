<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.api.mapper.ExternalSystemItemMapper">

    <resultMap id="ExternalSystemItemVoMap" type="com.medusa.gruul.api.model.vo.ExternalSystemItemVo">
        <result column="id" property="id"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="localTableName" property="localTableName"/>
        <result column="address" property="address"/>
        <result column="name" property="name"/>
        <result column="time_interval" property="timeInterval"/>
        <result column="is_use" property="isUse"/>
        <result column="contrast_type" property="contrastType"/>
        <result column="externalSystemName" property="externalSystemName"/>
        <result column="contrastTypeName" property="contrastTypeName"/>
        <result column="default_value" property="defaultValue"/>
        <result column="table_name" property="tableName"/>
        <result column="sub_default_value" property="subDefaultValue"/>
        <result column="sub_table_name" property="subTableName"/>
    </resultMap>
    <select id="selectList" parameterType="Object"  resultMap="ExternalSystemItemVoMap">
        SELECT item.*,(select table_name as localTableName FROM onl_cgform_head as head where id=item.local_table_id) as localTableName,(select it.item_text FROM t_dict as dict left join t_dict_item as it on dict.id=it.dict_id where dict.dict_code="contrastType" and it.item_value=item.contrast_type) as contrastTypeName
        ,es.name as externalSystemName FROM t_external_system_item as item LEFT join t_external_system as es on es.id=item.external_system_id
        where item.is_deleted=0
        <if test="externalSystemItemDto.name!=null and externalSystemItemDto.name!=''">
            and item.name like CONCAT('%',#{externalSystemItemDto.name},'%')
        </if>
        <if test="externalSystemItemDto.localTableName!=null and externalSystemItemDto.localTableName!='' ">
            and item.local_table_name like CONCAT('%',#{externalSystemItemDto.localTableName},'%')
        </if>
        <if test="externalSystemItemDto.contrastType!=null  and externalSystemItemDto.contrastType!='' ">
            and item.contrast_type=#{externalSystemItemDto.contrastType}
        </if>
        <if test="externalSystemItemDto.externalSystemName!=null and externalSystemItemDto.externalSystemName!=''">
            and es.name like CONCAT('%',#{externalSystemItemDto.externalSystemName},'%')
        </if>
        <if test="externalSystemItemDto.isUse!=null and externalSystemItemDto.isUse!='' ">
            and item.is_use = #{externalSystemItemDto.isUse}
        </if>
    </select>
    <select id="getByExternalSystemIds" parameterType="Object"  resultType="com.medusa.gruul.api.api.entity.ExternalSystemItem">
        SELECT * FROM t_external_system_item
        where is_deleted=0
        and external_system_id in
        <foreach collection="externalSystemIdList" item="id"
                 index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>


    <select id="listAll"   resultType="com.medusa.gruul.api.api.entity.ExternalSystemItem">
        SELECT * FROM t_external_system_item
    </select>
    <select id="newListWto"   resultType="com.medusa.gruul.api.api.entity.ExternalSystem">
        SELECT * FROM t_external_system
    </select>


    <insert id="itemAdd" parameterType="Object" >
	  INSERT  INTO  t_external_system_item
					(

                     external_system_id
                     ,address
                     ,msgField

					)
			values (
                      #{externalSystemId,jdbcType=VARCHAR}
                     ,#{address,jdbcType=VARCHAR}
                    ,#{msgfield,jdbcType=VARCHAR}
                    					)
	</insert>


</mapper>