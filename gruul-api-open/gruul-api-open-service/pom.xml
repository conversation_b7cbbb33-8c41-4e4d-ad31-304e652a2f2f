<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-api-open</artifactId>
        <groupId>com.medusa</groupId>
        <version>0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-api-open-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-common-base-open</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>${wxjava.version}</version>
        </dependency>
        <!--api模块-->
        <!--商品模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-goods-open-api</artifactId>
            <version>${gruul.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-admin-starter-client</artifactId>
                    <groupId>de.codecentric</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-order-open-api</artifactId>
            <version>${gruul.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-admin-starter-client</artifactId>
                    <groupId>de.codecentric</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-shops-open-api</artifactId>
            <version>${gruul.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-admin-starter-client</artifactId>
                    <groupId>de.codecentric</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-platform-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-payment-open-api</artifactId>
            <version>${gruul.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-admin-starter-client</artifactId>
                    <groupId>de.codecentric</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--注册中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mp.version}</version>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--RocketMQ-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!--api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-sms-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--test-->
        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter-test -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter-test</artifactId>
            <version>3.4.0</version>
        </dependency>
        <!--h2 单元测试-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-api-open-api</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-api-open-api</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-service</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-service</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <!-- spring-boot-starter-web (spring-webmvc + tomcat) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- xxl-job-core -->
        <!-- https://mvnrepository.com/artifact/com.xuxueli/xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

        </plugins>
    </build>



</project>