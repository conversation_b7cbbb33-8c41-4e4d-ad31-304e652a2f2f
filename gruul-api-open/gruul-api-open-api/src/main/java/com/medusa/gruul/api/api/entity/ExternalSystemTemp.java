package com.medusa.gruul.api.api.entity;


import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;



/**
 * @Description: 外部系统临时表
 * @Author: qsx
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@Data
@TableName("t_external_system_temp")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_external_system_temp对象", description="外部系统临时表")
public class ExternalSystemTemp extends BaseEntity {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人Id*/
    @ApiModelProperty(value = "创建人Id")
	private Long createUserId;
	/**修改人Id*/
    @ApiModelProperty(value = "修改人Id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**其他系统传来的数据id*/
    @ApiModelProperty(value = "其他系统传来的数据id")
	private String externalDataId;
	/**本系统的数据id*/
    @ApiModelProperty(value = "本系统的数据id")
	private Long localDataId;
	/**外部系统id*/
    @ApiModelProperty(value = "外部系统id")
	private Long externalSystemId;
	/**外部系统明细id*/
    @ApiModelProperty(value = "外部系统明细id")
	private Long externalSystemItemId;
	/**最后修改时间*/
    @ApiModelProperty(value = "最后修改时间")
	private Date lastSendDate;
}
