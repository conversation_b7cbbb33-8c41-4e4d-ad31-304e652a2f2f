package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:35 2025/5/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order_delivery_item")
@ApiModel(value = "OrderDeliveryItem对象", description = "订单收货明细信息表")
public class OrderDeliveryItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单物流id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @TableField("main_id")
    private Long mainId;
    /**
     * 商铺ID
     */
    @ApiModelProperty(value = "商铺ID")
    @TableField("shop_id")
    private String shopId;
    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @TableField("order_id")
    private Long orderId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @TableField("product_id")
    private Long productId;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    @TableField("product_pic")
    private String productPic;


    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    @TableField("product_name")
    private String productName;

    /**
     * 商品sku编号
     */
    @ApiModelProperty(value = "商品sku编号")
    @TableField("product_sku_id")
    private Long productSkuId;

    /**
     * 商品sku条码
     */
    @ApiModelProperty(value = "商品sku条码")
    @TableField("product_sku_code")
    private String productSkuCode;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    @TableField("product_quantity")
    private Integer productQuantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    /**
     * 代发货订单ID
     */
    @ApiModelProperty(value = "代发货订单ID")
    @TableField("order_delivery_proxy_id")
    private Long orderDeliveryProxyId;

    /**
     * 代发货订单明细ID
     */
    @ApiModelProperty(value = "代发货订单明细ID")
    @TableField("order_delivery_proxy_item_id")
    private Long orderDeliveryProxyItemId;
}
