package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:22 2025/5/29
 */
@Data
@ApiModel("待发货订单明细新增dto")
public class OrderDeliveryProxyItemDto {


    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @NotNull(message = "商品id不能为空")
    private Long productId;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String productPic;


    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String productName;

    /**
     * 商品sku编号
     */
    @ApiModelProperty(value = "商品sku编号")
    @NotNull(message = "商品sku编号不能为空")
    private Long productSkuId;

    /**
     * 商品sku条码
     */
    @ApiModelProperty(value = "商品sku条码")
    private String productSkuCode;

    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @NotNull(message = "发货数量不能为空")
    private Integer productQuantity;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    @NotNull(message = "店铺id不能为空")
    private String shopId;

}
