package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * The type Manage order overview vo.
 * <p>
 * 交易概况
 *
 * <AUTHOR>
 * @date 2022 /06/10 11:20
 */
@Data
@ApiModel("交易概况")
public class ManageOrderTradeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "浏览量")
    private Integer views;

    /**
     * 交易量
     */
    @ApiModelProperty(value = "交易量")
    private Integer transactionVolume;

    /**
     * 待签收订单
     */
    @ApiModelProperty(value = "交易金额")
    private BigDecimal turnover;

    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    private String date;

    /**
     * 新增客户数
     */
    @ApiModelProperty(value = "新增客户数")
    private Integer addCustom;

    /**
     * 复购客户数
     */
    @ApiModelProperty(value = "复购客户数")
    private Integer againBuyCustom;


    /**
     * 购买客户数
     */
    @ApiModelProperty(value = "购买客户数")
    private Integer buyCustom;
}
