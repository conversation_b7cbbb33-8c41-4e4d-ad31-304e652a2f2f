package com.medusa.gruul.order.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 管理端订单汇总报表返回结果
 */
@Data
@ApiModel(value = "管理端订单汇总报表返回结果")
public class ManageOrderStaticVo implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 记录id
     */
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 销售权益包数量
     */
    @ApiModelProperty(value = "销售权益包数量")
    private BigDecimal packageQty;

    /**
     * 销售权益包总金额
     */
    @ApiModelProperty(value = "销售权益包总金额")
    private BigDecimal packageAmount;

    /**
     * 销售普通商品数量
     */
    @ApiModelProperty(value = "销售普通商品数量")
    private BigDecimal productQty;

    /**
     * 销售普通商品总金额
     */
    @ApiModelProperty(value = "销售普通商品总金额")
    private BigDecimal productAmount;

    /**
     * 销售总数量
     */
    @ApiModelProperty(value = "销售总数量")
    private BigDecimal totalQty;

    /**
     * 销售总金额
     */
    @ApiModelProperty(value = "销售总金额")
    private BigDecimal totalAmount;


    /**
     * 新会员数量
     */
    @ApiModelProperty(value = "新会员数量")
    private Integer memberQty;


    /**
     * 权益包核销次数
     */
    @ApiModelProperty(value = "权益包核销次数")
    private Integer packageVerifyQty;


    /**
     * 权益包未核销次数
     */
    @ApiModelProperty(value = "权益包未核销次数")
    private Integer packageUnVerifyQty;

    /**
     * 是否末级
     */
    @ApiModelProperty(value = "是否末级")
    private String isCatalog;

}
