package com.medusa.gruul.order.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:23 2025/6/13
 */
@Data
@ApiModel(value = "管理端导出返回结果")
public class ManageOrderExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "原价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "优惠")
    private BigDecimal youhuiPrice;

    @ApiModelProperty(value = "扣减")
    private BigDecimal deductionPrice;

    @ApiModelProperty(value = "现价")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "发货状态")
    private String deliveryType;

    @ApiModelProperty(value = "客户名称")
    private String nikeName;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "推荐人")
    private String recommendName;


    @ApiModelProperty(value = "商家名称")
    private String shopName;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "卖点描述")
    private String saleDescribe;

    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品数量")
    private Integer productQuantity;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal realAmount;




    @ApiModelProperty(value = "仓库名称")
    private String warehouseFullName;

    @ApiModelProperty(value = "备注")
    private String note;


}
