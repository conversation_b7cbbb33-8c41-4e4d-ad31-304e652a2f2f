package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxy;
import com.medusa.gruul.order.model.OrderDeliveryProxyParam;
import com.medusa.gruul.order.model.OrderDeliveryProxyVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:13 2025/5/29
 */
public interface IOrderDeliveryProxyService extends IService<OrderDeliveryProxy> {

    /**
     * 后台分页查询待发货列表
     * @param param
     * @param param
     * @return
     */
    Page<OrderDeliveryProxyVo> getOrderDeliveryProxy(OrderDeliveryProxyParam param);

}
