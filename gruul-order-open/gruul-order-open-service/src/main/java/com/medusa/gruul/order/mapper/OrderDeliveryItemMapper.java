package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.order.api.entity.OrderDeliveryItem;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:41 2025/5/28
 */
@Mapper
public interface OrderDeliveryItemMapper extends BaseMapper<OrderDeliveryItem> {

    /**
     * 根据主表id获取发货详情
     * @param mainId
     * @return
     */
    List<ManageOrderDeliveryItemVo>getManageOrderDeliveryItem(@Param("mainId") Long mainId);

}
