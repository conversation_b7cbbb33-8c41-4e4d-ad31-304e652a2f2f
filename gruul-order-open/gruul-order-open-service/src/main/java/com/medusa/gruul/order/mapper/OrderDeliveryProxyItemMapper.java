package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxyItem;
import com.medusa.gruul.order.model.BatchDeliveryProxyItemVo;
import com.medusa.gruul.order.model.OrderDeliveryProxyItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:18 2025/5/29
 */
@Mapper
public interface OrderDeliveryProxyItemMapper extends BaseMapper<OrderDeliveryProxyItem> {

    /**
     * 根据待发货订单主表信息获取明细表数据
     * @param mainId
     * @return
     */
    List<OrderDeliveryProxyItemVo>getOrderDeliveryProxyItem(@Param("mainId") Long mainId);

    /**
     * 根据代发货订单id获取代发货订单详情内容
     * @param ids
     * @return
     */
    List<BatchDeliveryProxyItemVo> getBatchDeliveryProxy(@Param("ids")List<Long> ids);
}
