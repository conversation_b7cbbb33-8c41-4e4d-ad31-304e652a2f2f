package com.medusa.gruul.order.model;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:53 2024/10/31
 */
@Data
@ApiModel(value = "管理后台订单明细查询条件")
public class ManageOrderItemParam extends QueryParam {

    @ApiModelProperty(value = "下单开始日期,格式2019-10-10")
    private String startDate;
    @ApiModelProperty(value = "下单结束日期,格式2019-10-10")
    private String endDate;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "订单编号，精确搜索")
    private String orderId;
    @ApiModelProperty(value = "订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4" +
            ".已完成（用户已经签收）, 5.已关闭")
    private List<Integer> orderStatus;
    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    @ApiModelProperty(value = "订单状态")
    private List<Integer>orderStatusList;

    @ApiModelProperty(value = "用户名称")
    private String accountName;

    @ApiModelProperty(value = "买家昵称，模糊搜索")
    private String userName;

    @ApiModelProperty(value = "是否退款 -1：所有订单, 0. 否, 1.是")
    private Integer isRefund;

    @ApiModelProperty(value = "买家电话，模糊搜索")
    private String phone;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "收货人电话，模糊搜索")
    private String receiverPhone;
}
