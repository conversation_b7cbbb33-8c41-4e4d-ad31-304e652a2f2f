package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.entity.OrderProductEvaluate;
import com.medusa.gruul.order.api.model.ProductRateVo;
import com.medusa.gruul.order.model.ApiSearchProductEvaluateDto;
import com.medusa.gruul.order.model.UserEvaluateVo;

import java.util.List;

/**
 * The interface Order product evaluate service.
 * <AUTHOR>
 */
public interface IOrderProductEvaluateService extends IService<OrderProductEvaluate> {

    /**
     * 商品详情页评价概况
     *
     * @param productId the product id
     * @return com.medusa.gruul.order.model.UserEvaluateVo user evaluate vo
     * <AUTHOR>
     * @date 2020 /1/18 21:49
     */
    UserEvaluateVo userEvaluateOverview(String productId);

    /**
     * 商品详情页评价
     *
     * @param dto the dto
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2020 /1/18 22:14
     */
    PageUtils productEvaluate(ApiSearchProductEvaluateDto dto);

    /**
     * 获取商品评价分数
     *
     * @param productIds the product ids
     * @return java.util.List<com.medusa.gruul.order.api.model.ProductRateVo> list
     * <AUTHOR>
     * @date 2020 /2/7 20:22
     */
    List<ProductRateVo> productRate(List<Long> productIds);
}
