package com.medusa.gruul.order.model;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:49 2024/10/12
 */
@ApiModel("生成外部系统出库单单Param")
@Data
public class OutStockOutOrderParam extends QueryParam implements Serializable {
    private static final long serialVersionUID = 1L;

}
