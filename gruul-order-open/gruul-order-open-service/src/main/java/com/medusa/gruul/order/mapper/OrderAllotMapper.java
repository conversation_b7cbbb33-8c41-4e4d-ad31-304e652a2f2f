package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.order.api.entity.OrderAllot;
import com.medusa.gruul.order.model.OutOrderAllotParam;
import com.medusa.gruul.order.model.OutOrderAllotVo;
import com.medusa.gruul.order.model.OutStockOutOrderParam;
import com.medusa.gruul.order.model.OutStockOutOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:22 2024/12/25
 */
@Repository
public interface OrderAllotMapper extends BaseMapper<OrderAllot> {

    /**
     * 分页获取需要同步的出库单
     * @param page
     * @param param
     * @return
     */
    Page<OutOrderAllotVo> searchOutOrderAllot(Page page, @Param("param") OutOrderAllotParam param);


    /**
     * 批量修改调拨单发送状态
     * @param ids
     * @param sendStatus
     */
    void updateSendStatus(@Param(value = "ids")List<Long> ids,  @Param(value = "sendStatus")String sendStatus);

}
