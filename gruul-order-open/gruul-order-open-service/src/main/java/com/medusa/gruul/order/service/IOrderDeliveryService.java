package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.order.api.entity.OrderDelivery;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryVo;
import com.medusa.gruul.order.api.model.OrderDeliveryDto;
import com.medusa.gruul.order.model.ReceiverAddressDto;

/**
 * <p>
 * 订单物流信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
public interface IOrderDeliveryService extends IService<OrderDelivery> {

    /**
     * 修改收货人地址
     *
     * @param dto the dto
     * @return void
     * <AUTHOR>
     * @date 2020 /7/3 20:30
     */
    void updateReceiverAddress(ReceiverAddressDto dto);

    /**
     * 获取发货信息
     * @param orderId
     * @return
     */
    OrderDelivery getOrderDelivery(String orderId);

    /**
     * 更新快递单号（显示）
     * @param orderDeliveryDto
     * @return
     */
    Boolean updateOrderDeliveryDeliverySnShow(OrderDeliveryDto orderDeliveryDto);

    /**
     * 根据发货单id获取发货信息
     * @param id
     * @return
     */
    ManageOrderDeliveryVo getManageOrderDeliveryById(Long id);
}
