package com.medusa.gruul.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountExtends;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.ManageVerifyPackageGoodsStaticDto;
import com.medusa.gruul.account.api.model.vo.ManageVerifyPackageGoodsStaticVo;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.feign.RemoteAfsService;
import com.medusa.gruul.afs.api.model.AfsSimpleVo;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.DeliveryStatusEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.WxDeliverStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.order.mapper.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.mq.Sender;
import com.medusa.gruul.order.service.IManageOrderService;
import com.medusa.gruul.order.service.IOrderDeliveryProxyItemService;
import com.medusa.gruul.order.service.IOrderDeliveryProxyService;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.StoreFrontOrderVo;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.CommissionRuleTypeEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Slf4j
@Service
public class ManageOrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IManageOrderService {

    @Resource
    private Sender sender;
    @Resource
    private MiniOrderServiceImpl miniOrderService;
    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;
    @Resource
    private OrderEvaluateMapper orderEvaluateMapper;
    @Resource
    private OrderProductEvaluateMapper orderProductEvaluateMapper;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private RemoteShopsService remoteShopsService;
    @Resource
    private OrderSettingMapper orderSettingMapper;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Autowired
    private RemoteAfsService remoteAfsService;

    @Autowired
    private IOrderDeliveryProxyService orderDeliveryProxyService;

    @Autowired
    private IOrderDeliveryProxyItemService orderDeliveryProxyItemService;

    @Autowired
    private AfsOrderItemMapper afsOrderItemMapper;

    @Override
    public PageUtils searchOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                if (ObjectUtil.isNotNull(dto.getSendBillId()) && dto.getSendBillId() == -1) {
                    orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                } else {
                    orderStatusList.clear();
                }
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                break;
            default:
                break;
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        //近一个月->0; 近三个月->1; 全部->2;
        /*switch (dto.getQuicklyDate()) {
            case 1:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
            case 2:
                startDate = null;
                endDate = null;
                break;
            case 0:
            default:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -1).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
        }*/
        //判断是否为主店铺
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(dto.getApiShopId());
        }
        String shopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    shopIds.add(shopId);
                    dto.setShopIds(shopIds);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }
        Page<ManageOrderVo> page = baseMapper.searchManageOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        List<ManageOrderVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ManageOrderVo record : records) {
                if(StringUtil.isNotEmpty(record.getWarehouseId())){
                    Warehouse warehouse = remoteGoodsService.getWarehouseById(Long.valueOf(record.getWarehouseId()));
                    record.setWarehouse(warehouse);
                }
                if(StringUtil.isNotEmpty(record.getShopId())){
                    ShopsPartner shopsPartner1 = remoteShopsService.getByShopId(Long.valueOf(record.getShopId()));
                    if(shopsPartner1!=null&&!shopsPartner1.equals("")){
                        record.setShopName(shopsPartner1.getName());
                    }
                }

                List<ManageOrderDeliveryVo> orderDeliveryList = record.getOrderDeliveryList();
                if(orderDeliveryList.size()>1){
                    orderDeliveryList.remove(0);
                    Map<DeliverTypeEnum, List<ManageOrderDeliveryVo>> dataList = orderDeliveryList.stream().collect(Collectors.groupingBy(ManageOrderDeliveryVo::getDeliveryType));
                    List<DeliverTypeEnum>deliverTypeEnumList = new ArrayList<>();
                    for (Map.Entry<DeliverTypeEnum, List<ManageOrderDeliveryVo>> entry : dataList.entrySet()) {
                        DeliverTypeEnum key = entry.getKey();
                        deliverTypeEnumList.add(key);
                    }
                    record.setDeliverTypeEnumList(deliverTypeEnumList);
                    record.setOrderDeliveryList(orderDeliveryList);
                }else{
                    ManageOrderDeliveryVo manageOrderDeliveryVo = orderDeliveryList.get(0);
                    List<DeliverTypeEnum>deliverTypeEnumList = new ArrayList<>();
                    deliverTypeEnumList.add(manageOrderDeliveryVo.getDeliveryType());
                    record.setDeliverTypeEnumList(deliverTypeEnumList);
                }
                ManageOrderDeliveryVo manageOrderDeliveryVo = orderDeliveryList.get(0);
                record.setReceiverName(manageOrderDeliveryVo.getReceiverName());
                record.setReceiverPhone(manageOrderDeliveryVo.getReceiverPhone());
                record.setReceiverPostCode(manageOrderDeliveryVo.getReceiverPostCode());
                record.setReceiverProvince(manageOrderDeliveryVo.getReceiverProvince());
                record.setReceiverCity(manageOrderDeliveryVo.getReceiverCity());
                record.setReceiverRegion(manageOrderDeliveryVo.getReceiverRegion());
                record.setReceiverDetailAddress(manageOrderDeliveryVo.getReceiverDetailAddress());

                if(StringUtils.isNotEmpty(record.getOrderUserId())
                        &&record.getUserId()!=null&&!record.getUserId().equals("")
                        &&!record.getOrderUserId().equals(String.valueOf(record.getUserId()))){
                    record.setOtherOrder(CommonConstants.NUMBER_ONE);
                }else{
                    record.setOtherOrder(CommonConstants.NUMBER_ZERO);
                }

                // 处理订单项的数量展示逻辑
                List<SimpleOrderItemVo> itemVoList = record.getItemVoList();
                if(CollectionUtil.isNotEmpty(itemVoList)) {
                    for (SimpleOrderItemVo itemVo : itemVoList) {
                        ProductVo productVo = remoteGoodsService.findProductById(itemVo.getProductId());
                        if(productVo != null && productVo.getLimitType() != null &&
                                (productVo.getLimitType() == 1 || productVo.getLimitType() == 2)) {
                            // 多规格商品，获取SKU信息
                            SkuStock skuStock = remoteGoodsService.findSkuStockById(itemVo.getProductSkuId());
                            if(skuStock != null && skuStock.getReallyOutStock() != null && skuStock.getReallyOutStock() > 0) {
                                itemVo.setDisplayQuantity(skuStock.getReallyOutStockNum(itemVo.getProductQuantity()));
                            } else {
                                itemVo.setDisplayQuantity(itemVo.getProductQuantity());
                            }
                        } else {
                            // 单规格商品或商品信息获取失败，使用原始数量
                            itemVo.setDisplayQuantity(itemVo.getProductQuantity());
                        }
                    }
                }

            }
        }
        ShopContextHolder.setShopId(shopId);
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchOrderDetail(ManageOrderItemParam params) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        List<Integer> orderStatus = params.getOrderStatus();
        if (CollectionUtil.isNotEmpty(orderStatus)){
            for (Integer status : orderStatus) {
                switch (status) {
                    case -1:
                        orderStatusList.clear();
                        break;
                    case 0:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                        break;
                    case 1:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                        break;
                    case 2:
                        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                        break;
                    case 3:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                        break;
                    case 4:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                        break;
                    case 5:
                        orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                        break;
                    default:
                        break;
                }
            }
        }
        params.setOrderStatusList(orderStatusList);

        Page<ManageOrderItemVo> page = baseMapper.searchOrderItem(new Page<>(params.getCurrent(),params.getSize()),params);
        List<ManageOrderItemVo> list =page.getRecords();
        Map<Long, List<AfsOrderItem>> afsOrderItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)){
            List<Long> orderIds = list.stream().map(ManageOrderItemVo::getOrderId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orderIds)){
                LambdaQueryWrapper<AfsOrderItem> wrapper = new LambdaQueryWrapper<>();
                wrapper.select(AfsOrderItem::getOrderId,AfsOrderItem::getProductQuantity,AfsOrderItem::getRefundAmount);
                wrapper.in(AfsOrderItem::getOrderId,orderIds);
                ShopContextHolder.clear();
                List<AfsOrderItem> afsOrderItems = afsOrderItemMapper.selectList(wrapper);
                if (CollectionUtil.isNotEmpty(afsOrderItems)){
                    afsOrderItemMap  = afsOrderItems.stream().collect(Collectors.groupingBy(AfsOrderItem::getOrderId));
                }

            }
        }
        // 计算退款数
        for (ManageOrderItemVo item : list) {
            BigDecimal refundAmount = BigDecimal.ZERO;
            BigDecimal refundProductQuantity = BigDecimal.ZERO;
            List<AfsOrderItem> afsOrderItemList = afsOrderItemMap.get(item.getOrderId());
            if (CollectionUtil.isNotEmpty(afsOrderItemList)){
                for (AfsOrderItem orderItem : afsOrderItemList) {
                    refundProductQuantity = refundProductQuantity.add(new BigDecimal(orderItem.getProductQuantity()+""));
                    refundAmount = refundAmount.add(orderItem.getRefundAmount());
                }
            }
            item.setRefundRealAmount(refundAmount);
            item.setRefundProductQuantity(refundProductQuantity);
            //发货方式
            String warehouseFullName = "";
            String deliverTypeName = "";
            if(item.getOrderDeliveryList()!=null&&item.getOrderDeliveryList().size()>0){
                List<ManageOrderDeliveryVo> orderDeliveryList = item.getOrderDeliveryList();
                orderDeliveryList.remove(0);
                for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
                    if(warehouseFullName.length()>0){
                        warehouseFullName = warehouseFullName + "，";
                    }
                    if(deliverTypeName.length()>0){
                        deliverTypeName = deliverTypeName + "，";
                    }
                    if(manageOrderDeliveryVo.getWarehouseId()!=null){
                        Warehouse warehouse = remoteGoodsService.getWarehouseById(manageOrderDeliveryVo.getWarehouseId());
                        warehouseFullName = warehouseFullName + warehouse.getWarehouseFullName();
                    }
                    deliverTypeName = deliverTypeName + manageOrderDeliveryVo.getDeliveryType().getDesc();
                }
            }
            item.setWarehouseName(warehouseFullName);
            item.setDeliverTypeName(deliverTypeName);

        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrderByReceived(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());//待卖家发货
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());//配送中
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());//等待买家取货
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());//等待评价
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());//订单已完成
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());//审核通过
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchOutReceiveOrder(OutReceiveOrderParam param) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());//待卖家发货
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());//配送中
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());//等待买家取货
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());//等待评价
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());//订单已完成
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());//审核通过
        Page<OutReceiveOrderVo> page = baseMapper.searchOutReceiveOrder(new Page(param.getCurrent(), param.getSize()), orderStatusList);
        List<OutReceiveOrderVo> list = page.getRecords();
        List<Long>idList=list.stream().map(OutReceiveOrderVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateReceiveSyncStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(List<Long> orderIds) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canCancel(order.getStatus())) {
                throw new ServiceException("当前状态不能取消此订单");
            }
        }
        for (Order order : orders) {
            miniOrderService.cancelOrder(order.getId(), order, OrderStatusEnum.SELLER_CANCEL_CLOSE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverMessage(Long[] orderIds) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(orderIds!=null&&orderIds.length>0){
            List<Long> list = Arrays.asList(orderIds);
            for (Long orderId : list) {
                OrderVo orderVo = baseMapper.selectOrderVoById(orderId);
                sender.sendDeliveryGoodsMessage(orderVo);
            }
        }else{
            LambdaQueryWrapper<Order>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getStatus,OrderStatusEnum.WAIT_FOR_SEND);
            wrapper.isNotNull(Order::getShopId);
            List<Order> orderList = baseMapper.selectList(wrapper);
            if(orderList!=null&&orderList.size()>0){
                for (Order order : orderList) {
                    OrderVo orderVo = baseMapper.selectOrderVoById(order.getId());
                    sender.sendDeliveryGoodsMessage(orderVo);
                }
            }
        }
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public Integer vailDeliveryByOrderId(String orderId) {
        Integer result = 0;
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        String shopsPartnerShopId = shopsPartner.getShopId();
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Order order = this.baseMapper.selectById(orderId);
        String shopId = order.getShopId();

        //获取订单特殊配置
        List<SpecialSetting> list = remoteMiniInfoService.getSpecialSettingByShopId(shopId);
        if(list!=null&&list.size()>0){
            SpecialSetting specialSetting = list.get(0);
            Integer platformProxyShipping = specialSetting.getPlatformProxyShipping();
            if(platformProxyShipping==0){//是否允许平台下单
                //为否判断当前订单是否属于当前店铺订单
                if(shopsPartnerShopId.equals(shopId)){
                    result = 1;//允许发货
                }else{
                    result = 2;//其他商户订单不允许发货
                }
            }else{
                //为是判断当前用户是否为主店铺
                if(mainFlag==1){
                    result = 1;
                }else{
                    result = 3;//不是主店铺，不允许发货
                }
            }
        }else{
            ShopContextHolder.setShopId(oldShopId);
            throw new ServiceException("特殊配置不存在，不允许发货");
        }

        ShopContextHolder.setShopId(oldShopId);
        return result;
    }

    @Override
    public Integer vailDelivery(String orderIds) {
        Integer result = 0;
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        String shopsPartnerShopId = shopsPartner.getShopId();
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        for (String orderId : orderIds.split(",")) {
            Order order = this.baseMapper.selectById(orderId);
            String shopId = order.getShopId();
            List<SpecialSetting> list = remoteMiniInfoService.getSpecialSettingByShopId(shopId);
            if(list!=null&&list.size()>0){
                SpecialSetting specialSetting = list.get(0);
                Integer platformProxyShipping = specialSetting.getPlatformProxyShipping();
                if(platformProxyShipping==0){//是否允许平台下单
                    //为否判断当前订单是否属于当前店铺订单
                    if(shopsPartnerShopId.equals(shopId)){
                        result = 1;//允许发货
                    }else{
                        ShopContextHolder.setShopId(oldShopId);
                        throw new ServiceException("其他商户订"+orderId+"不允许平台发货");
                    }
                }else{
                    //为是判断当前用户是否为主店铺
                    if(mainFlag==1){
                        result = 1;
                    }else{
                        ShopContextHolder.setShopId(oldShopId);
                        throw new ServiceException("不是主店铺，不允许发货");
                    }
                }
            }else{
                ShopContextHolder.setShopId(oldShopId);
                throw new ServiceException("特殊配置不存在，不允许发货");
            }
        }
        ShopContextHolder.setShopId(oldShopId);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paymentOrder(List<Long> orderIds) {
        //线下支付可以支付其他门店订单
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canPay(order.getStatus())) {
                throw new ServiceException("当前状态不能支付此订单");
            }
        }
        for (Order order : orders) {
            miniOrderService.paymentOrder(order.getId(), order, OrderStatusEnum.WAIT_FOR_SEND);
        }
        ShopContextHolder.setShopId(shopId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSendStatus(List<Long> orderIds,String sendStatus) {
        baseMapper.updateSendStatus(orderIds,sendStatus);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReceiveSyncStatus(List<Long> orderIds,String receiveSyncStatus) {
        baseMapper.updateReceiveSyncStatus(orderIds,receiveSyncStatus);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStock(Long orderId) {
        Order order = baseMapper.selectById(orderId);
//        for (Order order : orders) {
//            if (!OrderStatusEnum.canCancel(order.getStatus())) {
//                throw new ServiceException("当前状态不能取消此订单");
//            }
//        }
//        for (Order order : orders) {
//            miniOrderService.paymentOrder(order.getId(), order, OrderStatusEnum.WAIT_FOR_SEND);
//        }
    }

    @Override
    public void noteOrder(List<Long> orderIds, String note, Boolean isOver) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            StringBuffer sb = new StringBuffer();
            if (StrUtil.isBlank(note)) {
                note = "";
            }
            if (!isOver) {
                if (StrUtil.isNotBlank(order.getNote())) {
                    order.setNote(sb.append(order.getNote()).append(StrUtil.CRLF).append(note).toString());
                } else {
                    order.setNote(note);
                }
            } else {
                order.setNote(note);
            }
        }
        this.updateBatchById(orders);
    }


    @Override
    public PageUtils searchOrderEvaluate(ManageSearchEvaluateDto dto) {
        Page<ManageEvaluateVo> page = orderEvaluateMapper.searchOrderEvaluate(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        for (ManageEvaluateVo record : page.getRecords()) {
            record.setRate(record.getShopRate());
        }
        return new PageUtils(page);
    }

    @Override
    public void choiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            productEvaluate.setChoice(true);
            orderProductEvaluateMapper.updateById(productEvaluate);
        }
    }

    @Override
    public void replyEvaluate(Long id, String reply) {
        OrderProductEvaluate productEvaluate = orderProductEvaluateMapper.selectById(id);
        productEvaluate.setReply(reply);
        orderProductEvaluateMapper.updateById(productEvaluate);
    }

    @Override
    public OrderVo orderInfo(Long orderId) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        OrderVo orderVo = baseMapper.selectOrderVoById(orderId);

        for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
            Long productSkuId = orderItemVo.getProductSkuId();
            if (productSkuId==null || orderItemVo.getProductQuantity() ==null)
                continue;
            SkuStock skuStockById = remoteGoodsService.findSkuStockById(productSkuId);
            orderItemVo.setProductQuantity(skuStockById.getReallyOutStockNum(orderItemVo.getProductQuantity()));
        }

        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        if(orderDeliveryList.size()>0){
            orderDeliveryList.remove(0);
            orderVo.setOrderDeliveryList(orderDeliveryList);
        }
        if(StringUtils.isNotEmpty(orderVo.getOrderUserId())
                &&StringUtils.isNotEmpty(orderVo.getUserId())
                &&!orderVo.getOrderUserId().equals(orderVo.getUserId())){
            orderVo.setOtherOrder(CommonConstants.NUMBER_ONE);
        }else{
            orderVo.setOtherOrder(CommonConstants.NUMBER_ZERO);
        }
        String shopId = orderVo.getShopId();
        ShopContextHolder.setShopId(shopId);

        if(orderVo!=null&&orderVo.getWarehouseId()!=null){
            Warehouse warehouse = remoteGoodsService.getWarehouseById(orderVo.getWarehouseId());
            if(warehouse!=null){
                orderVo.setWarehouseAddress(warehouse.getWarehouseAddress());
                orderVo.setWarehouseFullName(warehouse.getWarehouseFullName());
            }
        }
        if(orderVo!=null&&orderVo.getStoreFrontId()!=null){
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getPlatformDepartmentByStoreFrontId(orderVo.getStoreFrontId());
            if(storeFrontOrderVo!=null){
                orderVo.setStoreFrontName(storeFrontOrderVo.getStoreFrontName());
                orderVo.setStoreFrontCode(storeFrontOrderVo.getStoreFrontCode());
            }
        }

        OrderSetting orderSetting = orderSettingMapper.selectOne(null);

        LambdaQueryWrapper<OrderEvaluate>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderEvaluate::getOrderId,orderVo.getId());
        List<OrderEvaluate> orderEvaluateList = orderEvaluateMapper.selectList(wrapper);
        if(orderEvaluateList!=null&&orderEvaluateList.size()>0){
            OrderEvaluate orderEvaluate = orderEvaluateList.get(0);
            String effectImageUrl = orderEvaluate.getEffectImageUrl();
            if(StringUtils.isNotEmpty(effectImageUrl)){
                String[] split = effectImageUrl.split(",");
                if(split.length < orderSetting.getUploadPicNum()){
                    orderVo.setUploadEffectFlag(CommonConstants.NUMBER_ONE);
                }
                List<String>effectImageUrlList = new ArrayList<>(Arrays.asList(split));
                orderVo.setEffectImgUrlList(effectImageUrlList);
            }
        }

        ShopContextHolder.setShopId(oldShopId);



        return orderVo;
    }

    @Override
    public void unChoiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            if (productEvaluate.getChoice()) {
                productEvaluate.setChoice(false);
                orderProductEvaluateMapper.updateById(productEvaluate);
            }
        }
    }

    @Override
    public PageUtils searchLogisticsOrderList(ManageLogisticsOrderDto dto) {
        Page<ManageDeliveryOrderVo> page = baseMapper.searchLogisticsOrderList(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        return new PageUtils(page);

    }

    @Override
    public ManageOrderOverviewVo getOverview() {
        ManageOrderOverviewVo vo = new ManageOrderOverviewVo();
        Integer waitForPay = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_PAY));
        Integer waitForSend = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_SEND));
        Integer shipped = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.SHIPPED));
        Integer waitForPickup = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_PICKUP));
        vo.setWaitForPay(waitForPay);
        vo.setWaitForSend(waitForSend);
        vo.setShipped(shipped);
        vo.setWaitForPickup(waitForPickup);
        return vo;
    }
    @Override
    public ManageOrderRealTimeOverviewVo getRealTimeOverview(String startDate,String endDate,Integer type) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date date1 = dateFormat.parse(startDate);
        Date date2 = dateFormat.parse(endDate);

        startDate =dateFormat.format(date1);
        startDate=startDate+" 00:00:00";
        endDate =dateFormat.format(date2);
        endDate=endDate+" 23:59:59";

        List<ManageOrderTradeDto> dbTradeDtoList = baseMapper.transacTionOverview(startDate, endDate, OrderStatusEnum.getPaidStatus());
        List<ManageOrderTradeDto> tradeDtoList = new ArrayList<>();
        while(date1.compareTo(date2) <= 0){
            Date finalDate = date1;
            List<ManageOrderTradeDto> existList = dbTradeDtoList.stream().filter(e -> {
                try {
                    return dateFormat.parse(e.getDate()).compareTo(finalDate) == 0;
                } catch (ParseException parseException) {
                    parseException.printStackTrace();
                }
                return false;
            }).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(existList)){
                ManageOrderTradeDto newDto = new ManageOrderTradeDto();
                newDto.setDate(dateFormat.format(date1));
                newDto.setTransactionVolume(0);
                newDto.setTurnover(new BigDecimal("0"));
                tradeDtoList.add(newDto);
            }else{
                tradeDtoList.addAll(existList);
            }
            date1 = DateUtil.offsetDay(date1, 1);
        }
        Integer num=0;
        HashMap<String,String> map=new HashMap<>();
//       获取当前日期
        Date date = new Date();
        String newData= dateFormat.format(date);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();
        //昨天
        String yesterDay = dateFormat.format(yesterday);

        ManageOrderRealTimeOverviewVo manageOrderRealTimeOverviewVo=new ManageOrderRealTimeOverviewVo();
        manageOrderRealTimeOverviewVo.setUpdateTime(date);
        ManageOrderTradeDto trade=new ManageOrderTradeDto();
        ManageOrderTradeDto yesterdayTrade=new ManageOrderTradeDto();
        if(type==1){
            //今天
            map.put("startDate",newData+" 00:00:00");
            map.put("endDate",newData+" 23:59:59");
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(newData+" 00:00:00",newData+" 23:59:59", OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAgainBuyCustom(againBuyCustom);

            Integer buyCustom = getBuyCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setBuyCustom(buyCustom);

            //昨天
            map.put("startDate",yesterDay+" 00:00:00");
            map.put("endDate",yesterDay+" 23:59:59");
            Integer yesterNum=remoteMiniAccountService.getFootprintNumber(map);
            yesterdayTrade=baseMapper.transacTion(yesterDay+" 00:00:00",yesterDay+" 23:59:59", OrderStatusEnum.getPaidStatus());
            yesterdayTrade.setViews(yesterNum);

            Integer addCustom2 = remoteMiniAccountService.getAddCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAddCustom(addCustom2);

            Integer againBuyCustom2 = getAgainBuyCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAgainBuyCustom(againBuyCustom2);

            Integer buyCustom2 = getBuyCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setBuyCustom(buyCustom2);
        }else {
            //查询日期的总和
            map.put("startDate",startDate);
            map.put("endDate",endDate);
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(startDate,endDate, OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(startDate, endDate);
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(startDate, endDate);
            trade.setAgainBuyCustom(againBuyCustom);
            Integer buyCustom = getBuyCustom(startDate, endDate);
            trade.setBuyCustom(buyCustom);
        }
        manageOrderRealTimeOverviewVo.setTradeLineVos(tradeDtoList);
        manageOrderRealTimeOverviewVo.setYesterdayTrade(yesterdayTrade);
        manageOrderRealTimeOverviewVo.setTrade(trade);
        return manageOrderRealTimeOverviewVo;
    }
    @Override
    public Integer getBuyCustom(String startDate, String endDate) {
        List<Map> list = this.baseMapper.getBuyCustom(startDate, endDate);
        if(list!=null&&list.size()>0){
            return list.size();
        }else{
            return 0;
        }
    }

    @Override
    public List<ManageOrderVo> searchLogisticsOrder(ManageSearchLogisticsOrderDto dto) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Long> orderIds = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getOrderIds())) {
            orderIds =
                    Arrays.stream(dto.getOrderIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }

        List<ManageOrderVo> list = baseMapper.searchLogisticsOrder(orderIds);
        if(list!=null&&list.size()>0){
            for (ManageOrderVo manageOrderVo : list) {
                List<ManageOrderDeliveryVo> orderDeliveryList = manageOrderVo.getOrderDeliveryList();
                if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
                    ManageOrderDeliveryVo manageOrderDeliveryVo = orderDeliveryList.get(0);
                    manageOrderVo.setReceiverCity(manageOrderDeliveryVo.getReceiverCity());
                    manageOrderVo.setReceiverProvince(manageOrderDeliveryVo.getReceiverProvince());
                    manageOrderVo.setReceiverName(manageOrderDeliveryVo.getReceiverName());
                    manageOrderVo.setReceiverPhone(manageOrderDeliveryVo.getReceiverPhone());
                    manageOrderVo.setReceiverDetailAddress(manageOrderDeliveryVo.getReceiverDetailAddress());
                    manageOrderVo.setReceiverRegion(manageOrderDeliveryVo.getReceiverRegion());
                    manageOrderVo.setReceiverPostCode(manageOrderDeliveryVo.getReceiverPostCode());
                }
            }
        }


        ShopContextHolder.setShopId(oldShopId);
        return list;
    }

    @Override
    public Integer countLogisticsWaitSend() {
        Integer waitForSend = baseMapper.countLogisticsWaitSend();
        return waitForSend;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logisticsSend(ManageSearchLogisticsOrderDto dto) {
        List<Long> orderIds =
                Arrays.stream(dto.getOrderIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        //修改库存
        for (int i = 0; i < orderIds.size(); i++) {
            List<ProductStock> stockList = new ArrayList<>();
            List<OrderItem> orderItemList = remoteOrderService.orderItemByOrderIds(orderIds.get(i));
            Map orderItemMap = new HashMap();
            List<Long> skuIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                skuIds = orderItemList.stream().map(OrderItem::getProductSkuId).collect(Collectors.toList());
                orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getProductSkuId, v -> v));
            }
            List<ProductStock> productStockList = remoteGoodsService.productStockBySkuIds(skuIds, dto.getWarehouseId());
            if (CollectionUtils.isEmpty(productStockList)) {
                throw new ServiceException("库存数量不足，发货失败！");
            }
            for (int j = 0; j < productStockList.size(); j++) {
                ProductStock productStock = productStockList.get(j);
                OrderItem orderItem = (OrderItem) orderItemMap.get(productStock.getSkuId());
                //库存数据
                Integer stock = productStock.getStock().intValue();
                //订单数量
                Integer quantity = orderItem.getProductQuantity();
                //剩余数量
                Integer nuber = stock - quantity;
                productStock.setStock(new BigDecimal(nuber));
                stockList.add(productStock);
                if (nuber <= 0) {
                    throw new ServiceException("库存数量不足，发货失败！");
                }
            }
            boolean success=remoteGoodsService.batchProductSubtractStock(stockList);
            boolean fl=remoteOrderService.updateOrderWarehouse(orderIds.get(i),dto.getWarehouseId());
            if(!success && !fl){
                throw new ServiceException("修改库存失败");
            }
        }
        //获取订单设置
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        OrderDelivery orderDelivery = new OrderDelivery();
        //订单收货
        orderDelivery.setDeliveryCompany("无");
        orderDelivery.setDeliverySn("无");
        orderDelivery.setDeliveryTime(LocalDateTime.now());
        orderDeliveryMapper.update(orderDelivery, new LambdaQueryWrapper<OrderDelivery>().in(OrderDelivery::getOrderId,
                orderIds));
        Order order = new Order();
        order.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP);
        baseMapper.update(order, new LambdaQueryWrapper<Order>().in(Order::getId, orderIds));

        for (Long orderId : orderIds) {
            OrderVo vo = baseMapper.selectOrderVoById(orderId);
            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(vo.getUserId(), Arrays.asList(4));
            //历史系统方法-先去掉
            //sender.sendDeliveryMessage(vo, accountInfoDto.getMiniAccountOauths().getOpenId());
            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(vo.getId());
            baseOrderMessage.setShopId(vo.getShopId());
            baseOrderMessage.setTenantId(vo.getTenantId());
            sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                    orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchLogisticsSend(List<ManageSearchLogisticsOrderBatchDto> manageSearchLogisticsOrderBatchDtoList) {
        for (ManageSearchLogisticsOrderBatchDto manageSearchLogisticsOrderBatchDto : manageSearchLogisticsOrderBatchDtoList) {
            String shopId = ShopContextHolder.getShopId();
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
            //修改库存
            List<ProductStock> stockList = new ArrayList<>();
            List<OrderItem> orderItemList = remoteOrderService.orderItemByOrderIds(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()));
            Map orderItemMap = new HashMap();
            List<Long> skuIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                skuIds = orderItemList.stream().map(OrderItem::getProductSkuId).collect(Collectors.toList());
                orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getProductSkuId, v -> v));
            }
            List<ProductStock> productStockList = remoteGoodsService.productStockBySkuIds(skuIds, manageSearchLogisticsOrderBatchDto.getWarehouseId());
            if (CollectionUtils.isEmpty(productStockList)) {
                throw new ServiceException("库存数量不足，发货失败！");
            }
            for (int j = 0; j < productStockList.size(); j++) {
                ProductStock productStock = productStockList.get(j);
                OrderItem orderItem = (OrderItem) orderItemMap.get(productStock.getSkuId());
                //库存数据
                Integer stock = productStock.getStock().intValue();
                //订单数量
                Integer quantity = orderItem.getProductQuantity();
                //剩余数量
                Integer nuber = stock - quantity;
                productStock.setStock(new BigDecimal(nuber));
                stockList.add(productStock);
                if (nuber <= 0) {
                    throw new ServiceException("库存数量不足，发货失败！");
                }
            }
            boolean success=remoteGoodsService.batchProductSubtractStock(stockList);
            boolean fl=remoteOrderService.updateOrderWarehouse(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()),manageSearchLogisticsOrderBatchDto.getWarehouseId());
            if(!success && !fl){
                throw new ServiceException("修改库存失败");
            }
            //获取订单设置
            OrderSetting orderSetting = orderSettingMapper.selectOne(null);
            OrderDelivery orderDelivery = new OrderDelivery();
            //订单收货
            orderDelivery.setDeliveryCompany("无");
            orderDelivery.setDeliverySn("无");
            orderDelivery.setDeliveryTime(LocalDateTime.now());
            orderDelivery.setDeliveryType(DeliverTypeEnum.NO);
            orderDeliveryMapper.update(orderDelivery, new LambdaQueryWrapper<OrderDelivery>().eq(OrderDelivery::getOrderId,
                    manageSearchLogisticsOrderBatchDto.getOrderId()));
            Order order = new Order();
            order.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP);
            baseMapper.update(order, new LambdaQueryWrapper<Order>().eq(Order::getId, manageSearchLogisticsOrderBatchDto.getOrderId()));
            OrderVo vo = baseMapper.selectOrderVoById(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()));
            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(vo.getUserId(), Arrays.asList(4));
            //历史系统方法-先去掉
            //sender.sendDeliveryMessage(vo, accountInfoDto.getMiniAccountOauths().getOpenId());
            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(vo.getId());
            baseOrderMessage.setShopId(vo.getShopId());
            baseOrderMessage.setTenantId(vo.getTenantId());
            sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                    orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
            //订单发货消息
            sender.sendShippedOrderMessage(vo);

            ShopContextHolder.setShopId(shopId);
        }
    }
    /**
     * 商家批量修改审核通过订单
     * @param orderIds the order ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvedOrder(List<Long> orderIds) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canApproved(order.getStatus())) {
                throw new ServiceException("当前状态不能审核此订单[" + order.getId() + "]");
            }
        }
        for (Order order : orders) {
            order.setStatus(OrderStatusEnum.APPROVED);
            order.setApprovedTime(LocalDateTime.now());
        }
        this.updateBatchById(orders);
    }

    @Override
    public List<Order> getOrderByWxDeliverStatus() {
        LambdaQueryWrapper<Order>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getWxDeliverStatusEnum, WxDeliverStatusEnum.DELIVER);
        List<Order> list = this.list(wrapper);
        return list;
    }

    @Override
    public Integer getAgainBuyCustom(String startDate, String endDate) {

        List<Map> list = this.baseMapper.getAgainBuyCustom(startDate, endDate);
        if(list!=null&&list.size()>0){
            return list.size();
        }else{
            return 0;
        }
    }

    @Override
    public BigDecimal getCommissionByOrderId(Long orderId,Integer type) {
        BigDecimal amount = BigDecimal.ZERO;
        List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItem orderItem : orderItemList) {
                Long productId = orderItem.getProductId();
                ProductVo product = remoteGoodsService.findProductById(productId);
                Integer ruleType = product.getRuleType();
                Integer productQuantity = orderItem.getProductQuantity();
                if(ruleType!=null){
                    if(type==CommonConstants.NUMBER_ZERO){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                    if(type==CommonConstants.NUMBER_ONE){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                }
            }
        }

        return amount;
    }

    /**
     * 按门店、员工统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public Page manageOrderStatic(ManageOrderStaticDto manageOrderStaticDto) {
        if(null == manageOrderStaticDto || (StrUtil.isBlank(manageOrderStaticDto.getParentCode()) && StrUtil.isBlank(manageOrderStaticDto.getStoreName()))){
            manageOrderStaticDto.setFirstClassFlag(1);
        }
        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        manageOrderStaticDto.setType(0);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        if(StrUtil.isNotBlank(manageOrderStaticDto.getParentCode())){
            // 查询门店编码是否是末级，如果是末级，则表示查询的是员工的统计数据
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getStoreFrontByClassCode(manageOrderStaticDto.getParentCode());
            if(null != storeFrontOrderVo && "0".equalsIgnoreCase(storeFrontOrderVo.getIsCatalog())){
                manageOrderStaticDto.setType(1);
            }
        }
        if(StrUtil.isBlank(manageOrderStaticDto.getParentCode())){
            manageOrderStaticDto.setParentCode("-1");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.manageOrderStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        // 查询门店、员工的核销数量
        ManageVerifyPackageGoodsStaticDto dto = new ManageVerifyPackageGoodsStaticDto();
        dto.setType(manageOrderStaticDto.getType());
        if(dto.getType() == 0){
            // 门店，获取pageVo.getRecords()里的id组成list
            dto.setClassCodeList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }else{
            // 员工
            dto.setEmployeeIdList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }
        dto.setPackageName(manageOrderStaticDto.getPackageName());
        dto.setStartTime(manageOrderStaticDto.getStartTime());
        dto.setEndTime(manageOrderStaticDto.getEndTime());
        List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStaticVos = remoteMiniAccountService.listVerifyPackageCodeCount(dto);
        if(CollectionUtil.isNotEmpty(manageVerifyPackageGoodsStaticVos)){
            // 将核销记录按照id、核销次数放入map
            Map<String, Integer> verifyPackageGoodsStaticMap = manageVerifyPackageGoodsStaticVos.stream().collect(Collectors.toMap(ManageVerifyPackageGoodsStaticVo::getId, ManageVerifyPackageGoodsStaticVo::getPackageVerifyQty));
            // 循环pageVo.getRecords()，将核销次数放入pageVo.getRecords()
            pageVo.getRecords().forEach(e -> {
                if(verifyPackageGoodsStaticMap.containsKey(e.getId())){
                    e.setPackageVerifyQty(verifyPackageGoodsStaticMap.get(e.getId()));
                }
            });
        }

        return pageVo;
    }

    /**
     * 按门店、员工统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public ManageOrderStaticVo manageOrderStaticTotal(ManageOrderStaticDto manageOrderStaticDto) {
        if(null == manageOrderStaticDto || (StrUtil.isBlank(manageOrderStaticDto.getParentCode()) && StrUtil.isBlank(manageOrderStaticDto.getStoreName()))){
            manageOrderStaticDto.setFirstClassFlag(1);
        }
        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        manageOrderStaticDto.setType(0);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        if(StrUtil.isNotBlank(manageOrderStaticDto.getParentCode())){
            // 查询门店编码是否是末级，如果是末级，则表示查询的是员工的统计数据
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getStoreFrontByClassCode(manageOrderStaticDto.getParentCode());
            if(null != storeFrontOrderVo && "0".equalsIgnoreCase(storeFrontOrderVo.getIsCatalog())){
                manageOrderStaticDto.setType(1);
            }
        }
        if(StrUtil.isBlank(manageOrderStaticDto.getParentCode())){
            manageOrderStaticDto.setParentCode("-1");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.manageOrderStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        // 查询门店、员工的核销数量
        ManageVerifyPackageGoodsStaticDto dto = new ManageVerifyPackageGoodsStaticDto();
        dto.setType(manageOrderStaticDto.getType());
        if(dto.getType() == 0){
            // 门店，获取pageVo.getRecords()里的id组成list
            dto.setClassCodeList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }else{
            // 员工
            dto.setEmployeeIdList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }
        dto.setPackageName(manageOrderStaticDto.getPackageName());
        dto.setStartTime(manageOrderStaticDto.getStartTime());
        dto.setEndTime(manageOrderStaticDto.getEndTime());
        // 取合计记录
        ManageOrderStaticVo manageOrderStaticVoTotal = baseMapper.manageOrderStaticTotal(manageOrderStaticDto);
        ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticVos = remoteMiniAccountService.verifyPackageCodeTotal(dto);
        if(null != manageOrderStaticVoTotal && null != manageVerifyPackageGoodsStaticVos){
            manageOrderStaticVoTotal.setPackageVerifyQty(manageVerifyPackageGoodsStaticVos.getPackageVerifyQty());
        }
        return manageOrderStaticVoTotal;
    }

    /**
     * 按权益包统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public Page managePackageStatic(ManageOrderStaticDto manageOrderStaticDto) {

        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.managePackageStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        return pageVo;
    }

    /**
     * 按权益包统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public ManageOrderStaticVo managePackageStaticTotal(ManageOrderStaticDto manageOrderStaticDto) {

        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        // 取合计记录
        ManageOrderStaticVo manageOrderStaticVoTotal = baseMapper.managePackageStaticTotal(manageOrderStaticDto);
        return manageOrderStaticVoTotal;
    }

    @Override
    @Transactional
    public void updateOrderDeductionPrice(ManageOrderDeductionPriceDto dto) {
        if(dto.getOrderId()!=null){
            CurUserDto curUser= CurUserUtil.getHttpCurUser();
            Order order = this.getById(dto.getOrderId());
            if(order == null){
                throw new ServiceException("订单不存在！");
            }
            //扣减金额
            BigDecimal deductionPrice = dto.getDeductionPrice();

            if(deductionPrice == null || deductionPrice.compareTo(BigDecimal.ZERO)<=0){
                throw new ServiceException("扣减金额必须大于0！");
            }
            //实际付款现金
            BigDecimal cashAmount = order.getCashAmount();

            BigDecimal result = cashAmount.subtract(deductionPrice);

            if(result.compareTo(BigDecimal.ZERO)<0){
                throw new ServiceException("扣减金额不能大于现金支付金额！");
            }

            order.setDeductionPrice(dto.getDeductionPrice());
            order.setDeductionUser(curUser.getUserId());
            order.setDeductionTime(LocalDateTime.now());
            order.setCashAmount(result);
            order.setDiscountsAmount(order.getDiscountsAmount().subtract(deductionPrice));
            order.setPayAmount(order.getPayAmount().subtract(deductionPrice));
            this.updateById(order);
        }else{
            throw new ServiceException("订单id不能为空");
        }
    }

    @Override
    public Integer getOrderCountByStoreFrontCode(String storeFrontCode) {
        LambdaQueryWrapper<Order>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStoreFrontCode,storeFrontCode);
        int count = this.count(queryWrapper);
        return count;
    }

    /**
     * 根据订单id获取发货信息
     * @param orderId
     * @return
     */
    @Override
    public List<OrderItemDeliveryVo> getOrderItemDelivery(String orderId) {
        List<OrderItemDeliveryVo> list = orderItemMapper.getOrderItemDelivery(orderId);
        if(list!=null&&list.size()>0){
            for (OrderItemDeliveryVo orderItemDeliveryVo : list) {
                Integer afterQty = remoteAfsService.getAfterQtyByOrder(orderItemDeliveryVo.getProductId(), orderItemDeliveryVo.getProductSkuId(), orderItemDeliveryVo.getOrderId());
                Integer unDeliveryQuantity = orderItemDeliveryVo.getUnDeliveryQuantity();
                if(afterQty == null){
                    afterQty = 0;
                }
                if(unDeliveryQuantity - afterQty>=0){
                    orderItemDeliveryVo.setUnDeliveryQuantity(unDeliveryQuantity - afterQty);
                }else{
                    orderItemDeliveryVo.setUnDeliveryQuantity(0);
                }

            }
        }

        return list;
    }

    @Override
    public PageUtils<OrderDeliveryProxyVo> getOrderDeliveryProxy(OrderDeliveryProxyParam param) {

        Page<OrderDeliveryProxyVo> page = orderDeliveryProxyService.getOrderDeliveryProxy(param);

        return new PageUtils<>(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverProxyMessage(Long[] ids) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaQueryWrapper<OrderDeliveryProxy>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderDeliveryProxy::getDeliveryStatus,DeliveryStatusEnum.NO.getCode());
        if(ids!=null&&ids.length>0){
            wrapper.in(OrderDeliveryProxy::getId,ids);
        }
        List<OrderDeliveryProxy> list = orderDeliveryProxyService.list(wrapper);
        if(list!=null&&list.size()>0){
            for (OrderDeliveryProxy orderDeliveryProxy : list) {
                OrderDeliveryProxyMessage message = new OrderDeliveryProxyMessage();
                message.setId(orderDeliveryProxy.getId());
                message.setShopId(orderDeliveryProxy.getShopId());
                message.setTenantId(orderDeliveryProxy.getTenantId());
                sender.sendDeliveryProxyMessage(message);
            }
        }
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public List<ManageOrderVo> getBatchDeliveryProxy(List<Long>ids) {


        List<SimpleOrderItemVo>newList = new ArrayList<>();

        if(ids!=null&&ids.size()>0){
            if(ids.size() >1){
                throw new ServiceException("待发货id只能为一条;");
            }
            //判断地址必须一样
            LambdaQueryWrapper<OrderDeliveryProxy>wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OrderDeliveryProxy::getId,ids);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverName);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverPhone);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverCity);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverDetailAddress);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverPostCode);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverRegion);
            wrapper.groupBy(OrderDeliveryProxy::getReceiverProvince);
            List<OrderDeliveryProxy> orderDeliveryProxyList = orderDeliveryProxyService.list(wrapper);
            if(orderDeliveryProxyList!=null&&orderDeliveryProxyList.size()>0){
                if(orderDeliveryProxyList.size()>1){
                    throw new ServiceException("批量代发货订单的发货地址信息要完全一致！");
                }
            }else{
                throw new ServiceException("待发货记录不存在！");
            }
            OrderDeliveryProxy orderDeliveryProxy = orderDeliveryProxyList.get(0);


            List<BatchDeliveryProxyItemVo>list = orderDeliveryProxyItemService.getBatchDeliveryProxy(ids);
            if(list!=null&&list.size()>0){
                for (BatchDeliveryProxyItemVo batchDeliveryProxyItemVo : list) {
                    Long productId = batchDeliveryProxyItemVo.getProductId();
                    Long productSkuId = batchDeliveryProxyItemVo.getProductSkuId();
                    ProductVo product = remoteGoodsService.findProductById(productId);
                    String productName = product.getName();
                    SkuStock skuStock = remoteGoodsService.findSkuStockById(productSkuId);
                    if(skuStock!=null&&StringUtils.isNotEmpty(skuStock.getSpecs())){
                        productName = productName + "-" +skuStock.getSpecs();
                    }
                    //待发货数量
                    Integer unDeliveryQuantity = batchDeliveryProxyItemVo.getUnDeliveryQuantity();
                    //申请售后树
                    Integer afterQty = remoteAfsService.getAfterQty(productId, productSkuId, batchDeliveryProxyItemVo.getUserId());

                    if(afterQty == null){
                        afterQty = 0;
                    }

                    List<SimpleOrderItemVo> simpleOrderItemList = orderItemMapper.selectSimpleOrderItem(batchDeliveryProxyItemVo);
                    if(simpleOrderItemList!=null&&simpleOrderItemList.size()>0){

                        Integer sum = simpleOrderItemList.stream().mapToInt(SimpleOrderItemVo::getUnDeliveryQuantity).sum();
                        if(unDeliveryQuantity>sum-afterQty){
                            throw new ServiceException(productName+"商品待发货数量不足，请联系卖家");
                        }
                        for (SimpleOrderItemVo simpleOrderItemVo : simpleOrderItemList) {
                            simpleOrderItemVo.setOrderDeliveryProxyItemId(batchDeliveryProxyItemVo.getOrderDeliveryProxyItemId());
                            simpleOrderItemVo.setOrderDeLiveryProxyId(batchDeliveryProxyItemVo.getOrderDeLiveryProxyId());

                            //订单明细待发货数量
                            Integer unDeliveryQuantity2 = simpleOrderItemVo.getUnDeliveryQuantity();
                            //订单明细对应售后数量
                            Integer afterQtyByOrder = remoteAfsService.getAfterQtyByOrder(simpleOrderItemVo.getProductId(), simpleOrderItemVo.getProductSkuId(), simpleOrderItemVo.getOrderId());

                            if(afterQtyByOrder == null){
                                afterQtyByOrder = 0;
                            }
                            unDeliveryQuantity2 = unDeliveryQuantity2-afterQtyByOrder;
                            if(unDeliveryQuantity>0){
                                if(unDeliveryQuantity>=unDeliveryQuantity2){
                                    simpleOrderItemVo.setDeliveryQuantity(unDeliveryQuantity2);
                                    unDeliveryQuantity = unDeliveryQuantity - unDeliveryQuantity2;
                                    newList.add(simpleOrderItemVo);
                                }else{
                                    simpleOrderItemVo.setDeliveryQuantity(unDeliveryQuantity);
                                    unDeliveryQuantity = 0;
                                    newList.add(simpleOrderItemVo);
                                }
                            }else{
                                break;
                            }
                        }
                    }
                }

            }else{
                throw new ServiceException("待发货记录不存在！");
            }
            Map<Long, List<SimpleOrderItemVo>> listMap = newList.stream().collect(Collectors.groupingBy(SimpleOrderItemVo::getOrderId));
            List<ManageOrderVo>resultList= new ArrayList<>();
            for (Map.Entry<Long, List<SimpleOrderItemVo>> entry : listMap.entrySet()) {
                Long key = entry.getKey();
                List<SimpleOrderItemVo> value = entry.getValue();
                List<Long>orderIds = new ArrayList<>();
                orderIds.add(key);
                List<ManageOrderVo> manageOrderVoList = baseMapper.searchLogisticsOrder(orderIds);
                if(manageOrderVoList!=null&&manageOrderVoList.size()>0){
                    ManageOrderVo manageOrderVo = manageOrderVoList.get(0);
                    manageOrderVo.setItemVoList(value);
                    manageOrderVo.setOrderDeLiveryProxyId(ids.get(0));
                    manageOrderVo.setReceiverCity(orderDeliveryProxy.getReceiverCity());
                    manageOrderVo.setReceiverProvince(orderDeliveryProxy.getReceiverProvince());
                    manageOrderVo.setReceiverName(orderDeliveryProxy.getReceiverName());
                    manageOrderVo.setReceiverPhone(orderDeliveryProxy.getReceiverPhone());
                    manageOrderVo.setReceiverDetailAddress(orderDeliveryProxy.getReceiverDetailAddress());
                    manageOrderVo.setReceiverRegion(orderDeliveryProxy.getReceiverRegion());
                    manageOrderVo.setReceiverPostCode(orderDeliveryProxy.getReceiverPostCode());
                    resultList.add(manageOrderVo);
                }
            }
            resultList = resultList.stream().sorted(Comparator.comparing(ManageOrderVo::getCreateTime)).collect(Collectors.toList());
            return resultList;
        }else{
            throw new ServiceException("代发货订单id不能为空");
        }

    }

    @Override
    public BigDecimal getSumAmount(String startDate, String endDate, List<String> skuIds, List<String> shopUserIds) {
        BigDecimal amount = this.baseMapper.getSumAmount(startDate,endDate,skuIds,shopUserIds);
        return amount;
    }

    @Override
    public void exportOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                if (ObjectUtil.isNotNull(dto.getSendBillId()) && dto.getSendBillId() == -1) {
                    orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                } else {
                    orderStatusList.clear();
                }
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                break;
            default:
                break;
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        //近一个月->0; 近三个月->1; 全部->2;
        /*switch (dto.getQuicklyDate()) {
            case 1:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
            case 2:
                startDate = null;
                endDate = null;
                break;
            case 0:
            default:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -1).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
        }*/
        //判断是否为主店铺
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(dto.getApiShopId());
        }
        String shopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    shopIds.add(shopId);
                    dto.setShopIds(shopIds);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }

        dto.setCurrent(1);
        // 设置导出最大数量
        dto.setSize(CommonConstants.MAX_EXPORT_SIZE);

        Page<ManageOrderExcelVo> manageOrderExcelVoPage = baseMapper.searchManageOrderExcelPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        List<ManageOrderExcelVo> excelVoList = manageOrderExcelVoPage.getRecords();
        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "提现列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, ManageOrderExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }

    }

    /**
     * 查询未给会员用户产生佣金且状态为完成的订单
     * @param dto the dto
     * @return
     */
    @Override
    public PageUtils searchByMiniAccount(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(1);
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        if(StrUtil.isNotBlank(dto.getMiniAccountShopUserId())){
            MiniAccountExtends miniAccountExtends = this.remoteMiniAccountService.getMiniAccountExtends(dto.getMiniAccountShopUserId());
            if(null != miniAccountExtends){
                dto.setMiniAccountShopUserId(miniAccountExtends.getShopUserId());
            }
        }

        String shopId = ShopContextHolder.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Page<Order> page = baseMapper.searchByMiniAccount(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        ShopContextHolder.setShopId(shopId);

        return new PageUtils(page);
    }
    /**
     * 批量确认收货
     * @param orderIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchReceipt(Long[] orderIds) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(orderIds != null){
            for (Long orderId : orderIds) {
                this.miniOrderService.receiptOrder(orderId, true);
            }
        }
        ShopContextHolder.setShopId(shopId);
    }

    public void exportOrderEvaluate(ManageSearchEvaluateDto dto) {
        List<ManageEvaluateVo> list = orderEvaluateMapper.searchOrderEvaluate(new Page(1, 1000000), dto).getRecords();
        for (ManageEvaluateVo record : list) {
            record.setRate(record.getShopRate());
        }
        HuToolExcelUtils.exportData(list, "评价管理列表",
                item->{
                    ManageEvaluateExcelVo excelVo = new ManageEvaluateExcelVo();

                    excelVo.setUserName(item.getUserName());
                    excelVo.setRate(item.getRate());
                    if (item.getCreateTime() != null) {
                        excelVo.setCreateTime(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    }
                    // 处理商品信息和评价内容
                    if (item.getItemList() != null && !item.getItemList().isEmpty()) {
                        StringBuilder productNames = new StringBuilder();
                        StringBuilder comments = new StringBuilder();
                        StringBuilder reply = new StringBuilder();

                        for (int i = 0; i < item.getItemList().size(); i++) {
                            OrderProductEvaluate productEvaluate = item.getItemList().get(i);
                            if (i > 0) {
                                productNames.append("; ");
                                comments.append("; ");
                                reply.append("; ");
                            }
                            productNames.append(productEvaluate.getProductName());
                            comments.append(productEvaluate.getComment() != null ? productEvaluate.getComment() : "");
                            reply.append(productEvaluate.getReply() != null ? productEvaluate.getReply() : "");
                        }

                        excelVo.setProductNames(productNames.toString());
                        excelVo.setComments(comments.toString());
                        excelVo.setReply(reply.toString());
                    } else {
                        excelVo.setProductNames("");
                        excelVo.setComments("");
                        excelVo.setReply("");
                    }

                    // 处理订单类型
                    if (item.getType() != null) {
                        excelVo.setOrderType(item.getType().getDesc());
                    } else {
                        excelVo.setOrderType("");
                    }

                    return excelVo;
                });
    }
    @Override
    public void deleteOrder(List<Long> orderIds) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        CurUserDto curUser= CurUserUtil.getHttpCurUser();
        AccountInfo accountInfo = remoteMiniInfoService.getAccountInfoByUserId(curUser.getUserId());
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(accountInfo.getShopId()));
        if(accountInfo.getAccountType()!=null&&accountInfo.getAccountType()==0
                &&shopsPartner.getMainFlag()!=null&&shopsPartner.getMainFlag()==1){
            for (Order order : orders) {
                if (!OrderStatusEnum.COMPLETE.equals(order.getStatus())) {
                    throw new ServiceException("当前状态不能删除此订单[" + order.getId() + "]");
                }
                //判断是否存在收货单
                List<AfsSimpleVo> afsList = remoteAfsService.getAfsOrderByReceiptBillId(order.getId());
                if(afsList!=null&&afsList.size()>0){
                    throw new ServiceException("存在售后订单不能删除！");
                }
                //判断套包订单是否核销，通惠证订单是否核销
                Integer result = remoteMiniAccountService.vailDeleteOrderFlag(order.getId());
                if(result==1){
                    throw new ServiceException("套包订单已核销，不能删除！");
                }
                if(result==2){
                    throw new ServiceException("通惠证订单已核销，不能删除！");
                }
                OrderVo orderVo = orderInfo(order.getId());
                this.removeById(order.getId());
                sender.sendDeleteOrderMessage(orderVo);
            }
        }else{
            throw new ServiceException("当前用户没有权限删除订单");
        }

    }
    @Override
    public void exportPackageStatic(ManageOrderStaticDto param){
        HuToolExcelUtils.exportParamToMax(param);
        Page page = this.managePackageStatic(param);
        List<String> totalFields = Stream.of("packageQty", "packageAmount", "packageVerifyQty", "packageVerifyAmount").collect(Collectors.toList());
        HuToolExcelUtils.exportData(page.getRecords(),"权益包汇总",source->new ManageOrderStaticExcelVo(),totalFields);
    }
    @Override
    public void exportOrderDetail(ManageOrderItemParam params) {
        // 设置导出最大数量
        HuToolExcelUtils.exportParamToMax(params);
        PageUtils pageUtils = this.searchOrderDetail(params);
        List<ManageOrderItemVo> orderItemList = pageUtils.getList();
        // 设置合计字段
        List<String> totalFields = Stream.of("productQuantity", "realAmount", "refundProductQuantity", "refundRealAmount").collect(Collectors.toList());
        HuToolExcelUtils.exportData(orderItemList, "订单明细列表",
                item->{
                    ManageOrderItemExcelVo excelVo = new ManageOrderItemExcelVo();
                    excelVo.setOrderId(item.getOrderId().toString());
                    if (item.getDeliveryType() != null && item.getDeliveryType().getDesc() != null){
                        excelVo.setDeliveryTypeName(item.getDeliveryType().getDesc());
                    } else {
                        excelVo.setDeliveryTypeName("");
                    }
                    return excelVo;
                },totalFields);
    }
}
