package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.order.api.entity.OrderDelivery;
import com.medusa.gruul.order.api.model.OrderDeliveryDto;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单物流 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
public interface OrderDeliveryMapper extends BaseMapper<OrderDelivery> {


    /**
     * Select one by order id order delivery dto.
     *
     * @param orderId the order id
     * @return the order delivery dto
     * 查询物流订单
     * @param:OrderId
     * @return: com.medusa.gruul.order.api.model.OrderDeliveryDto
     * @throws:
     * <AUTHOR>
     * @version : v1.0
     * @date 2020 /3/14 5:22 下午
     */
    OrderDeliveryDto selectOneByOrderId(@Param(value = "orderId") long orderId);

    /**
     * Select by order id order delivery.
     *
     * @param orderId the order id
     * @return the order delivery
     * 查询物流订单
     * @param:OrderId
     * @return: com.medusa.gruul.order.api.model.OrderDeliveryDto
     * @throws:
     * <AUTHOR>
     * @version : v1.0
     * @date 2020 /3/14 5:22 下午
     */
    OrderDelivery selectByOrderId(@Param(value = "orderId") long orderId);

    /**
     * Update by param int.
     *
     * @param orderDeliveryDto the order delivery dto
     * @return the int
     * 更新发货时间和状态
     * @param:orderDeliveryDto
     * @return: int
     * @throws:
     * <AUTHOR>
     * @version : v1.0
     * @date 2020 /3/14 5:22 下午
     */
    int updateByParam(OrderDeliveryDto orderDeliveryDto);

    /**
     * 根据订单id获取发货信息
     * @param orderId
     * @return
     */
    List<ManageOrderDeliveryVo> getManageOrderDelivery(@Param(value = "orderId") Long orderId);

    /**
     * 根据id获取发货信息
     * @param id
     * @return
     */
    ManageOrderDeliveryVo getManageOrderDeliveryById(@Param(value = "id") Long id);
    /**
     * 根据订单代发货订单ID获取发货信息
     * @param orderDeliveryProxyId
     * @return
     */
    List<ManageOrderDeliveryVo> getManageOrderDeliveryByOrderDeliveryProxyId(@Param(value = "orderDeliveryProxyId") Long orderDeliveryProxyId);

}
