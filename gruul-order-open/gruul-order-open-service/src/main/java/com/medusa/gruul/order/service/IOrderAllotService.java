package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.entity.OrderAllot;
import com.medusa.gruul.order.model.OutOrderAllotParam;
import com.medusa.gruul.order.model.OutStockOutOrderParam;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:26 2024/12/25
 */
public interface IOrderAllotService extends IService<OrderAllot> {

    /**
     * 外部系统查询订单-调拨单
     * @param param
     * @return
     */
    PageUtils searchOutOrderAllot(OutOrderAllotParam param);

    /**
     * 商家批量修改订单发送状态
     *
     * @param orderIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(List<Long> orderIds, String sendStatus);
}
