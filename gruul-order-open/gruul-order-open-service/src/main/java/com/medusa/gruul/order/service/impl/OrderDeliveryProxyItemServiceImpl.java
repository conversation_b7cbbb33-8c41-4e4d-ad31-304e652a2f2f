package com.medusa.gruul.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxyItem;
import com.medusa.gruul.order.mapper.OrderDeliveryProxyItemMapper;
import com.medusa.gruul.order.model.BatchDeliveryProxyItemVo;
import com.medusa.gruul.order.service.IOrderDeliveryProxyItemService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:19 2025/5/29
 */
@Service
public class OrderDeliveryProxyItemServiceImpl extends ServiceImpl<OrderDeliveryProxyItemMapper, OrderDeliveryProxyItem> implements IOrderDeliveryProxyItemService {


    @Override
    public List<BatchDeliveryProxyItemVo> getBatchDeliveryProxy(List<Long> ids) {
        return this.baseMapper.getBatchDeliveryProxy(ids);
    }
}
