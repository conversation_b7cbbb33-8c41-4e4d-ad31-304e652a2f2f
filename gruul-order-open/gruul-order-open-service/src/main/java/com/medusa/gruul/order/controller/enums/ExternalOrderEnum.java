package com.medusa.gruul.order.controller.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 订单发送状态枚举
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Getter
public enum ExternalOrderEnum {

    /**
     * 未发送  NOT_ISSUED
     */
    NOT_ISSUED(0,"未发送"),
    /**
     * 已发送 ISSUED
     */
    ISSUED(1,"已发送");



    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;


    ExternalOrderEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
