package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxy;
import com.medusa.gruul.order.model.OrderDeliveryProxyParam;
import com.medusa.gruul.order.model.OrderDeliveryProxyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:12 2025/5/29
 */
@Mapper
public interface OrderDeliveryProxyMapper extends BaseMapper<OrderDeliveryProxy> {

    /**
     * 后台分页查询待发货列表
     * @param page
     * @param param
     * @return
     */
    Page<OrderDeliveryProxyVo> getOrderDeliveryProxy(Page<OrderDeliveryProxyVo> page, @Param("param") OrderDeliveryProxyParam param);
}
