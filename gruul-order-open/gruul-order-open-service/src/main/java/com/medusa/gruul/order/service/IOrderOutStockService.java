package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.entity.OrderOutStock;
import com.medusa.gruul.order.api.model.AddOrderOutStockDto;
import com.medusa.gruul.order.api.model.CreateOrderOutStockMessage;
import com.medusa.gruul.order.model.OutStockOutOrderParam;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:59 2024/10/12
 */
public interface IOrderOutStockService extends IService<OrderOutStock> {

    /**
     * 添加出库单数据
     * @param addOrderOutStockDto
     * @return
     */
    OrderOutStock addOrderOutStock(AddOrderOutStockDto addOrderOutStockDto);

    /**
     * 添加出库单数据
     * @param createOrderOutStockMessage
     * @return
     */
    Boolean addOrderOutStock(CreateOrderOutStockMessage createOrderOutStockMessage);

    /**
     * 外部系统查询订单-出库单
     * @param param
     * @return
     */
    PageUtils searchOutStockOutOrder(OutStockOutOrderParam param);

    /**
     * 商家批量修改订单发送状态
     *
     * @param orderIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(List<Long> orderIds, String sendStatus);
}
