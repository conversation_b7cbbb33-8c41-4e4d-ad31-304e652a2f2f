package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderOutStock;
import com.medusa.gruul.order.model.OutReceiveOrderVo;
import com.medusa.gruul.order.model.OutStockOutOrderParam;
import com.medusa.gruul.order.model.OutStockOutOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:54 2024/10/12
 */
@Repository
public interface OrderOutStockMapper extends BaseMapper<OrderOutStock> {

    /**
     * 分页获取需要同步的出库单
     * @param page
     * @param param
     * @return
     */
    Page<OutStockOutOrderVo> searchOutStockOutOrder(Page page, @Param("param") OutStockOutOrderParam param);

    /**
     * 商家批量修改订单发送状态
     *
     * @param orderIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(@Param(value = "orderIds") List<Long> orderIds, @Param(value = "sendStatus")String sendStatus);
}
