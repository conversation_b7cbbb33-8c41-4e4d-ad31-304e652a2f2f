package com.medusa.gruul.order.model;

import com.medusa.gruul.account.api.entity.MiniAccountAddress;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ShopsItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: 结算页信息封装-多店铺
 * @Date: Created in 15:47 2023/9/28
 */
@Data
@ApiModel(value = "结算页信息封装-多店铺")
public class ShopsConfirmOrderVo {

    /**
     * 所选商品-多店铺
     */
    @ApiModelProperty(value = "所选商品-多店铺")
    List<ShopsItemVo> shopItemVoList;

    /**
     * 系统配置的自定义表单
     */
    @ApiModelProperty(value = "系统配置的自定义表单")
    List<OrderComponentVo> componentVoList;
    /**
     * 收货地址列表
     */
    @ApiModelProperty(value = "收货地址列表")
    private List<MiniAccountAddress> miniAccountAddress;

    /**
     * 用户可用积分
     */
    @ApiModelProperty(value = "用户可用积分")
    private BigDecimal userIntegral;

    /**
     * 默认仓库
     */
    @ApiModelProperty(value = "默认仓库")
    private Integer stockFlag;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal youhuiPrice;

    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    @ApiModelProperty(value = "满减满赠活动")
    private ConfirmOrderFullDonationVo confirmOrderFullDonationVo;

}
