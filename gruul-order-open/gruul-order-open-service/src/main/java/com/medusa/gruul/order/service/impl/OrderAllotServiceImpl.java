package com.medusa.gruul.order.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.entity.OrderAllot;
import com.medusa.gruul.order.api.entity.OrderAllotDet;
import com.medusa.gruul.order.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.order.mapper.OrderAllotDetMapper;
import com.medusa.gruul.order.mapper.OrderAllotMapper;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.service.IOrderAllotService;
import net.sf.jsqlparser.expression.LongValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:27 2024/12/25
 */
@Service
public class OrderAllotServiceImpl extends ServiceImpl<OrderAllotMapper, OrderAllot>implements IOrderAllotService {

    @Autowired
    private OrderAllotDetMapper orderAllotDetMapper;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Override
    public PageUtils searchOutOrderAllot(OutOrderAllotParam param) {
        Page<OutOrderAllotVo> page = baseMapper.searchOutOrderAllot(new Page(param.getCurrent(), param.getSize()), param);
        List<OutOrderAllotVo> list=page.getRecords();
        if(list!=null&&list.size()>0){
            for (OutOrderAllotVo outOrderAllotVo : list) {
                Long id = outOrderAllotVo.getId();
                LambdaQueryWrapper<OrderAllotDet> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OrderAllotDet::getMainId,id);
                List<OrderAllotDet> detList = orderAllotDetMapper.selectList(wrapper);
                List<OutOrderAllotDetVo>dataDetList = new ArrayList<>();
                if(detList!=null&&detList.size()>0){
                    for (OrderAllotDet orderAllotDet : detList) {
                        OutOrderAllotDetVo outOrderAllotDetVo = new OutOrderAllotDetVo();
                        outOrderAllotDetVo.setId(orderAllotDet.getId());
                        outOrderAllotDetVo.setNumber(orderAllotDet.getNumber());
                        outOrderAllotDetVo.setPrice(orderAllotDet.getPrice());
                        outOrderAllotDetVo.setAmount(orderAllotDet.getAmount());
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(orderAllotDet.getProductId()));
                        outOrderAllotDetVo.setProductName(product.getName());
                        if(StringUtils.isNotEmpty(product.getClassCode())){
                            outOrderAllotDetVo.setProductCode(product.getClassCode());
                        }else{
                            outOrderAllotDetVo.setProductCode("");
                        }

                        if(StrUtil.isNotBlank(orderAllotDet.getLinkProductId())){
                            ProductVo linkProduct = remoteGoodsService.findProductById(Long.valueOf(orderAllotDet.getLinkProductId()));
                            if(null != linkProduct){
                                outOrderAllotDetVo.setProductName(linkProduct.getName());
                                outOrderAllotDetVo.setProductCode(StrUtil.isEmpty(linkProduct.getClassCode()) ? "" : linkProduct.getClassCode());
                            }
                        }
                        outOrderAllotDetVo.setSpecs2(StrUtil.isEmpty(orderAllotDet.getSpecs2()) ? "" : orderAllotDet.getSpecs2());

                        dataDetList.add(outOrderAllotDetVo);
                    }
                }
                outOrderAllotVo.setOutOrderAllotDet(dataDetList);
            }
        }
        List<Long>idList=list.stream().map(OutOrderAllotVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public void updateSendStatus(List<Long> ids, String sendStatus) {
        baseMapper.updateSendStatus(ids,sendStatus);
    }
}
