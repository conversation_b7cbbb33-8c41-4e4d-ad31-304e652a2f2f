package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:43 2025/6/6
 */
@ApiModel(value = "订单支付信息dto")
@Data
public class ApiOrderPayMessageDto {

    @ApiModelProperty("订单支付金额")
    @NotNull
    private BigDecimal payAmount;


    @ApiModelProperty("是否代客户下单：0.否；1.是")
    private Integer replaceCreateOrderFlag;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("是否使用金豆：0.否；1.是")
    private Integer goldenFlag;

    @ApiModelProperty(value = "支付金豆")
    private BigDecimal payGolden;

    @ApiModelProperty("是否使用佣金：0.否；1.是")
    private Integer commissionFlag;

    @ApiModelProperty(value = "支付佣金")
    private BigDecimal payCommission;

}
