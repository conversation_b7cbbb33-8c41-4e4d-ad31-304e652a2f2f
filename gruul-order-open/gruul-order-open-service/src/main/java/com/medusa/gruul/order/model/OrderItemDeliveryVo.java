package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:29 2025/5/28
 */
@Data
@ApiModel(value = "订单发货返回订单明细信息")
public class OrderItemDeliveryVo {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String productName;

    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量")
    private Integer productQuantity;
    /**
     * 已发货数量
     */
    @ApiModelProperty(value = "已发货数量")
    private Integer deliveryQuantity;
    /**
     * 待发货数量
     */
    @ApiModelProperty(value = "待发货数量")
    private Integer unDeliveryQuantity;

    @ApiModelProperty(value = "商品id")
    private Long  productId;

    @ApiModelProperty(value = "商品规格id")
    private Long productSkuId;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

}
