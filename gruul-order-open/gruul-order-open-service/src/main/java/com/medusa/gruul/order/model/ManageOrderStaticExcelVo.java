package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 权益包汇总
 */
@Data
@ApiModel(value = "订单汇总报表返回结果")
public class ManageOrderStaticExcelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String name;

    /**
     * 销售权益包数量
     */
    @ApiModelProperty(value = "销售权益包数量")
    private BigDecimal packageQty;

    /**
     * 权益包金额
     */
    @ApiModelProperty(value = "权益包金额")
    private BigDecimal packageAmount;

    /**
     * 核销数
     */
    @ApiModelProperty(value = "核销数")
    private Integer packageVerifyQty;

    /**
     * 未核销数
     */
    @ApiModelProperty(value = "未核销数")
    private Integer packageUnVerifyQty;
}
