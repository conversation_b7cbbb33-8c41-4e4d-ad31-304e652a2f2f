package com.medusa.gruul.order.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxy;
import com.medusa.gruul.order.mapper.OrderDeliveryProxyMapper;
import com.medusa.gruul.order.model.OrderDeliveryProxyParam;
import com.medusa.gruul.order.model.OrderDeliveryProxyVo;
import com.medusa.gruul.order.service.IOrderDeliveryProxyService;
import org.springframework.stereotype.Service;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:14 2025/5/29
 */
@Service
public class OrderDeliveryProxyServiceImpl extends ServiceImpl<OrderDeliveryProxyMapper, OrderDeliveryProxy>implements IOrderDeliveryProxyService {
    @Override
    public Page<OrderDeliveryProxyVo> getOrderDeliveryProxy( OrderDeliveryProxyParam param) {

        return this.baseMapper.getOrderDeliveryProxy(new Page<OrderDeliveryProxyVo>(param.getCurrent(),param.getSize()),param);

    }
}
