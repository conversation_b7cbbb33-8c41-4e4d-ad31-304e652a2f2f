package com.medusa.gruul.order.model;

import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.order.api.enums.DeliveryStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:39 2025/5/30
 */
@Data
@ApiModel("后台待发货订单查询条件Param")
public class OrderDeliveryProxyParam extends QueryParam {

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;


    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String productName;

    /**
     * 发货状态:0-未发货，1-已发货
     */
    @ApiModelProperty(value = "发货状态:0-未发货，1-已发货")
    private Integer deliveryStatus;

}
