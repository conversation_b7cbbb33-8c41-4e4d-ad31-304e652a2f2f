package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.order.api.entity.OrderDeliveryProxy;
import com.medusa.gruul.order.api.enums.DeliveryStatusEnum;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:11 2025/5/30
 */
@Data
@ApiModel("后台待发货订单查询Vo")
public class OrderDeliveryProxyVo {

    /**
     * 待发货订单id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    private String receiverPostCode;

    /**
     * 省份/直辖市
     */
    @ApiModelProperty(value = "省份/直辖市")
    private String receiverProvince;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String receiverCity;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String receiverRegion;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String receiverDetailAddress;

    /**
     * 发货状态:0-未发货，1-已发货
     */
    @ApiModelProperty(value = "发货状态:0-未发货，1-已发货")
    private DeliveryStatusEnum deliveryStatus;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private String deliveryTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 代发货订单明细
     */
    @ApiModelProperty(value = "代发货订单明细")
    private List<OrderDeliveryProxyItemVo>itemList;

    /**
     * 代发货订单发货记录
     */
    @ApiModelProperty(value = "代发货订单发货记录")
    private List<ManageOrderDeliveryVo>orderDeliveryList;

}
