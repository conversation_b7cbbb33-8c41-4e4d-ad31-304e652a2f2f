package com.medusa.gruul.order.model;

import com.medusa.gruul.order.api.model.ItemDto;
import com.medusa.gruul.order.api.model.ItemShopDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: 查询订单需要的参数-多店铺
 * @Date: Created in 19:09 2023/9/28
 */
@Data
@ApiModel(value = "查询订单需要的参数-多店铺")
public class GroupOrderResultShopsDto {


    @NotNull
    @ApiModelProperty(value = "之前返回的OrderIds")
    private String orderIds;

    @NotEmpty
    @ApiModelProperty(value = "SKU ID")
    private List<ItemDto> items;

}
