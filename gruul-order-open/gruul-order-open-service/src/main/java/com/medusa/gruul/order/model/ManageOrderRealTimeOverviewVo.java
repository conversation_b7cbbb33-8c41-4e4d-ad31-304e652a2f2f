package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * The type Manage order overview vo.
 * <p>
 * 实时概况
 *
 * <AUTHOR>
 * @date 2022 /06/10 11:20
 */
@Data
@ApiModel("实时概况")
public class ManageOrderRealTimeOverviewVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 昨天交易概况
     */
    @ApiModelProperty(value = "昨天交易概况")
    private ManageOrderTradeDto yesterdayTrade;

    /**
     * 今天交易概况
     */
    @ApiModelProperty(value = "今天交易概况")
    private ManageOrderTradeDto trade;

    /**
     * 交易线用的数据
     */
    @ApiModelProperty(value = "交易线用的数据")
    private List<ManageOrderTradeDto> tradeLineVos;
}
