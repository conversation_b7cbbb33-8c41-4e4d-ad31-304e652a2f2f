package com.medusa.gruul.order.model;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:14 2024/9/29
 */
@ApiModel("生成外部系统收款单Param")
@Data
public class OutReceiveOrderParam extends QueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

}
