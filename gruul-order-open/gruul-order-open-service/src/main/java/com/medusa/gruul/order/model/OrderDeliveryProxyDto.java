package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:21 2025/5/29
 */
@Data
@ApiModel("待发货订单新增dto")
public class OrderDeliveryProxyDto {

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    @NotNull(message = "收货人姓名不能为空")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    @NotNull(message = "收货人电话不能为空")
    private String receiverPhone;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    @NotNull(message = "收货人邮编不能为空")
    private String receiverPostCode;

    /**
     * 省份/直辖市
     */
    @ApiModelProperty(value = "省份/直辖市")
    @NotNull(message = "省份/直辖市不能为空")
    private String receiverProvince;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @NotNull(message = "城市不能为空")
    private String receiverCity;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    @NotNull(message = "区不能为空")
    private String receiverRegion;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @NotNull(message = "详细地址不能为空")
    private String receiverDetailAddress;

    /**
     * 待发货订单明细列表
     */
    @ApiModelProperty(value = "待发货订单明细列表")
    @NotEmpty(message = "待发货订单明细列表不能为空")
    private List<OrderDeliveryProxyItemDto> itemList;

}
