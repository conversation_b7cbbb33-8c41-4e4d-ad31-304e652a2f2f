package com.medusa.gruul.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.enums.WxDeliverStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.BaseOrderMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.api.model.OutSalesItemLydHistoryDto;
import com.medusa.gruul.order.api.model.OutSalesLydHistoryDto;
import com.medusa.gruul.order.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.order.mapper.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.mq.Sender;
import com.medusa.gruul.order.service.IManageOrderService;
import com.medusa.gruul.order.service.ISalesItemLydHistoryService;
import com.medusa.gruul.order.service.ISalesLydHistoryService;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.CommissionRuleTypeEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史出库单明细- 服务实现类
 */
@Slf4j
@Service
public class SalesLydHistoryServiceImpl extends ServiceImpl<SalesLydHistoryMapper, SalesLydHistory> implements ISalesLydHistoryService {

    @Resource
    private Sender sender;
    @Resource
    private MiniOrderServiceImpl miniOrderService;
    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;
    @Resource
    private OrderEvaluateMapper orderEvaluateMapper;
    @Resource
    private OrderProductEvaluateMapper orderProductEvaluateMapper;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private RemoteShopsService remoteShopsService;
    @Resource
    private OrderSettingMapper orderSettingMapper;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private ISalesItemLydHistoryService salesItemLydHistoryService;

    @Override
    public PageUtils searchOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                if (ObjectUtil.isNotNull(dto.getSendBillId()) && dto.getSendBillId() == -1) {
                    orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                } else {
                    orderStatusList.clear();
                }
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                break;
            default:
                break;
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        //近一个月->0; 近三个月->1; 全部->2;
        /*switch (dto.getQuicklyDate()) {
            case 1:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
            case 2:
                startDate = null;
                endDate = null;
                break;
            case 0:
            default:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -1).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
        }*/
        //判断是否为主店铺
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(dto.getApiShopId());
        }
        String shopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    shopIds.add(shopId);
                    dto.setShopIds(shopIds);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }
        Page<ManageOrderVo> page = baseMapper.searchManageOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        List<ManageOrderVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ManageOrderVo record : records) {
                if(StringUtil.isNotEmpty(record.getWarehouseId())){
                    Warehouse warehouse = remoteGoodsService.getWarehouseById(Long.valueOf(record.getWarehouseId()));
                    record.setWarehouse(warehouse);
                }
                if(StringUtil.isNotEmpty(record.getShopId())){
                    ShopsPartner shopsPartner1 = remoteShopsService.getByShopId(Long.valueOf(record.getShopId()));
                    if(shopsPartner1!=null&&!shopsPartner1.equals("")){
                        record.setShopName(shopsPartner1.getName());
                    }
                }
            }
        }
        ShopContextHolder.setShopId(shopId);
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrderByReceived(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());//待卖家发货
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());//配送中
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());//等待买家取货
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());//等待评价
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());//订单已完成
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());//审核通过
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(List<Long> orderIds) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverMessage(Long[] orderIds) {

    }

    @Override
    public Integer vailDeliveryByOrderId(String orderId) {
        Integer result = 0;

        return result;
    }

    @Override
    public Integer vailDelivery(String orderIds) {
        Integer result = 0;

        return result;
    }

    /**
     * 保存外部系统传过来的历史出库单
     * @param outDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesLydHistory outSave(OutSalesLydHistoryDto outDto) {
        SalesLydHistory sales = BeanUtil.copyProperties(outDto, SalesLydHistory.class, "type", "status");
        // 循环OrderStatusEnum，找到对应的枚举
        if (outDto.getType() == null) {
            throw new ServiceException("订单类型不能为空");
        }
        if (outDto.getStatus() == null) {
            throw new ServiceException("订单状态不能为空");
        }

        sales.setType(OrderTypeEnum.find(outDto.getType()));
        sales.setStatus(OrderStatusEnum.find(outDto.getStatus()));
        sales.setId(IdWorker.getId());
        this.save(sales);
        Long salesId = sales.getId();
        // 保存历史出库明细
        List<SalesItemLydHistory> detailList = new ArrayList<>();
        for (OutSalesItemLydHistoryDto detailDto : outDto.getItemDtoList()) {
            SalesItemLydHistory detail = BeanUtil.copyProperties(detailDto, SalesItemLydHistory.class);
            detail.setSalesId(salesId);
            detail.setId(IdWorker.getId());
            detailList.add(detail);
        }
        this.salesItemLydHistoryService.saveBatch(detailList);
        return sales;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paymentOrder(List<Long> orderIds) {
        //线下支付可以支付其他门店订单

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSendStatus(List<Long> orderIds,String sendStatus) {
        baseMapper.updateSendStatus(orderIds,sendStatus);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStock(Long orderId) {

    }

    @Override
    public void noteOrder(List<Long> orderIds, String note, Boolean isOver) {

    }


    @Override
    public PageUtils searchOrderEvaluate(ManageSearchEvaluateDto dto) {
        Page<ManageEvaluateVo> page = orderEvaluateMapper.searchOrderEvaluate(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        for (ManageEvaluateVo record : page.getRecords()) {
            record.setRate(record.getShopRate());
        }
        return new PageUtils(page);
    }

    @Override
    public void choiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            productEvaluate.setChoice(true);
            orderProductEvaluateMapper.updateById(productEvaluate);
        }
    }

    @Override
    public void replyEvaluate(Long id, String reply) {
        OrderProductEvaluate productEvaluate = orderProductEvaluateMapper.selectById(id);
        productEvaluate.setReply(reply);
        orderProductEvaluateMapper.updateById(productEvaluate);
    }

    @Override
    public OrderVo orderInfo(Long orderId) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        OrderVo orderVo = baseMapper.selectOrderVoById(orderId);
        String shopId = orderVo.getShopId();
        ShopContextHolder.setShopId(shopId);

        if(orderVo!=null&&orderVo.getWarehouseId()!=null){
            Warehouse warehouse = remoteGoodsService.getWarehouseById(orderVo.getWarehouseId());
            if(warehouse!=null){
                orderVo.setWarehouseAddress(warehouse.getWarehouseAddress());
                orderVo.setWarehouseFullName(warehouse.getWarehouseFullName());
            }
        }
        ShopContextHolder.setShopId(oldShopId);
        return orderVo;
    }

    @Override
    public void unChoiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            if (productEvaluate.getChoice()) {
                productEvaluate.setChoice(false);
                orderProductEvaluateMapper.updateById(productEvaluate);
            }
        }
    }

    @Override
    public PageUtils searchLogisticsOrderList(ManageLogisticsOrderDto dto) {
        Page<ManageDeliveryOrderVo> page = baseMapper.searchLogisticsOrderList(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        return new PageUtils(page);

    }

    @Override
    public ManageOrderOverviewVo getOverview() {
        ManageOrderOverviewVo vo = new ManageOrderOverviewVo();

        return vo;
    }
    @Override
    public ManageOrderRealTimeOverviewVo getRealTimeOverview(String startDate,String endDate,Integer type) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date date1 = dateFormat.parse(startDate);
        Date date2 = dateFormat.parse(endDate);

        startDate =dateFormat.format(date1);
        startDate=startDate+" 00:00:00";
        endDate =dateFormat.format(date2);
        endDate=endDate+" 23:59:59";

        List<ManageOrderTradeDto> dbTradeDtoList = baseMapper.transacTionOverview(startDate, endDate, OrderStatusEnum.getPaidStatus());
        List<ManageOrderTradeDto> tradeDtoList = new ArrayList<>();
        while(date1.compareTo(date2) <= 0){
            Date finalDate = date1;
            List<ManageOrderTradeDto> existList = dbTradeDtoList.stream().filter(e -> {
                try {
                    return dateFormat.parse(e.getDate()).compareTo(finalDate) == 0;
                } catch (ParseException parseException) {
                    parseException.printStackTrace();
                }
                return false;
            }).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(existList)){
                ManageOrderTradeDto newDto = new ManageOrderTradeDto();
                newDto.setDate(dateFormat.format(date1));
                newDto.setTransactionVolume(0);
                newDto.setTurnover(new BigDecimal("0"));
                tradeDtoList.add(newDto);
            }else{
                tradeDtoList.addAll(existList);
            }
            date1 = DateUtil.offsetDay(date1, 1);
        }
        Integer num=0;
        HashMap<String,String> map=new HashMap<>();
//       获取当前日期
        Date date = new Date();
        String newData= dateFormat.format(date);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();
        //昨天
        String yesterDay = dateFormat.format(yesterday);

        ManageOrderRealTimeOverviewVo manageOrderRealTimeOverviewVo=new ManageOrderRealTimeOverviewVo();
        manageOrderRealTimeOverviewVo.setUpdateTime(date);
        ManageOrderTradeDto trade=new ManageOrderTradeDto();
        ManageOrderTradeDto yesterdayTrade=new ManageOrderTradeDto();
        if(type==1){
            //今天
            map.put("startDate",newData+" 00:00:00");
            map.put("endDate",newData+" 23:59:59");
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(newData+" 00:00:00",newData+" 23:59:59", OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAgainBuyCustom(againBuyCustom);
            //昨天
            map.put("startDate",yesterDay+" 00:00:00");
            map.put("endDate",yesterDay+" 23:59:59");
            Integer yesterNum=remoteMiniAccountService.getFootprintNumber(map);
            yesterdayTrade=baseMapper.transacTion(yesterDay+" 00:00:00",yesterDay+" 23:59:59", OrderStatusEnum.getPaidStatus());
            yesterdayTrade.setViews(yesterNum);

            Integer addCustom2 = remoteMiniAccountService.getAddCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAddCustom(addCustom2);

            Integer againBuyCustom2 = getAgainBuyCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAgainBuyCustom(againBuyCustom2);

        }else {
            //查询日期的总和
            map.put("startDate",startDate);
            map.put("endDate",endDate);
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(startDate,endDate, OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(startDate, endDate);
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(startDate, endDate);
            trade.setAgainBuyCustom(againBuyCustom);
        }
        manageOrderRealTimeOverviewVo.setTradeLineVos(tradeDtoList);
        manageOrderRealTimeOverviewVo.setYesterdayTrade(yesterdayTrade);
        manageOrderRealTimeOverviewVo.setTrade(trade);
        return manageOrderRealTimeOverviewVo;
    }


    @Override
    public List<ManageOrderVo> searchLogisticsOrder(ManageSearchLogisticsOrderDto dto) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Long> orderIds = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getOrderIds())) {
            orderIds =
                    Arrays.stream(dto.getOrderIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }

        List<ManageOrderVo> list = baseMapper.searchLogisticsOrder(orderIds);
        ShopContextHolder.setShopId(oldShopId);
        return list;
    }

    @Override
    public Integer countLogisticsWaitSend() {
        Integer waitForSend = baseMapper.countLogisticsWaitSend();
        return waitForSend;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logisticsSend(ManageSearchLogisticsOrderDto dto) {

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchLogisticsSend(List<ManageSearchLogisticsOrderBatchDto> manageSearchLogisticsOrderBatchDtoList) {

    }
    /**
     * 商家批量修改审核通过订单
     * @param orderIds the order ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvedOrder(List<Long> orderIds) {

    }

    @Override
    public List<Order> getOrderByWxDeliverStatus() {
        return null;
    }

    @Override
    public Integer getAgainBuyCustom(String startDate, String endDate) {

        List<Map> list = this.baseMapper.getAgainBuyCustom(startDate, endDate);
        if(list!=null&&list.size()>0){
            return list.size();
        }else{
            return 0;
        }
    }

    @Override
    public BigDecimal getCommissionByOrderId(Long orderId,Integer type) {
        BigDecimal amount = BigDecimal.ZERO;
        List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItem orderItem : orderItemList) {
                Long productId = orderItem.getProductId();
                ProductVo product = remoteGoodsService.findProductById(productId);
                Integer ruleType = product.getRuleType();
                Integer productQuantity = orderItem.getProductQuantity();
                if(ruleType!=null){
                    if(type==CommonConstants.NUMBER_ZERO){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                    if(type==CommonConstants.NUMBER_ONE){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                }
            }
        }

        return amount;
    }



}
