package com.medusa.gruul.order.model;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:38 2025/3/28
 */
@ApiModel("会员消费列表Param")
@Data
public class ApiMemberOrderParam extends QueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userId;

}
