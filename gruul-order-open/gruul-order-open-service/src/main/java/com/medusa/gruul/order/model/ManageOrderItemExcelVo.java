package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 评价管理导出Excel VO类
 */
@Data
@ApiModel(value = "订单明细导出Excel VO类")
public class ManageOrderItemExcelVo {

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "单价")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "数量")
    private BigDecimal productQuantity;

    @ApiModelProperty(value = "金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal refundProductQuantity;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundRealAmount;

    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    @ApiModelProperty(value = "客户名称")
    private String nikeName;

    @ApiModelProperty(value = "电话号码")
    private String phone;

    @ApiModelProperty(value = "职员名称")
    private String accountName;

    @ApiModelProperty(value = "发货方式")
    private String deliveryTypeName;

    @ApiModelProperty(value = "发货仓库")
    private String warehouseName;

}
