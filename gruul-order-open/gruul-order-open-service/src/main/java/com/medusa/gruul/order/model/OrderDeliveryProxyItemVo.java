package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:38 2025/5/30
 */
@Data
@ApiModel("后台待发货订单明细查询Vo")
public class OrderDeliveryProxyItemVo {

    private static final long serialVersionUID = 1L;

    /**
     * 待发货订单明细id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long productId;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String productPic;


    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String productName;

    /**
     * 商品sku编号
     */
    @ApiModelProperty(value = "商品sku编号")
    private Long productSkuId;

    /**
     * 商品sku条码
     */
    @ApiModelProperty(value = "商品sku条码")
    private String productSkuCode;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer productQuantity;

    /**
     * 待发货数量
     */
    @ApiModelProperty(value = "待发货数量")
    private Integer unDeliveryQuantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
