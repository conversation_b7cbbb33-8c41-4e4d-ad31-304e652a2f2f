package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:07 2025/5/30
 */
@Data
@ApiModel(value = "批量代发货明细记录")
public class BatchDeliveryProxyItemVo {

    @ApiModelProperty(value = "代发货订单ID")
    private Long orderDeLiveryProxyId;

    @ApiModelProperty(value = "代发货订单明细id")
    private Long orderDeliveryProxyItemId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品规格id")
    private Long productSkuId;

    @ApiModelProperty(value = "代发货数量")
    private Integer unDeliveryQuantity;

}
