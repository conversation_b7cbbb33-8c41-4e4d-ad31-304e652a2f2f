package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.logistics.model.dto.manager.CountCostDto;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.payment.api.model.dto.PayResultDto;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDto;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.shops.api.model.PrizeProductDto;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
public interface IMiniOrderService extends IService<Order> {

    /**
     * 生成确认单信息
     *
     * @param dto the dto
     * @return com.medusa.gruul.order.web.result.ConfirmOrderVo confirm order
     * <AUTHOR>
     * @Date 2019 /10/6 12:57
     */
    ConfirmOrderVo getConfirmOrder(ConfirmOrderDto dto);

    /**
     * 生成确认单信息-多店铺
     * @param dto
     * @return
     */
    ShopsConfirmOrderVo getShopsConfirmOrderVo(ConfirmOrderDto dto);


    /**
     * 生成订单
     *
     * @param dto          the dto
     * @param orderId      the order id
     * @param skuStockList the sku stock list
     * @param curUser      the cur user
     * @return void boolean
     * <AUTHOR>
     * @date 2019 /11/18 22:09
     */
    Boolean createOrder(CreateOrderDto dto, Long orderId, List<SkuStock> skuStockList, CurUserDto curUser);


    /**
     * 生成通惠证订单
     * @param createTicketOrderDto
     * @return
     */
    String createTicketOrder(CreateTicketOrderDto createTicketOrderDto);

    /**
     * 生成积分兑换订单
     * @param dto
     * @param orderId
     * @param skuStockList
     * @param curUser
     * @return
     */
    Boolean createIntegralProductOrder(CreateOrderDto dto, Long orderId, List<SkuStock> skuStockList, CurUserDto curUser);

    /**
     * 检查库存是否足够
     *
     * @param createOrderDto the create order dto
     * @return java.lang.Long string
     * <AUTHOR>
     * @date 2019 /10/24 22:04
     */
    String preCheckOrder(CreateOrderDto createOrderDto);

    /**
     * 检查库存是否足够-多店铺
     * @param createOrderDto
     * @return
     */
    String preShopsCheckOrder(CreateOrderDto createOrderDto);

    /**
     * 用户取消订单
     *
     * @param orderId the order id
     * @return void
     * <AUTHOR>
     * @date 2019 /11/20 20:32
     */
    void cancelOrder(Long orderId);

    /**
     * 支付超时取消
     *
     * @param orderId the order id
     * @return void
     * <AUTHOR>
     * @date 2019 /11/28 21:27
     */
    void autoCancelOrder(Long orderId);

    /**
     * 用户支付订单
     *
     * @param orderId     the order id
     * @param userBalance
     * @param request     the request
     * @return void pay result dto
     * <AUTHOR>
     * @date 2019 /11/20 20:39
     */
    PayResultDto payOrder(Long orderId, Boolean userBalance, HttpServletRequest request);


    /**
     * 用户支付订单-多店铺
     *
     * @param orderIds     the order id
     * @param userBalance
     * @param request     the request
     * @return void pay result dto
     * <AUTHOR>
     * @date 2019 /11/20 20:39
     */
    PayResultDto payOrderShops(String orderIds, Boolean userBalance, HttpServletRequest request);

    /**
     * 支付回调
     *
     * @param orderId  the order id
     * @return void
     * <AUTHOR>
     * @date 2019 /11/20 20:39
     */
    void paymentNotify(Long orderId);

    /**
     * 用户确认收货
     *
     * @param orderId  the order id
     * @param isSystem the is system
     * @return void
     * <AUTHOR>
     * @date 2019 /11/20 22:07
     */
    void receiptOrder(Long orderId, boolean isSystem);

    /**
     * 检查活动是否结束
     *
     * @param dto the dto
     * @return java.lang.Integer integer
     * <AUTHOR>
     * @date 2019 /11/23 11:04
     */
    Integer checkOrder(GroupOrderResultDto dto);

    /**
     * 检查活动是否结算-多店铺
     * @param dto
     * @return
     */
    Integer checkOrderShops(GroupOrderResultShopsDto dto);


    /**
     * 用户订单列表
     *
     * @param dto the dto
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2019 /11/25 21:42
     */
    PageUtils<ApiOrderVo> searchOrder(ApiSearchOrderDto dto);


    /**
     * 用户评价订单
     *
     * @param dto the dto
     * @return void
     * <AUTHOR>
     * @date 2019 /11/25 21:41
     */
    void evaluateOrder(ApiOrderEvaluateDto dto);

    /**
     * 系统评价订单
     *
     * @param orderId
     * @return void
     * <AUTHOR>
     * @date 2019 /11/25 21:41
     */
    void evaluateOrder(Long orderId);

    /**
     * 订单详情
     *
     * @param orderId the order id
     * @return com.medusa.gruul.order.model.OrderVo order vo
     * <AUTHOR>
     * @date 2019 /11/25 22:32
     */
    OrderVo orderInfo(Long orderId);

    /**
     * searchOrderEvaluate
     *
     * @param dto the dto
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2019 /12/4 20:20
     */
    PageUtils searchOrderEvaluate(ApiSearchEvaluateDto dto);

    /**
     * 自动完成订单
     *
     * @param orderId the order id
     * @return void
     * <AUTHOR>
     * @date 2019 /12/8 19:54
     */
    void completeOrder(Long orderId);


    /**
     * 我的订单概况
     *
     * @param
     * @return com.medusa.gruul.order.api.model.OrderOverviewVo order overview vo
     * <AUTHOR>
     * @date 2020 /2/19 21:53
     */
    OrderOverviewVo orderOverview();

    /**
     * Order overview order overview vo.
     *
     * @param userId the user id
     * @return the order overview vo
     */
    OrderOverviewVo orderOverview(String userId);

    /**
     * Order share info order share info.
     *
     * @param orderId the order id
     * @return the order share info
     */
    OrderShareInfo orderShareInfo(Long orderId);



    /**
     * Refund notify.
     *
     * @param message the message
     */
    void refundNotify(RefundNotifyResultDto message);

    /**
     * 重新获取运费
     *
     * @param dto the dto
     * @return java.math.BigDecimal freight amount
     * <AUTHOR>
     * @date 2020 /1/4 21:16
     */
    CountCostDto getFreightAmount(GetCostDto dto);

    /**
     * 通过userId获取兑换记录
     * @param apiIntegralOrderParam
     * @return
     */
    PageUtils<ApiIntegralOrderVo>getApiIntegralOrderVoByUserId(ApiIntegralOrderParam apiIntegralOrderParam);

    /**
     * 获取用户兑换商品数
     * @param userId
     * @param skuId
     * @param productId
     * @return
     */
    BigDecimal getUserExchangeNumByUserId(String userId,String skuId,Long productId);

    /**
     * 查询会员历史订单
     * @param dto
     * @return
     */
    PageUtils<ApiManageHistoryOrderVo> searchHistoryOrder(ApiSearchHistoryOrderDto dto);

    /**
     * 通过邀请码获取用户关联信息
     * @param inviteCode
     * @return
     */
    RelationInfoVo getRelationInfoByInviteCode(String inviteCode);

    /**
     * 分页查询会员消费记录
     * @param param
     * @return
     */
    PageUtils<ApiMemberOrderVo>getApiMemberOrder(ApiMemberOrderParam param);

    /**
     * 判断是否能够升级
     * @param dto
     * @return
     */
    Boolean getUpgrade(UpgradeDto dto);

    /**
     * 分页查询未发货订单商品
     * @param param
     * @return
     */
    List<UnShippedOrderItemVo> getUnShippedOrderItem(UnShippedOrderItemParam param);

    /**
     * 新增待发货订单
     * @param dto
     */
    void addOrderDeliveryProxy(OrderDeliveryProxyDto dto);
    /**
     * 小程序分页查询待发货信息
     * @param param
     * @return
     */
    PageUtils getOrderDeliveryProxy(OrderDeliveryProxyParam param);

    /**
     * 获取订单支付信息
     * @param dto
     * @return
     */
    ApiOrderPayMessageVo getApiOrderPayMessage(ApiOrderPayMessageDto dto);

    /**
     * 上传效果图
     * @param dto
     */
    void uploadEffectUrl(OrderUploadEffectDto dto);


    Boolean createPrizeOrder(PrizeProductDto prizeProductMessage);

    /**
     * 获取区域团队消费业绩
     * @param upLevelTime
     * @param regionType
     * @param agentRegionCode
     * @param teamTimes
     * @return
     */
    BigDecimal getTeamsRegionAmount(String upLevelTime, Integer regionType, String agentRegionCode, Integer teamTimes);

    /**
     * 根据区域Code，成为区域会员时间获取订单数
     * @param agentRegionCode
     * @param upLevelTime
     * @return
     */
    Integer getOrderCountByRegionCode(String agentRegionCode, String upLevelTime,Integer regionType);

    /**
     * 获取待发货数量
     * @param userId
     * @param memberTypeId
     * @return
     */
    Integer getUnDeliveryQuantity(String userId, Long memberTypeId);
}
