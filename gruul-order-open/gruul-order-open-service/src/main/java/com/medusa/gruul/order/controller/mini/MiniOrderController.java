package com.medusa.gruul.order.controller.mini;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.dto.CurShopInfoDto;
import com.medusa.gruul.logistics.model.dto.manager.CountCostDto;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.service.IMiniOrderService;
import com.medusa.gruul.order.service.IOrderDeliveryService;
import com.medusa.gruul.order.service.IOrderProductEvaluateService;
import com.medusa.gruul.order.service.IOrderSettingService;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.MiniMsgVo;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 用户小程序端
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@RestController
@RequestMapping("/mini")
@Api(tags = "用户小程序端订单接口")
public class MiniOrderController {
    @Resource
    private IMiniOrderService miniOrderService;
    @Resource
    private IOrderSettingService orderSettingService;

    @Resource
    private IOrderDeliveryService orderDeliveryService;
    @Resource
    private IOrderProductEvaluateService orderEvaluateService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;


    @ApiOperation("获取结算页信息")
    @PostMapping("/confirm_order")
    public Result<ConfirmOrderVo> getConfirmOrder(@RequestBody @Valid ConfirmOrderDto dto) {
        ConfirmOrderVo confirmOrderVo = miniOrderService.getConfirmOrder(dto);
        return Result.ok(confirmOrderVo);
    }

    @ApiOperation("判断是否能升级")
    @PostMapping("/getUpgrade")
    public Result getUpgrade(@RequestBody @Valid UpgradeDto dto) {
        Boolean result = miniOrderService.getUpgrade(dto);
        return Result.ok(result);
    }

    @ApiOperation("获取结算页信息-多店铺")
    @PostMapping("/shops/confirm_order")
    public Result<ShopsConfirmOrderVo> getShopsConfirmOrder(@RequestBody @Valid ConfirmOrderDto dto) {
        ShopsConfirmOrderVo shopsConfirmOrderVo = miniOrderService.getShopsConfirmOrderVo(dto);
        return Result.ok(shopsConfirmOrderVo);
    }

    @ApiOperation("获取运费")
    @PostMapping("/freightAmount")
    public Result<CountCostDto> getFreightAmount(@RequestBody GetCostDto dto) {
        CountCostDto result = miniOrderService.getFreightAmount(dto);
        return Result.ok(result);
    }

    @ApiOperation(value = "创建订单")
    @PostMapping("/create")
    public Result<String> createOrder(@RequestBody @Valid CreateOrderDto createOrderDto) {
        String orderId = miniOrderService.preCheckOrder(createOrderDto);
        return Result.ok(orderId);
    }

    @ApiOperation(value = "创建订单-多店铺")
    @PostMapping("/shops/create")
    public Result<String> createShopsOrder(@RequestBody @Valid CreateOrderDto createOrderDto) {
        String orderId = miniOrderService.preShopsCheckOrder(createOrderDto);
        return Result.ok(orderId);
    }

    @ApiOperation(value = "创建通惠证订单")
    @PostMapping("/createTicketOrder")
    public Result<String> createTicketOrder(@RequestBody @Valid CreateTicketOrderDto createTicketOrderDto) {
        String orderId = miniOrderService.createTicketOrder(createTicketOrderDto);
        return Result.ok(orderId);
    }
    @ApiOperation(value = "查询订单创建情况", notes = "-1->已售罄;0->排队中;order->成功;")
    @PostMapping("/result")
    public Result getOrderResult(@RequestBody GroupOrderResultDto dto) {
        Order order = miniOrderService.getById(dto.getOrderId());
        if (order != null) {
            return Result.ok(order);
        } else {
            Integer result = miniOrderService.checkOrder(dto);
            return Result.ok(result);
        }
    }

    @ApiOperation(value = "查询订单创建情况-多店铺")
    @PostMapping("/shops/result")
    public Result getShopsOrderResult(@RequestBody GroupOrderResultShopsDto dto) {
        String orderIds = dto.getOrderIds();
        List<String> ids = Arrays.asList(orderIds.split(","));
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Order::getOrderGroupId, ids);
        List<Order> orderList = miniOrderService.list(queryWrapper);
        if (orderList.size()==ids.size()) {
            return Result.ok(orderIds);
        } else {
            Integer result = miniOrderService.checkOrderShops(dto);
            return Result.ok(result);
        }
    }


    @ApiOperation(value = "用户订单概况", notes = "用户订单概况")
    @GetMapping("/overview")
    public Result orderOverview() {
        OrderOverviewVo orderOverviewVo = miniOrderService.orderOverview();
        return Result.ok(orderOverviewVo);
    }


    @ApiOperation(value = "用户查询订单", notes = "用户查询订单")
    @GetMapping("/search")
    public Result<PageUtils<ApiOrderVo>> searchOrder(ApiSearchOrderDto dto) {
        PageUtils<ApiOrderVo> page = miniOrderService.searchOrder(dto);
        return Result.ok(page);
    }


    @ApiOperation(value = "用户查询订单详情", notes = "用户查询订单详情")
    @GetMapping("/info/{orderId}")
    public Result<OrderVo> orderInfo(@PathVariable(value = "orderId") @NotNull Long orderId) {
        OrderVo orderVo = miniOrderService.orderInfo(orderId);
        return Result.ok(orderVo);
    }

    @ApiOperation(value = "小程序查询晒单内容", notes = "晒单设置")
    @GetMapping("/share/{orderId}")
    public Result<OrderShareInfo> orderShareInfo(@PathVariable(value = "orderId") @NotNull Long orderId) {
        OrderShareInfo orderShareInfo = miniOrderService.orderShareInfo(orderId);
        return Result.ok(orderShareInfo);
    }


    @ApiOperation(value = "用户取消订单", notes = "用户取消订单")
    @PutMapping("/cancel/{orderId}")
    public Result cancelOrder(@PathVariable(value = "orderId") @NotNull Long orderId) {
        miniOrderService.cancelOrder(orderId);
        return Result.ok();

    }

    @ApiOperation(value = "用户支付订单", notes = "用户支付订单")
    @PutMapping("/pay/{orderId}")
    public Result payOrder(@PathVariable(value = "orderId") @NotNull Long orderId,
                           @RequestParam(value = "userBalance") Boolean userBalance, HttpServletRequest request) {
        return Result.ok(miniOrderService.payOrder(orderId, userBalance, request));

    }


    @ApiOperation(value = "用户支付订单", notes = "用户支付订单-多店铺")
    @PutMapping("/shops/pay/{orderIds}")
    public Result payShopsOrder(@PathVariable(value = "orderIds") @NotNull String orderIds,
                           @RequestParam(value = "userBalance") Boolean userBalance, HttpServletRequest request) {
        return Result.ok(miniOrderService.payOrderShops(orderIds, userBalance, request));

    }

    @ApiOperation(value = "小程序修改收货地址", notes = "小程序修改收货地址")
    @PutMapping("/receiver/address")
    public Result updateReceiverAddress(@RequestBody @Validated ReceiverAddressDto dto) {
        orderDeliveryService.updateReceiverAddress(dto);
        return Result.ok();
    }


    @ApiOperation(value = "用户确认收货", notes = "用户确认收货")
    @PutMapping("/receipt/{orderId}")
    public Result receiptOrder(@PathVariable(value = "orderId") @NotNull Long orderId) {
        miniOrderService.receiptOrder(orderId, false);
        return Result.ok();
    }


    @EscapeLogin
    @Deprecated
    @ApiOperation(value = "是否开启评论", notes = "是否开启评论")
    @GetMapping("/evaluate/setting")
    public Result evaluateSetting() {
        OrderSetting orderSetting = orderSettingService.getOne(null);
        return Result.ok(orderSetting.getOpenEvaluate());
    }

    @EscapeLogin
    @ApiOperation(value = "是否开启评论", notes = "是否开启评论")
    @GetMapping("/setting")
    public Result<SettingVo> setting() {
        SettingVo settingVo = new SettingVo();
        //获取当前线程中的租户id的店铺信息;getTemplateCodeEnum 获取当前店铺使用模板类型
        CurShopInfoDto tenantIdShopInfo = CurUserUtil.getTenantIdShopInfo();
        OrderSetting orderSetting = orderSettingService.getOne(null);
        settingVo.setOpenEvaluate(orderSetting.getOpenEvaluate());
        settingVo.setSetting(orderSetting);
        //获取当前店铺可使用的小程序订阅模板
        List<MiniMsgVo> miniMsgVoList = remoteMiniInfoService.getCurrentMiniMsg();
        settingVo.setMiniMsgVoList(miniMsgVoList);
        settingVo.setCurShopInfoDto(tenantIdShopInfo);
        return Result.ok(settingVo);
    }

    @ApiOperation(value = "用户评价订单", notes = "用户评价订单")
    @PostMapping("/evaluate")
    public Result evaluateOrder(@RequestBody @Validated ApiOrderEvaluateDto dto) {
        miniOrderService.evaluateOrder(dto);
        return Result.ok();
    }

    @ApiOperation(value = "用户查看评价列表", notes = "用户查看评价列表")
    @GetMapping("/evaluate")
    public Result<PageUtils<ApiOrderEvaluateVo>> evaluateOrder(@RequestHeader(value = "token") String token,
                                                               ApiSearchEvaluateDto dto) {
        log.info(token);
        PageUtils page = miniOrderService.searchOrderEvaluate(dto);
        return Result.ok(page);
    }

    @EscapeLogin
    @ApiOperation(value = "商品详情页评价概况", notes = "商品详情页评价概况")
    @GetMapping("/product/evaluate/overview")
    public Result<UserEvaluateVo> userEvaluateOverview(@RequestParam(value = "productId") @NotBlank String productId) {
        UserEvaluateVo vo = orderEvaluateService.userEvaluateOverview(productId);
        return Result.ok(vo);
    }

    @EscapeLogin
    @ApiOperation(value = "商品详情页评价", notes = "商品详情页评价")
    @GetMapping("/product/evaluate")
    public Result<PageUtils<ProductEvaluateVo>> productEvaluate(ApiSearchProductEvaluateDto dto) {
        PageUtils page = orderEvaluateService.productEvaluate(dto);
        return Result.ok(page);
    }

    @ApiOperation(value = "查看兑换记录", notes = "查看兑换记录")
    @GetMapping("/getApiIntegralOrderVo")
    public Result<PageUtils<ApiIntegralOrderVo>> getApiIntegralOrderVo(ApiIntegralOrderParam apiIntegralOrderParam) {
        PageUtils<ApiIntegralOrderVo> page = miniOrderService.getApiIntegralOrderVoByUserId(apiIntegralOrderParam);
        return Result.ok(page);
    }

    @GetMapping("/get/relationInfo/inviteCode")
    @ApiOperation(value = "通过邀请码获取用户关联信息")
    public Result<RelationInfoVo> getRelationInfoByInviteCode(@ApiParam(value = "inviteCode") @RequestParam String inviteCode){
        RelationInfoVo relationInfoVo = miniOrderService.getRelationInfoByInviteCode(inviteCode);
        return Result.ok(relationInfoVo);
    }

    @PostMapping("/getApiMemberOrder")
    @ApiOperation(value = "分页查询会员消费记录")
    public Result<PageUtils<ApiMemberOrderVo>> getApiMemberOrder(@RequestBody  ApiMemberOrderParam param) {
        PageUtils<ApiMemberOrderVo> page = miniOrderService.getApiMemberOrder(param);
        return Result.ok(page);
    }

    /**
     * 分页查询未发货订单商品
     * @param param
     * @return
     */
    @PostMapping("/getUnShippedOrderItem")
    @ApiOperation(value = "分页查询未发货订单商品")
    public Result<List<UnShippedOrderItemVo>> getApiMemberOrder(@RequestBody  UnShippedOrderItemParam param) {
        List<UnShippedOrderItemVo> list = miniOrderService.getUnShippedOrderItem(param);
        return Result.ok(list);
    }

    /**
     * 添加代发货订单
     * @param dto
     * @return
     */
    @ApiOperation(value = "添加代发货订单", notes = "添加代发货订单")
    @PostMapping("/addOrderDeliveryProxy")
    public Result addOrderDeliveryProxy(@RequestBody @Validated OrderDeliveryProxyDto dto) {
        miniOrderService.addOrderDeliveryProxy(dto);
        return Result.ok();
    }


    @ApiOperation(value = "商小程序获取代发货订单列表", notes = "商小程序获取代发货订单列表")
    @PostMapping("/orderDeliveryProxy/search")
    public Result getOrderDeliveryProxy(@RequestBody OrderDeliveryProxyParam param) {
        PageUtils page = miniOrderService.getOrderDeliveryProxy(param);
        return Result.ok(page);
    }

    /**
     * 获取订单支付信息
     * @param dto
     * @return
     */
    @ApiOperation(value = "获取订单支付信息", notes = "获取订单支付信息")
    @PostMapping("/getApiOrderPayMessage")
    public Result getOrderDeliveryProxy(@RequestBody ApiOrderPayMessageDto dto) {
        ApiOrderPayMessageVo apiOrderPayMessageVo = miniOrderService.getApiOrderPayMessage(dto);
        return Result.ok(apiOrderPayMessageVo);
    }

    /**
     * 上传效果图
     * @param dto
     * @return
     */
    @ApiOperation(value = "上传效果图", notes = "上传效果图")
    @PostMapping("/uploadEffectUrl")
    public Result uploadEffectUrl(@RequestBody OrderUploadEffectDto dto) {
        miniOrderService.uploadEffectUrl(dto);
        return Result.ok();
    }

}
