package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.order.api.enums.PayTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:37 2025/6/6
 */
@ApiModel(value = "订单支付信息vo")
@Data
public class ApiOrderPayMessageVo {


    @ApiModelProperty("支付方式")
    private PayTypeEnum payType;


    @ApiModelProperty(value = "当前金豆")
    private BigDecimal currentGolden;

    @ApiModelProperty(value = "当前佣金")
    private BigDecimal currentCommission;

    @ApiModelProperty(value = "支付金豆")
    private BigDecimal payGolden;

    @ApiModelProperty(value = "支付佣金")
    private BigDecimal payCommission;

    @ApiModelProperty(value = "用户实际支付")
    private BigDecimal payMoney;


}
