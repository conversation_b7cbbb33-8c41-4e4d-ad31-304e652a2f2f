package com.medusa.gruul.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.model.CloseOrderMessage;
import com.medusa.gruul.order.api.model.CreateOrderMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.model.*;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
public interface IManageOrderService extends IService<Order> {


    /**
     * PC管理端订单列表
     *
     * @param dto the dto
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2019 /11/20 20:04
     */
    PageUtils searchOrder(ManageSearchOrderDto dto);

    /**
     * PC管理端订单详情列表
     * @param params
     * @return
     */
    PageUtils searchOrderDetail(ManageOrderItemParam params);

    /**
     * 导出订单明细列表
     * @param params
     */
    void exportOrderDetail(ManageOrderItemParam params);

    /**
     * PC管理端订单列表 (外部系统)
     *
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2019 /11/20 20:04
     */
    PageUtils searchExternalOrder(ManageSearchOrderDto dto);

    /**
     * 获取同步收款单数据
     * @param dto
     * @return
     */
    PageUtils searchExternalOrderByReceived(ManageSearchOrderDto dto);

    /**
     * 获取生成同步收款单数据
     * @param param
     * @return
     */
    PageUtils searchOutReceiveOrder(OutReceiveOrderParam param);


    /**
     * 商家批量关闭订单
     *
     * @param orderIds the order ids
     * @return void
     * <AUTHOR>
     * @date 2019 /11/23 19:03
     */
    void closeOrder(List<Long> orderIds);

    /**
     * 归还仓库库存
     *
     * @param orderId
     * @return void
     * <AUTHOR>
     * @date 2022 /06/02 19:03
     */
    void updateProductStock(Long orderId);

    /**
     * 商家批量修改线下支付订单
     *
     * @param orderIds the order ids
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void paymentOrder(List<Long> orderIds);

    /**
     * 商家批量修改订单发送状态
     *
     * @param orderIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(List<Long> orderIds,String sendStatus);

    /**
     * 商家批量修改订单收款发送状态
     * @param orderIds
     * @param receiveSyncStatus
     */
    void updateReceiveSyncStatus(List<Long> orderIds,String receiveSyncStatus);


    /**
     * 商家批量备注订单
     *
     * @param orderIds the order ids
     * @param note     the note
     * @param isOver   the is over
     * @return void
     * <AUTHOR>
     * @date 2019 /11/23 19:03
     */
    void noteOrder(List<Long> orderIds, String note, Boolean isOver);

    /**
     * 商家查看评价
     *
     * @param dto the dto
     * @return com.medusa.gruul.common.core.util.PageUtils page utils
     * <AUTHOR>
     * @date 2019 /12/3 20:06
     */
    PageUtils searchOrderEvaluate(ManageSearchEvaluateDto dto);

    /**
     * 商家导出评价列表
     *
     */
    void exportOrderEvaluate(ManageSearchEvaluateDto dto);

    /**
     * 商家设置评价为精选
     *
     * @param ids the ids
     * @return void
     * <AUTHOR>
     * @date 2019 /11/23 19:03
     */
    void choiceEvaluate(List<Long> ids);

    /**
     * 商家回复评价
     *
     * @param id    the id
     * @param reply the reply
     * @return void
     * <AUTHOR>
     * @date 2019 /11/23 19:03
     */
    void replyEvaluate(Long id, String reply);

    /**
     * 商家查询订单详情
     *
     * @param orderId the order id
     * @return com.medusa.gruul.order.model.OrderVo order vo
     * <AUTHOR>
     * @date 2019 /11/26 20:41
     */
    OrderVo orderInfo(Long orderId);

    /**
     * 商家取消设置评价为精选
     *
     * @param asList the as list
     * @return void
     * <AUTHOR>
     * @date 2019 /12/2 21:16
     */
    void unChoiceEvaluate(List<Long> asList);

    /**
     * Search logistics order list page utils.
     *
     * @param manageLogisticsOrderDto the manage logistics order dto
     * @return the page utils
     * 物流批量发货订单列表
     * @param:id
     * @return: com.medusa.gruul.order.model.ManageDeliveryOrderVo
     * @throws:
     * <AUTHOR>
     * @version : v1.0
     * @date 2020 /3/6 7:33 下午
     */
    PageUtils searchLogisticsOrderList(ManageLogisticsOrderDto manageLogisticsOrderDto);

    /**
     * 控制台订单数据概况
     *
     * @param
     * @return com.medusa.gruul.order.model.ManageOrderOverviewVo overview
     * <AUTHOR>
     * @date 2020 /5/6 19:10
     */
    ManageOrderOverviewVo getOverview();
    /**
     * 控制台实时数据概况
     *
     * @param
     * @return com.medusa.gruul.order.model.ManageOrderOverviewVo overview
     * <AUTHOR>
     * @date 2020 /5/6 19:10
     */
    ManageOrderRealTimeOverviewVo getRealTimeOverview(String startDate,String endDate,Integer type) throws ParseException;



    /**
     * searchLogisticsOrder
     *
     * @param dto the dto
     * @return java.util.List<com.medusa.gruul.order.model.ManageOrderVo> list
     * <AUTHOR>
     * @date 2020 /6/17 22:02
     */
    List<ManageOrderVo> searchLogisticsOrder(ManageSearchLogisticsOrderDto dto);

    /**
     * countLogisticsWaitSend
     *
     * @param
     * @return java.lang.Integer integer
     * <AUTHOR>
     * @date 2020 /6/17 22:02
     */
    Integer countLogisticsWaitSend();

    /**
     * logisticsSend
     *
     * @param dto the dto
     * @return void
     * <AUTHOR>
     * @date 2020 /6/17 22:02
     */
    void logisticsSend(ManageSearchLogisticsOrderDto dto);

    /**
     * 商家批量修改审核通过订单
     *
     * @param orderIds the order ids
     */
    void approvedOrder(List<Long> orderIds);

    /**
     * 获取未完成微信收货订单
     * @return
     */
    List<Order> getOrderByWxDeliverStatus();

    /**
     * 获取复购客户数
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getAgainBuyCustom(String startDate,String endDate);

    /**
     * 获取订单商品计算的佣金
     * @param orderId 订单id
     * @param type 0 上级佣金，1上上级佣金
     * @return
     */
    BigDecimal getCommissionByOrderId(Long orderId,Integer type);

    /**
     * 无需快递的物流发货-批量
     * @param manageSearchLogisticsOrderBatchDtoList
     */
    void batchLogisticsSend(List<ManageSearchLogisticsOrderBatchDto> manageSearchLogisticsOrderBatchDtoList);

    /**
     * 批量发货提醒
     * @param orderIds
     */
    void deliverMessage(Long[] orderIds);

    /**
     * 验证是否允许发货
     * @param orderId
     * @return
     */
    Integer vailDeliveryByOrderId(String orderId);

    /**
     * 验证是否允许批量发货
     * @return
     */
    Integer vailDelivery(String orderIds);

    /**
     * 按门店、员工统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    Page manageOrderStatic(ManageOrderStaticDto manageOrderStaticDto);

    /**
     * 按门店、员工统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    ManageOrderStaticVo manageOrderStaticTotal(ManageOrderStaticDto manageOrderStaticDto);

    /**
     * 按权益包统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    Page managePackageStatic(ManageOrderStaticDto manageOrderStaticDto);

    /**
     * 导出权益包汇总
     * @param dto
     */
    void exportPackageStatic(ManageOrderStaticDto dto);

    /**
     * 按权益包统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    ManageOrderStaticVo managePackageStaticTotal(ManageOrderStaticDto manageOrderStaticDto);

    /**
     * 修改订单扣减金额
     * @param dto
     */
    void updateOrderDeductionPrice(ManageOrderDeductionPriceDto dto);

    /**
     * 通过门店classCode获取订单数量
     * @param storeFrontCode
     * @return
     */
    Integer getOrderCountByStoreFrontCode(String storeFrontCode);

    /**
     * 根据订单id获取发货信息
     * @param orderId
     * @return
     */
    List<OrderItemDeliveryVo> getOrderItemDelivery(String orderId);

    /**
     * 后台分页查询待发货信息
     * @param param
     * @return
     */
    PageUtils<OrderDeliveryProxyVo>getOrderDeliveryProxy(OrderDeliveryProxyParam param);

    /**
     * 商家批量发送代发货提醒
     * @param ids
     */
    void deliverProxyMessage(Long[] ids);

    /**
     * 获取批量代发货列表整合数据
     * @param ids
     * @return
     */
    List<ManageOrderVo> getBatchDeliveryProxy(List<Long>ids);

    /**
     * 获取团队订单业绩
     * @param startDate
     * @param endDate
     * @param skuIds
     * @param shopUserIds
     * @return
     */
    BigDecimal getSumAmount(String startDate, String endDate, List<String> skuIds, List<String> shopUserIds);
    /**
     * Pc管理端导出订单列表
     * @param dto
     */
    void exportOrder(ManageSearchOrderDto dto);


    /**
     * 查询未给会员用户产生佣金且状态为完成的订单
     *
     * @param dto the dto
     */
    PageUtils searchByMiniAccount(ManageSearchOrderDto dto);


    /**
     * 批量确认收货
     * @param orderIds
     */
    void batchReceipt(Long[] orderIds);

    /**
     * 获取购买客户数
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getBuyCustom(String startDate,String endDate);


    /**
     * 批量删除订单
     * @param orderIds
     */
    void deleteOrder(List<Long> orderIds);
}
