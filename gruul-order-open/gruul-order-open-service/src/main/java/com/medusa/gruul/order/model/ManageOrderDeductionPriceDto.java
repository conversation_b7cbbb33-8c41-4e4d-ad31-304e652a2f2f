package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:31 2024/12/24
 */
@Data
@ApiModel(value = "管理后台修改扣减金额")
public class ManageOrderDeductionPriceDto {

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "扣减金额")
    private BigDecimal deductionPrice;

}
