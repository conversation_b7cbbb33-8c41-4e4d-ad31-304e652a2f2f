package com.medusa.gruul.order.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.entity.OrderAllot;
import com.medusa.gruul.order.api.entity.OrderAllotDet;
import com.medusa.gruul.order.api.entity.OrderOutStock;
import com.medusa.gruul.order.api.entity.OrderOutStockDet;
import com.medusa.gruul.order.api.model.AddOrderOutStockDto;
import com.medusa.gruul.order.api.model.CreateOrderOutStockMessage;
import com.medusa.gruul.order.api.model.StockOutOrderDetDto;
import com.medusa.gruul.order.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.order.mapper.OrderAllotDetMapper;
import com.medusa.gruul.order.mapper.OrderAllotMapper;
import com.medusa.gruul.order.mapper.OrderOutStockDetMapper;
import com.medusa.gruul.order.mapper.OrderOutStockMapper;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.service.IOrderOutStockService;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:00 2024/10/12
 */
@Service
public class OrderOutStockServiceImpl extends ServiceImpl<OrderOutStockMapper, OrderOutStock>implements IOrderOutStockService {

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Autowired
    private OrderOutStockDetMapper orderOutStockDetMapper;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private OrderAllotMapper orderAllotMapper;

    @Autowired
    private OrderAllotDetMapper orderAllotDetMapper;

    @Override
    public OrderOutStock addOrderOutStock(AddOrderOutStockDto addOrderOutStockDto) {
        OrderOutStock orderOutStock = new OrderOutStock();
        BeanUtils.copyProperties(addOrderOutStockDto,orderOutStock);
        this.baseMapper.insert(orderOutStock);
        return orderOutStock;
    }

    @Override
    @Transactional
    public Boolean addOrderOutStock(CreateOrderOutStockMessage createOrderOutStockMessage) {
        Boolean result = false;
        OrderOutStock orderOutStock = new OrderOutStock();
        BeanUtils.copyProperties(createOrderOutStockMessage,orderOutStock);
        int insert = this.baseMapper.insert(orderOutStock);

        List<StockOutOrderDetDto> detList = createOrderOutStockMessage.getDetList();
        if(detList!=null&&detList.size()>0){
            for (StockOutOrderDetDto stockOutOrderDetDto : detList) {
                OrderOutStockDet orderOutStockDet = new OrderOutStockDet();
                orderOutStockDet.setShopId(orderOutStock.getShopId());
                orderOutStockDet.setTenantId(orderOutStock.getTenantId());

                orderOutStockDet.setProductId(stockOutOrderDetDto.getProductId());
                orderOutStockDet.setSkuId(stockOutOrderDetDto.getSkuId());
                orderOutStockDet.setProductCode(stockOutOrderDetDto.getProductCode());
                orderOutStockDet.setProductName(stockOutOrderDetDto.getProductName());

                orderOutStockDet.setLinkProductId(stockOutOrderDetDto.getLinkProductId());
                orderOutStockDet.setLinkSkuId(stockOutOrderDetDto.getLinkSkuId());
                orderOutStockDet.setLinkProductCode(stockOutOrderDetDto.getLinkProductCode());
                orderOutStockDet.setLinkProductName(stockOutOrderDetDto.getLinkProductName());

                orderOutStockDet.setSpecs2(stockOutOrderDetDto.getSpecs2());
                orderOutStockDet.setNumber(stockOutOrderDetDto.getNumber());
                orderOutStockDet.setAmount(stockOutOrderDetDto.getAmount());
                orderOutStockDet.setPrice(stockOutOrderDetDto.getPrice());
                orderOutStockDet.setMainId(orderOutStock.getId()+"");
                orderOutStockDetMapper.insert(orderOutStockDet);
            }
        }
        if(insert>0){
            //判断是否生成调拨单-订单购买对应仓库不等于核销对应仓库
            if(StringUtils.isNotEmpty(createOrderOutStockMessage.getStockId())
            &&StringUtils.isNotEmpty(createOrderOutStockMessage.getBuyStockId())
            &&!createOrderOutStockMessage.getStockId().equals(createOrderOutStockMessage.getBuyStockId())){
                OrderAllot orderAllot = new OrderAllot();
                BeanUtils.copyProperties(createOrderOutStockMessage,orderAllot);
                orderAllot.setStockInId(createOrderOutStockMessage.getBuyStockId());
                orderAllot.setStockInCode(createOrderOutStockMessage.getBuyStockCode());
                orderAllot.setStockInName(createOrderOutStockMessage.getBuyStockName());
                orderAllot.setStockOutId(createOrderOutStockMessage.getStockId());
                orderAllot.setStockOutCode(createOrderOutStockMessage.getStockCode());
                orderAllot.setStockOutName(createOrderOutStockMessage.getStockName());
                orderAllotMapper.insert(orderAllot);
                if(detList!=null&&detList.size()>0){
                    for (StockOutOrderDetDto stockOutOrderDetDto : detList) {
                        OrderAllotDet orderAllotDet = new OrderAllotDet();
                        orderAllotDet.setShopId(orderAllot.getShopId());
                        orderAllotDet.setTenantId(orderAllot.getTenantId());


                        orderAllotDet.setProductId(stockOutOrderDetDto.getProductId());
                        orderAllotDet.setSkuId(stockOutOrderDetDto.getSkuId());
                        orderAllotDet.setProductCode(stockOutOrderDetDto.getProductCode());
                        orderAllotDet.setProductName(stockOutOrderDetDto.getProductName());

                        orderAllotDet.setLinkProductId(stockOutOrderDetDto.getLinkProductId());
                        orderAllotDet.setLinkSkuId(stockOutOrderDetDto.getLinkSkuId());
                        orderAllotDet.setLinkProductCode(stockOutOrderDetDto.getLinkProductCode());
                        orderAllotDet.setLinkProductName(stockOutOrderDetDto.getLinkProductName());

                        orderAllotDet.setSpecs2(stockOutOrderDetDto.getSpecs2());
                        orderAllotDet.setNumber(stockOutOrderDetDto.getNumber());
                        orderAllotDet.setAmount(stockOutOrderDetDto.getAmount());
                        orderAllotDet.setPrice(stockOutOrderDetDto.getPrice());
                        orderAllotDet.setMainId(orderAllot.getId()+"");
                        orderAllotDetMapper.insert(orderAllotDet);
                    }
                }
            }
            result = true;
        }
        return result;
    }

    @Override
    public PageUtils searchOutStockOutOrder(OutStockOutOrderParam param) {

        Page<OutStockOutOrderVo> page = baseMapper.searchOutStockOutOrder(new Page(param.getCurrent(), param.getSize()), param);
        List<OutStockOutOrderVo> list=page.getRecords();

        if(list!=null&&list.size()>0){
            for (OutStockOutOrderVo outStockOutOrderVo : list) {
                Long id = outStockOutOrderVo.getId();
                LambdaQueryWrapper<OrderOutStockDet>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OrderOutStockDet::getMainId,id);
                List<OrderOutStockDet> detList = orderOutStockDetMapper.selectList(wrapper);
                List<OutStockOutOrderDetVo>dataDetList = new ArrayList<>();
                if(detList!=null&&detList.size()>0){
                    for (OrderOutStockDet orderOutStockDet : detList) {
                        OutStockOutOrderDetVo outStockOutOrderDetVo = new OutStockOutOrderDetVo();
                        outStockOutOrderDetVo.setId(orderOutStockDet.getId());
                        outStockOutOrderDetVo.setNumber(orderOutStockDet.getNumber());
                        outStockOutOrderDetVo.setPrice(orderOutStockDet.getPrice());
                        outStockOutOrderDetVo.setAmount(orderOutStockDet.getAmount());
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(orderOutStockDet.getProductId()));
                        outStockOutOrderDetVo.setProductName(product.getName());
                        outStockOutOrderDetVo.setProductCode(StrUtil.isEmpty(product.getClassCode()) ? "" : product.getClassCode());
                        if(StrUtil.isNotBlank(orderOutStockDet.getLinkProductId())){
                            ProductVo linkProduct = remoteGoodsService.findProductById(Long.valueOf(orderOutStockDet.getLinkProductId()));
                            if(null != linkProduct){
                                outStockOutOrderDetVo.setProductName(linkProduct.getName());
                                outStockOutOrderDetVo.setProductCode(StrUtil.isEmpty(linkProduct.getClassCode()) ? "" : linkProduct.getClassCode());
                            }
                        }
                        outStockOutOrderDetVo.setSpecs2(StrUtil.isEmpty(orderOutStockDet.getSpecs2()) ? "" : orderOutStockDet.getSpecs2());
                        dataDetList.add(outStockOutOrderDetVo);
                    }
                }
                outStockOutOrderVo.setOutStockOutOrderDet(dataDetList);

                String userId = outStockOutOrderVo.getUserId();

                if(StringUtil.isNotEmpty(userId)){
                    //查询用户的会员id
                    AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(userId, Arrays.asList(1));
                    //会员手机号
                    String phone = accountInfoDto.getMiniAccountunt().getPhone();
                    String nikeName = accountInfoDto.getMiniAccountunt().getNikeName();
                    outStockOutOrderVo.setPhone(phone);
                    outStockOutOrderVo.setUserName(nikeName);
                }
            }
        }

        List<Long>idList=list.stream().map(OutStockOutOrderVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public void updateSendStatus(List<Long> orderIds, String sendStatus) {
        baseMapper.updateSendStatus(orderIds,sendStatus);
    }
}

