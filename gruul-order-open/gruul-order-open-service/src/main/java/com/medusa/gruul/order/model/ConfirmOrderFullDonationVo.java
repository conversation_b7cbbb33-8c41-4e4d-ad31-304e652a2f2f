package com.medusa.gruul.order.model;

import com.medusa.gruul.account.api.entity.MiniAccountAddress;
import com.medusa.gruul.goods.api.model.vo.api.ApiShopCouponVo;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.goods.api.model.vo.manager.ShopsItemVo;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:
 * @Description: 订单满减满赠优惠信息
 * @Date:
 */
@Data
@ApiModel(value = "订单满减满赠优惠信息")
public class ConfirmOrderFullDonationVo {


    /**
     * 满减金额
     */
    @ApiModelProperty(value = "满减金额")
    private BigDecimal reductionAmount;

    /**
     * 赠送优惠券
     */
    @ApiModelProperty(value = "赠送优惠券")
    private List<ShopCoupon> shopCouponVos;

    /**
     * 赠送商品列表
     */
    private List<ItemVo> goodsItemVoList;


}
