package com.medusa.gruul.account.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 优惠券明细导出Excel VO类
 */
@Data
@ApiModel(value = "优惠券明细导出Excel VO类")
public class MiniAccountCouponSearchExcelVo {

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券面额")
    private BigDecimal promotion;

    @ApiModelProperty(value = "使用人")
    private String useName;

    @ApiModelProperty(value = "使用店面")
    private String useShopName;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "使用时间")
    private String useTime;
}
