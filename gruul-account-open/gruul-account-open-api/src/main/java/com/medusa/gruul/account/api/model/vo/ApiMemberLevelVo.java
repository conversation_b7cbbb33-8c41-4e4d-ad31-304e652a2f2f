package com.medusa.gruul.account.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:16 2025/3/28
 */
@Data
@ApiModel(value = "ApiMemberLevelVo对象", description = "小程序端我的等级数据展示")
public class ApiMemberLevelVo {

    @ApiModelProperty(value = "会员id")
    private String memberId;

    @ApiModelProperty(value = "会员头像")
    private String avatarUrl;

    @ApiModelProperty(value = "当前会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "加入时间")
    private String joinTime;

    @ApiModelProperty(value = "加入时长")
    private String joinDays;

    @ApiModelProperty(value = "到期时间")
    private String endTime;

    @ApiModelProperty(value = "到期时长")
    private String endDays;

    @ApiModelProperty(value = "下一个会员等级")
    private String nextMemberLevel;

    @ApiModelProperty(value = "会员消费金额")
    private BigDecimal memberAmount;

    @ApiModelProperty(value = "下一个等级消费金额")
    private BigDecimal nextAmount;

    @ApiModelProperty(value = "下一等级还差金额")
    private BigDecimal nextUpgradeAmount;

    @ApiModelProperty(value = "会员说明")
    private String description;

    @ApiModelProperty(value = "已完成直推会员数")
    private Integer alreadyMemberNumber;

    @ApiModelProperty(value = "剩余直推会员数")
    private Integer missMemberNumber;

    @ApiModelProperty(value = "已完成直推代理数2")
    private Integer alreadyMemberNumber2;

    @ApiModelProperty(value = "剩余直推代理数2")
    private Integer missMemberNumber2;

    @ApiModelProperty(value = "待发货数量")
    private Integer unDeliveryQuantity;

    @ApiModelProperty(value = "是否可以申请入驻：0.否；1.是")
    private Integer applyShopsFlag;

    @ApiModelProperty(value = "会员体系会员->0-否，1-是")
    private Integer memberFlag;
}
