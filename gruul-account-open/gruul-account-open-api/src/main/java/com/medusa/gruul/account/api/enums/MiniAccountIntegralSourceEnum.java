package com.medusa.gruul.account.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:35 2025/7/8
 */
@Getter
public enum MiniAccountIntegralSourceEnum {

    SYSTEM(0, "系统"),

    BACKEND(1, "后台"),
    ;
    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;


    MiniAccountIntegralSourceEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
