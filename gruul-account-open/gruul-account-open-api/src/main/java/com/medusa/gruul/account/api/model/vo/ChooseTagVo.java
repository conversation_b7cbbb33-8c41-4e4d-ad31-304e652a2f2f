package com.medusa.gruul.account.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:55 2025/4/2
 */
@Data
@ApiModel(value = "可选择标签")
public class ChooseTagVo {

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签id")
    private Long tagId;

    @ApiModelProperty(value = "客户数量")
    private Integer accountNum;

}
