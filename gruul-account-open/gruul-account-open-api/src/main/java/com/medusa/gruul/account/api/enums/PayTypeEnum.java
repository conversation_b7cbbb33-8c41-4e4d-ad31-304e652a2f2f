package com.medusa.gruul.account.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:07 2025/6/9
 */
@Getter
public enum PayTypeEnum {

    /**
     * 支付方式
     */
    WX_PAY(102, "微信支付"),
    OFFLINE_PAY(1001, "线下支付");

    @EnumValue
    /**
     * 值
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    PayTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
