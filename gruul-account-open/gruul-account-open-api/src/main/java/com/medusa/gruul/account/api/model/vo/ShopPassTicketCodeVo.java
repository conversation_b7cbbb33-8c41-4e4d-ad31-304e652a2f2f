package com.medusa.gruul.account.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShopPassTicketCodeVo", description = "商家核销列表实体类")
public class ShopPassTicketCodeVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 通惠证id
     */
    @ApiModelProperty(value = "通惠证id")
    private Long passTicketId;

    /**
     * 通惠证名称
     */
    @ApiModelProperty(value = "通惠证名称")
    private String passTicketName;

    /**
     * 用户通惠证记录id
     */
    @ApiModelProperty(value = "用户通惠证id")
    private Long miniAccountPassTicketId;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名")
    private String nickName;

    /**
     * 会员手机号
     */
    @ApiModelProperty(value = "会员手机号")
    private String userMobile;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    /**
     * 核销人id
     */
    @ApiModelProperty(value = "核销人id")
    private Long verifyUserId;

    /**
     * 核销人手机号
     */
    @ApiModelProperty(value = "核销人手机号")
    private String verifyUserMobile;

    /**
     * 核销人姓名
     */
    @ApiModelProperty(value = "核销人姓名")
    private String verifyNickName;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

}
