package com.medusa.gruul.account.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:05 2025/6/3
 */
@Getter
@AllArgsConstructor
public enum MemberActiveSettingStatusEnum {

    NO(0, "停用"),
    YES(1, "启用");
    /**
     * 状态
     */
    private Integer status;

    /**
     * 名称
     */
    private String name;

}
