package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponCodeParam;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponSearchParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo;
import com.medusa.gruul.account.model.dto.MiniAccountCouponCodeDto;
import com.medusa.gruul.account.model.vo.MiniAccountCouponCodeVo;
import com.medusa.gruul.account.service.IMiniAccountCouponCodeService;
import com.medusa.gruul.account.service.IMiniAccountCouponService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:32 2024/8/28
 */
@RestController
@RequestMapping("/mini-account-coupon")
@Api(tags = "用户优惠券相关接口")
public class MiniAccountCouponController {

    @Autowired
    private IMiniAccountCouponService miniAccountCouponService;

    @Autowired
    private IMiniAccountCouponCodeService miniAccountCouponCodeService;
    /**
     * 分页获取优惠优惠券列表
     * @param miniAccountCouponSearchParam
     * @return
     */
    @PostMapping("/getMiniAccountCouponSearchVo")
    @ApiOperation(value = "分页获取优惠优惠券列表")
    public Result<PageUtils<MiniAccountCouponSearchVo>> getMiniAccountCouponSearchVo(@RequestBody  MiniAccountCouponSearchParam miniAccountCouponSearchParam){
        IPage<MiniAccountCouponSearchVo> miniAccountCouponSearchVo = miniAccountCouponService.getMiniAccountCouponSearchVo(miniAccountCouponSearchParam);
        return Result.ok(new PageUtils<>(miniAccountCouponSearchVo));
    }

    /**
     * 获取优惠券验证码
     *
     * @param miniAccountCouponCodeDto
     * @return
     */
    @PostMapping("/getCode")
    @ApiOperation(value = "获取优惠券验证码")
    public Result<MiniAccountCouponCodeVo> getCode(@RequestBody MiniAccountCouponCodeDto miniAccountCouponCodeDto) {
        MiniAccountCouponCodeVo codeVo = miniAccountCouponCodeService.getCode(miniAccountCouponCodeDto);
        return Result.ok(codeVo);
    }

    @RequestMapping(value = "/export/shopCouponCode", method = RequestMethod.POST)
    @ApiOperation(value = "导出商家核销记录")
    public void exportShopCouponCode(@RequestBody MiniAccountCouponCodeParam miniAccountCouponCodeParam){
        this.miniAccountCouponCodeService.exportShopCouponCode(miniAccountCouponCodeParam);
    }

    /**
     * 导出优惠券明细
     * @param param
     */
    @PostMapping("/exportMiniAccountCouponSearch")
    @ApiOperation(value = "导出优惠券明细")
    public void exportMiniAccountCouponSearch(@RequestBody MiniAccountCouponSearchParam param) {
        miniAccountCouponService.exportMiniAccountCouponSearch(param);
    }
}
