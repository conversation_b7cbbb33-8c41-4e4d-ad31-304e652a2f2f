package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeAuditDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDeleteDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDto;
import com.medusa.gruul.account.model.param.MiniAccountGoldenChangeParam;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenChangeVo;
import com.medusa.gruul.account.service.IMiniAccountGoldenChangeService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.dto.ApproveDataParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:56 2025/7/21
 */
@RestController
@RequestMapping("/mini-account-golden-change")
@Api(tags = "管理后台-金豆变更记录相关接口")
public class MiniAccountGoldenChangeController {

    @Autowired
    private IMiniAccountGoldenChangeService miniAccountGoldenChangeService;

    /**
     * 新增金豆变更记录
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增金豆变更记录")
    public Result add(@RequestBody @Validated MiniAccountGoldenChangeDto dto){
        miniAccountGoldenChangeService.addMiniAccountGoldenChange(dto);
        return Result.ok();
    }

    /**
     * 编辑金豆变更记录
     * @param dto
     * @return
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑金豆变更记录")
    public Result edit(@RequestBody @Validated MiniAccountGoldenChangeDto dto){
        miniAccountGoldenChangeService.editMiniAccountGoldenChange(dto);
        return Result.ok();
    }

    /**
     * 删除金豆变更记录
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除金豆变更记录")
    public Result delete(@RequestBody @Validated MiniAccountGoldenChangeDeleteDto dto){
        miniAccountGoldenChangeService.deleteMiniAccountGoldenChange(dto);
        return Result.ok();
    }

    /**
     * 审核金豆变更记录
     * @param dto
     * @return
     */
    @PostMapping("/audit")
    @ApiOperation(value = "审核金豆变更记录")
    public Result audit(@RequestBody @Validated MiniAccountGoldenChangeAuditDto dto){
        miniAccountGoldenChangeService.auditMiniAccountGoldenChange(dto);
        return Result.ok();
    }

    /**
     * 分页查询金豆变更记录
     * @param param
     * @return
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "分页查询金豆变更记录")
    public Result queryList(@RequestBody MiniAccountGoldenChangeParam param){
        IPage<MiniAccountGoldenChangeVo> page = miniAccountGoldenChangeService.queryList(param);
        return Result.ok(new PageUtils<>(page));
    }

    /**
     * 根据id查询金豆变更记录
     * @param param
     * @return
     */
    @PostMapping("/getMiniAccountGoldenChange")
    @ApiOperation(value = "根据id查询金豆变更记录")
    public Result getMiniAccountGoldenChange(@RequestBody MiniAccountGoldenChangeParam param){
        MiniAccountGoldenChangeVo miniAccountGoldenChangeVo = miniAccountGoldenChangeService.getMiniAccountGoldenChangeVo(param);
        return Result.ok(miniAccountGoldenChangeVo);
    }



}
