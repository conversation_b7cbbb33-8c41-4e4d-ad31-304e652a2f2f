package com.medusa.gruul.account.model.vo;

import com.medusa.gruul.order.api.model.OrderOverviewVo;
import com.medusa.gruul.platform.api.model.dto.ShopPackageFunctionDto;
import com.medusa.gruul.shops.api.model.AccountCenterVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @data: 2022/2/24
 */
@Data
public class MemberLevePriceVo {
    private Long id;
    @ApiModelProperty(value = "商品规格")
    private String specs;
    @ApiModelProperty(value = "会员等级id")
    private String memberLevelId;
    @ApiModelProperty(value = "会员等级")
    private String memberLevel;
    @ApiModelProperty(value = "零售价")
    private BigDecimal price;
    @ApiModelProperty(value = "指导价")
    private BigDecimal originalPrice;
    @ApiModelProperty(value = "会员价")
    private BigDecimal memberLevelPrice;
    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberLevelPercentage;
    @ApiModelProperty(value = "商品规格Id")
    private Long skuId;

}
