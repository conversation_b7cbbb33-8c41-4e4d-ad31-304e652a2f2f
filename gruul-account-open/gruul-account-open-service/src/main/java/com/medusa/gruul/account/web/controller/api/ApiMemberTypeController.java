package com.medusa.gruul.account.web.controller.api;

import com.medusa.gruul.account.api.entity.MemberType;
import com.medusa.gruul.account.model.vo.RegionMemberLevelVo;
import com.medusa.gruul.account.service.IMemberTypeService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:38 2025/7/10
 */
@RestController
@RequestMapping("/api/member-type")
@Api(tags = "小程序会员类型相关接口")
public class ApiMemberTypeController {

    @Autowired
    private IMemberTypeService memberTypeService;

    /**
     * 获取默认会员类型
     * @return
     */
    @PostMapping("/getDefaultMemberType")
    @ApiOperation(value = "获取默认会员类型")
    public Result getRegionMemberLevel(){
        MemberType memberType = memberTypeService.getDefaultMemberType();
        return Result.ok(memberType);
    }

}
