package com.medusa.gruul.account.mq;

import com.medusa.gruul.account.api.constant.AccountExchangeConstant;
import com.medusa.gruul.account.api.constant.AccountQueueNameConstant;
import com.medusa.gruul.account.api.enums.AccountQueueEnum;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.constant.OrderQueueEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory;
import org.springframework.messaging.handler.annotation.support.MessageHandlerMethodFactory;

import javax.annotation.Resource;

/**
 * @Description: RabbitConfig.java
 * @Author: alan
 * @Date: 2019/10/6 14:01
 */
@Slf4j
@Configuration
public class RabbitConfig implements RabbitListenerConfigurer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void configureRabbitListeners(RabbitListenerEndpointRegistrar registrar) {
        registrar.setMessageHandlerMethodFactory(messageHandlerMethodFactory());
    }

    @Bean
    MessageHandlerMethodFactory messageHandlerMethodFactory() {
        DefaultMessageHandlerMethodFactory messageHandlerMethodFactory = new DefaultMessageHandlerMethodFactory();
        messageHandlerMethodFactory.setMessageConverter(consumerJackson2MessageConverter());
        return messageHandlerMethodFactory;
    }

    @Bean
    public MappingJackson2MessageConverter consumerJackson2MessageConverter() {
        return new MappingJackson2MessageConverter();
    }

    @Bean
    public AmqpTemplate amqpTemplate() {
        // 使用jackson 消息转换器
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        rabbitTemplate.setEncoding("UTF-8");
        // 消息发送失败返回到队列中，yml需要配置 publisher-returns: true
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            String correlationId = message.getMessageProperties().getCorrelationId();
            log.info("消息：{} 发送失败, 应答码：{} 原因：{} 交换机: {}  路由键: {}", correlationId, replyCode, replyText, exchange,
                    routingKey);
        });
        // 消息确认，yml需要配置 publisher-confirms: true
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送到exchange成功,id: {}", correlationData.getId());
            } else {
                log.info("消息发送到exchange失败,原因: {}", cause);
            }
        });
        return rabbitTemplate;
    }

    /**
     * 用户服务积分交换机
     */
    @Bean
    DirectExchange accountDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.ACCOUNT_EXCHANGE).durable(true)
                .build();
    }

    /**
     * 权益包订单交换机
     * @return
     */
    @Bean
    DirectExchange accountPackageOrderDirect(){
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.ACCOUNT_PACKAGE_ORDER_EXCHANGE).durable(true)
                .build();
    }

    /**
     * 发送优惠券交换机
     * @return
     */
    @Bean
    DirectExchange sendCouponDirect(){
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.SEND_COUPON_EXCHANGE).durable(true)
                .build();
    }

    @Bean
    public Queue sendCouponQueue() {
        return new Queue(AccountQueueNameConstant.SEND_COUPON, true);
    }

    @Bean
    Binding sendCouponBinding(DirectExchange sendCouponDirect, Queue sendCouponQueue) {
        return BindingBuilder.bind(sendCouponQueue).to(sendCouponDirect)
                .with(AccountQueueEnum.QUEUE_SEND_COUPON.getRouteKey());
    }


    /**
     * 会员支付服务交换机
     */
    @Bean
    DirectExchange memberDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.MEMBER_EXCHANGE).durable(true)
                .build();
    }

    /**
     * 会员佣金提现交换机
     */
    @Bean
    DirectExchange cashDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.CASH_EXCHANGE).durable(true)
                .build();
    }

    /**
     * 用户服务积分队列
     */
    @Bean
    public Queue accountIntegralQueue() {
        return new Queue(AccountQueueEnum.QUEUE_ACCOUNT_INTEGRAL_CHANGE.getName(), true);
    }


    /**
     * 绑定积分加减roukey
     */
    @Bean
    Binding integralChangeBinding(DirectExchange accountDirect, Queue accountIntegralQueue) {
        return BindingBuilder.bind(accountIntegralQueue).to(accountDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_INTEGRAL_CHANGE.getRouteKey());
    }


    /**
     * 订单消息实际消费队列所绑定的交换机
     */
    @Bean
    DirectExchange orderDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(OrderConstant.EXCHANGE_NAME).durable(true).build();
    }

    /**
     * 用户订单支付成功队列
     */
    @Bean
    public Queue orderPayOkQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE, true);
    }


    /**
     * 将订单已支付队列绑定到交换机
     */
    @Bean
    Binding orderPayBinding(DirectExchange orderDirect, Queue orderPayOkQueue) {
        return BindingBuilder.bind(orderPayOkQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_PAYED.getRouteKey());
    }

    @Bean
    public Queue packageOrderStatusUpdateQueue() {
        return new Queue(AccountQueueNameConstant.PACKAGE_ORDER_STATUS_CHANGE, true);
    }

    @Bean
    Binding packageOrderStatusUpdateBinding(DirectExchange accountPackageOrderDirect, Queue packageOrderStatusUpdateQueue) {
        return BindingBuilder.bind(packageOrderStatusUpdateQueue).to(accountPackageOrderDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_PACKAGE_STATUS_UPDATE.getRouteKey());
    }


    /**
     * 用户积分订单支付成功队列
     */
    @Bean
    public Queue integralOrderPayOkQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_INTEGRAL_ORDER_PAY_OK_QUEUE_CHANGE, true);
    }

    /**
     * 权益包订单支付成功队列
     */
    @Bean
    Binding integralOrderPayBinding(DirectExchange orderDirect, Queue integralOrderPayOkQueue) {
        return BindingBuilder.bind(integralOrderPayOkQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_PAYED.getRouteKey());
    }
    /**
     * 用户权益包订单签收成功处理权益包信息队列
     */
    @Bean
    public Queue orderPayOkPackageQueue(){
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_PACKAGE, true);
    }
    /**
     * 将用户通惠证订单支付成功处理通惠证信息队列绑定到交换机
     */
    @Bean
    Binding orderPayOkPackageBing(DirectExchange orderDirect, Queue orderPayOkPackageQueue){
        return BindingBuilder.bind(orderPayOkPackageQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_PAYED.getRouteKey());
    }

    /**
     * 用户订单支付成功处理积分信息队列
     */
    @Bean
    public Queue orderPayOkExchangeQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_EXCHANGE, true);
    }

    /**
     * 将订单完成处理积分信息队列绑定到交换机
     */
    @Bean
    Binding orderPayOkExchangeBing(DirectExchange orderDirect, Queue orderPayOkExchangeQueue) {
        return BindingBuilder.bind(orderPayOkExchangeQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_COMPLETED.getRouteKey());
    }

    /**
     * 用户通惠证订单签收成功处理通惠证信息队列
     */
    @Bean
    public Queue orderPayOkTicketQueue(){
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_TICKET, true);
    }
    /**
     * 将用户通惠证订单支付成功处理通惠证信息队列绑定到交换机
     */
    @Bean
    Binding orderPayOkTicketBing(DirectExchange orderDirect, Queue orderPayOkTicketQueue){
        return BindingBuilder.bind(orderPayOkTicketQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_RECEIPT.getRouteKey());
    }



    /**
     * 用户订单完成成功队列
     */
    @Bean
    public Queue orderCompleteOkQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_COMPLETE_OK_QUEUE_CHANGE, true);
    }

    /**
     * 将订单完成队列绑定到交换机
     */
    @Bean
    Binding orderCompletedBinding(DirectExchange orderDirect, Queue orderCompleteOkQueue) {
        return BindingBuilder.bind(orderCompleteOkQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_COMPLETED.getRouteKey());
    }
    /**
     * 用户订单完成处理佣金队列
     */
    @Bean
    public Queue orderCompleteOkAddCommissionQueue(){
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_COMPLETE_OK_QUEUE_CHANGE_ADD_COMMISSION, true);
    }
    /**
     * 将订单完成处理佣金队列绑定到交换机
     */
    @Bean
    Binding orderCompletedAddCommissionBinding(DirectExchange orderDirect, Queue orderCompleteOkAddCommissionQueue) {
        return BindingBuilder.bind(orderCompleteOkAddCommissionQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_COMPLETED.getRouteKey());
    }
    @Bean
    public Queue accountCommissionCashOkQueue(){
        return new Queue(AccountQueueNameConstant.ACCOUNT_CASH_OK,true);
    }
    @Bean
    Binding accountCommissionCashOkBinding(DirectExchange accountDirect,Queue accountCommissionCashOkQueue){
        return BindingBuilder.bind(accountCommissionCashOkQueue).to(accountDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_CASH.getRouteKey());
    }

    @Bean
    public Queue accountCollectQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_COLLECT, true);
    }

    /**
     * 绑定用户收藏roukey
     */
    @Bean
    Binding accountCollectBinding(DirectExchange accountDirect, Queue accountIntegralQueue) {
        return BindingBuilder.bind(accountIntegralQueue).to(accountDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_COLLECT.getRouteKey());
    }


    @Bean
    public Queue accountDefaultQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_DEFAULT, true);
    }



    /**
     * 会员购买支付成功队列
     */
    @Bean
    public Queue memberPayOkQueue() {
        return new Queue(AccountQueueNameConstant.MEMBER_PAY_OK_QUEUE_CHANGE, true);
    }


    /**
     * 用户佣金交换机
     * @return
     */
    @Bean
    DirectExchange commissionCashDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(AccountExchangeConstant.MINI_ACCOUNT_COMMISSION_CASH_EXCHANGE).durable(true)
                .build();
    }

    /**
     * 用户佣金记录更新队列
     * @return
     */
    @Bean
    public Queue commissionCashQueue() {
        return new Queue(AccountQueueEnum.QUEUE_MINI_ACCOUNT_COMMISSION_CASH_UPDATE.getName(), true);
    }

    /**
     * 绑定用户佣金记录变更队列到佣金交换机
     * @param commissionCashDirect
     * @param commissionCashQueue
     * @return
     */
    @Bean
    Binding commissionBinding(DirectExchange commissionCashDirect,Queue commissionCashQueue){
        return BindingBuilder.bind(commissionCashQueue).to(commissionCashDirect)
                .with(AccountQueueEnum.QUEUE_MINI_ACCOUNT_COMMISSION_CASH_UPDATE.getRouteKey());
    }

    /**
     * 归还用户余额队列
     * @return
     */
    @Bean
    public Queue accountRevertBalanceQueue() {
        return new Queue(AccountQueueEnum.QUEUE_ACCOUNT_REVERT_BALANCE.getName(), true);
    }

    /**
     * 绑定归还用户余额队列到用户交换机
     * @param accountDirect
     * @param accountRevertBalanceQueue
     * @return
     */
    @Bean
    Binding revertAccountBalanceBinding(DirectExchange accountDirect,Queue accountRevertBalanceQueue){
        return BindingBuilder.bind(accountRevertBalanceQueue).to(accountDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_REVERT_BALANCE.getRouteKey());
    }

    /**
     * 升级会员等级队列
     * @return
     */
    @Bean
    public Queue accountUpgradeMemberLevelQueue() {
        return new Queue(AccountQueueEnum.QUEUE_ACCOUNT_UPGRADE_MEMBER_LEVEL.getName(), true);
    }

    /**
     * 绑定升级会员等级队列到用户交换机
     * @param accountDirect
     * @param accountUpgradeMemberLevelQueue
     * @return
     */
    @Bean
    Binding accountUpgradeMemberLevelBinding(DirectExchange accountDirect,Queue accountUpgradeMemberLevelQueue){
        return BindingBuilder.bind(accountUpgradeMemberLevelQueue).to(accountDirect)
                .with(AccountQueueEnum.QUEUE_ACCOUNT_UPGRADE_MEMBER_LEVEL.getRouteKey());
    }
    /**
     * 订单删除成功队列
     */
    @Bean
    public Queue orderDeleteQueue() {
        return new Queue(AccountQueueNameConstant.ACCOUNT_ORDER_DELETE, true);
    }

    /**
     * 订单删除成功队列绑定订单交换机
     */
    @Bean
    Binding orderDeleteBinding(DirectExchange orderDirect, Queue orderDeleteQueue) {
        return BindingBuilder.bind(orderDeleteQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_DELETE.getRouteKey());
    }

}
