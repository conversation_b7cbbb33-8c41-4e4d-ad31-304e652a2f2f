package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:19 2025/6/17
 */
@Data
@ApiModel(value = "前往升级返回数据")
public class UpgradeMemberDataVo {

    @ApiModelProperty(value = "修改标识：0.可修改；1.不可修改")
    private Integer updateFlag;

    @ApiModelProperty(value = "购买标识：0.否；1.是")
    private Integer buyFlag;

    @ApiModelProperty(value = "升级订单标识：0.否；1.是")
    private Integer updateOrder;

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

    @ApiModelProperty(value = "会员等级id")
    private Long memberLevelId;

    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;

    @ApiModelProperty(value = "前往升级返回商品数据")
    private List<UpgradeMemberProductVo> upgradeMemberProductList;


}
