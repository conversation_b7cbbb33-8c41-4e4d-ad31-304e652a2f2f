package com.medusa.gruul.account.web.controller.api;

import com.medusa.gruul.account.api.entity.MiniAccountCommissionCash;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionCashDto;
import com.medusa.gruul.account.model.param.MiniAccountCommissionCashParam;
import com.medusa.gruul.account.model.vo.ApiCommissionCashVo;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashVo;
import com.medusa.gruul.account.service.IMiniAccountCommissionCashService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: plh
 * @Description: 小程序-提现相关接口
 * @Date: Created in 14:22 2023/8/31
 */
@RestController
@RequestMapping("/api/mini-account-commission-cash")
@Api(tags = "小程序-提现相关接口")
public class ApiMiniAccountCommissionCashController {

    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;

    /**
     * 添加提现申请记录
     * @param miniAccountCommissionCashDto
     * @return
     */
    @PostMapping("/addMiniAccountCommissionCash")
    @ApiOperation(value = "添加提现申请记录")
    public Result addMiniAccountCommissionCash(@RequestBody @Validated MiniAccountCommissionCashDto miniAccountCommissionCashDto){
        Map<String,String> result = miniAccountCommissionCashService.add(miniAccountCommissionCashDto);
        return Result.ok(result);
    }

    /**
     * 获取当前用户提现记录
     * @param miniAccountCommissionCashParam
     * @return
     */
    @PostMapping("/getMiniAccountCommissionCashVo")
    @ApiOperation(value = "获取当前用户提现记录")
    public Result<PageUtils<MiniAccountCommissionCashVo>>getMiniAccountCommissionCashVo(@RequestBody @Validated MiniAccountCommissionCashParam miniAccountCommissionCashParam){
        PageUtils<MiniAccountCommissionCashVo> miniAccountCommissionCashVoPageUtils = miniAccountCommissionCashService.getMiniAccountCommissionCashVoByShopUserId(miniAccountCommissionCashParam);
        return Result.ok(miniAccountCommissionCashVoPageUtils);
    }

    /**
     * 获取用户提现信息
     * @return
     */
    @PostMapping("/getApiCommissionCash")
    @ApiOperation(value = "获取用户提现信息")
    public Result<ApiCommissionCashVo>getApiCommissionCash(){
        ApiCommissionCashVo apiCommissionCashVo =  miniAccountCommissionCashService.getApiCommissionCash();
        return Result.ok(apiCommissionCashVo);
    }


}
