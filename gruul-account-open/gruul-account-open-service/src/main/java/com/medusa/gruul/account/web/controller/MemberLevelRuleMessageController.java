package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.api.entity.MemberLevelRule;
import com.medusa.gruul.account.api.entity.MemberLevelRuleMessage;
import com.medusa.gruul.account.model.dto.MemberLevelRuleMessageDto;
import com.medusa.gruul.account.model.param.MemberLevelParam;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.MemberLevelRuleMessageVo;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;
import com.medusa.gruul.account.service.IMemberLevelRuleMessageService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:03 2025/3/18
 */
@RestController
@RequestMapping("/member-level-rule-message")
@Api(tags = "会员等级规则相关接口")
public class MemberLevelRuleMessageController {

    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;

    /**
     * 添加或者编辑会员等级规则信息
     * @param memberLevelRuleMessageDto
     * @return
     */
    @PostMapping("/addOrUpdateMemberLevelRuleMessage")
    @ApiOperation(value = "添加或者编辑会员等级规则信息")
    public Result addOrUpdateMemberLevelRuleMessage(@RequestBody MemberLevelRuleMessageDto memberLevelRuleMessageDto) {
        String result = memberLevelRuleMessageService.addOrUpdateMemberLevelRuleMessage(memberLevelRuleMessageDto);
        return Result.ok(result);
    }

    /**
     * 获取会员等级规则信息
     * @return
     */
    @PostMapping("/getMemberLevelRuleMessage")
    @ApiOperation(value = "获取会员等级规则信息")
    public Result getMemberLevelRuleMessage(@RequestBody MemberLevelRuleMessageParam param){
        MemberLevelRuleMessageVo memberLevelRuleMessageVo = memberLevelRuleMessageService.getMemberLevelRuleMessage(param);
        return Result.ok(memberLevelRuleMessageVo);
    }


}
