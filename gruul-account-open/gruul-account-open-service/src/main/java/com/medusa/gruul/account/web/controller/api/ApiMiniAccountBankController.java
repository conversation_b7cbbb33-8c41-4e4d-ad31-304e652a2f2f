package com.medusa.gruul.account.web.controller.api;

import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MiniAccountBank;
import com.medusa.gruul.account.model.dto.MiniAccountBankDto;
import com.medusa.gruul.account.model.param.MiniAccountBankParam;
import com.medusa.gruul.account.model.vo.MiniAccountBankVo;
import com.medusa.gruul.account.service.IMiniAccountBankService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:21 2025/5/27
 */
@RestController
@RequestMapping("/api/mini-account-bank")
@Api(tags = "小程序用户银行卡相关接口")
public class ApiMiniAccountBankController {

    @Autowired
    private IMiniAccountBankService miniAccountBankService;

    /**
     * 新增会员银行卡
     * @param dto
     * @return
     */
    @PostMapping("/addMiniAccountBank")
    @ApiOperation(value = "新增会员银行卡")
    public Result addMiniAccountBank(@RequestBody MiniAccountBankDto dto) {
        miniAccountBankService.addMiniAccountBank(dto);
        return Result.ok("新增成功");
    }

    /**
     * 编辑会员银行卡
     * @param dto
     * @return
     */
    @PostMapping("/editMiniAccountBank")
    @ApiOperation(value = "编辑会员银行卡")
    public Result editMiniAccountBank(@RequestBody MiniAccountBankDto dto) {
        miniAccountBankService.editMiniAccountBank(dto);
        return Result.ok("编辑成功");
    }

    /**
     * 删除会员银行卡
     * @param dto
     * @return
     */
    @PostMapping("/removeMiniAccountBank")
    @ApiOperation(value = "删除会员银行卡")
    public Result removeMiniAccountBank(@RequestBody MiniAccountBankDto dto) {
        miniAccountBankService.removeMiniAccountBank(dto);
        return Result.ok("删除成功");
    }

    /**
     * 分页查询会员银行卡
     * @param param
     * @return
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "分页查询会员银行卡")
    public Result queryList(@RequestBody MiniAccountBankParam param) {
        PageUtils<MiniAccountBankVo> pageUtils = miniAccountBankService.queryList(param);
        return Result.ok(pageUtils);
    }

}
