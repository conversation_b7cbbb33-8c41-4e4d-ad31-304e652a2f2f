package com.medusa.gruul.account.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(value = "用户会员权益到期时间")
@Data
public class UserMemberLevelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会员类型名称")
    private String memberTypeName;

    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;

    @ApiModelProperty(value = "加入时间")
    private String joinTime;

    @ApiModelProperty(value = "加入时长")
    private String joinDays;

    @ApiModelProperty(value = "到期时间")
    private String endTime;

    @ApiModelProperty(value = "到期时长")
    private String endDays;

    @ApiModelProperty(value = "会员体系会员->0-否，1-是")
    private Integer memberFlag;
}
