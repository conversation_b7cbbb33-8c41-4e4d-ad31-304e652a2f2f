package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 会员类型汇总视图对象
 */
@Data
@ApiModel(value = "会员类型汇总数据")
public class MemberTypeReportVo {
    
    @ApiModelProperty(value = "会员类型ID")
    private String memberTypeId;
    
    @ApiModelProperty(value = "会员类型名称")
    private String memberTypeName;
    
    @ApiModelProperty(value = "会员数量")
    private Integer memberCount;
    
    @ApiModelProperty(value = "消费总金额")
    private BigDecimal totalAmount;
}
