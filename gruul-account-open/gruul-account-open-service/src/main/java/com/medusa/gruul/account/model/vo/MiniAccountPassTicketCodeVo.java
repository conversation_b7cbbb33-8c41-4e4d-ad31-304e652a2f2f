package com.medusa.gruul.account.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 会员通行票验证码
 * <AUTHOR>
 */
@Data
@ApiModel(value = "会员通行票验证码展示对象")
public class MiniAccountPassTicketCodeVo {


    @ApiModelProperty(value = "用户通行票id")
    private Long miniAccountPassTicketId;


    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    @ApiModelProperty(value = "验证码二维码图片")
    private String qrCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}
