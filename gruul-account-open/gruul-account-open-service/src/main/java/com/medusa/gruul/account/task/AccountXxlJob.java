package com.medusa.gruul.account.task;

import com.medusa.gruul.account.service.IMiniAccountCommissionCashService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:58 2024/5/11
 */
@Component
@Slf4j
public class AccountXxlJob {

    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;
    @Autowired
    private IMiniAccountService miniAccountService;

    @XxlJob("UpdateCashJob")
    public ReturnT<String> UpdateCashJob(String param) throws Exception {
//        log.info("-----------佣金提现更新支付状态定时任务开启-----------");
//        miniAccountCommissionCashService.updatePayStatus();
//        log.info("-----------佣金提现更新支付状态定时任务结束-----------");
        return ReturnT.SUCCESS;
    }
    @XxlJob("UpdateMiniAccountLevel")
    public ReturnT<String> UpdateMiniAccountLevel(String param) throws Exception {
//        log.info("-----------用户会员等级定时任务开启-----------");
        miniAccountService.updateMiniAccountLevel();
//        log.info("-----------用户会员等级定时任务结束-----------");
        return ReturnT.SUCCESS;
    }
    @XxlJob("SendBirthDayWxMessage")
    public ReturnT<String> SendBirthDayWxMessage(String param) throws Exception {
        log.info("-----------发送生日祝福任务开启-----------");
        miniAccountService.sendBirthDayWxMessage();
        log.info("-----------发送生日祝福任务结束-----------");
        return ReturnT.SUCCESS;
    }
}
