package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoodsCode;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.PackageGoodsCodeDetailParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsCodeDetailVo;
import com.medusa.gruul.account.api.model.vo.ShopPackageGoodsCodeVo;
import com.medusa.gruul.account.model.dto.MiniAccountPackageGoodsDto;
import com.medusa.gruul.account.model.dto.VerifyGoodsDto;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsCodeVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:45 2024/9/11
 */
@RestController
@RequestMapping("/mini-platform/packageGoods")
@Api(tags = "小程序后台用户-权益包商品验证码相关接口")
public class MiniPlatformAccountPackageGoodsCodeController {

    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;
    @Autowired
    private IMiniAccountPackageGoodsCodeService miniAccountPackageGoodsCodeService;

    /**
     * 核销验证码
     *
     * @return
     */
    @PostMapping("/verifyPackageGoodsCode")
    @ApiOperation(value = "核销验证码")
    public Result<MiniAccountPackageGoodsCode> verifyPassTicketCode(@RequestBody VerifyGoodsDto dto) {
        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = miniAccountPackageGoodsCodeService.verifyCode(dto);
        return Result.ok(miniAccountPackageGoodsCode);
    }

    /**
     * 分页获取商家用户查询自己核销的记录
     *
     * @param miniAccountPackageGoodsCodeParam 分页数据
     * @return
     */
    @PostMapping("/pageUserPackageGoods")
    @ApiOperation(value = "分页获取商家用户查询自己核销的记录")
    public Result<IPage<ShopPackageGoodsCodeVo>> pageShopUserVerifyCode(@RequestBody MiniAccountPackageGoodsCodeParam miniAccountPackageGoodsCodeParam) {
        IPage<ShopPackageGoodsCodeVo> page = miniAccountPackageGoodsCodeService.pageShopUserVerifyCode(miniAccountPackageGoodsCodeParam);
        return Result.ok(page);
    }

    /**
     * 分页获取核销单明细
     * @param param
     * @return
     */
    @PostMapping("/pagePackageGoodsCodeDetail")
    @ApiOperation(value = "分页获取核销单明细")
    public Result<IPage<PackageGoodsCodeDetailVo>> pagePackageGoodsCodeDetail(@RequestBody PackageGoodsCodeDetailParam param) {
        IPage<PackageGoodsCodeDetailVo> page = miniAccountPackageGoodsCodeService.pagePackageGoodsCodeDetail(param);
        return Result.ok(page);
    }

    /**
     * 通过验证码获取权益包商品记录
     *
     * @return
     */
    @PostMapping("/getPackageGoodsByCode")
    @ApiOperation(value = "通过验证码获取权益包商品记录")
    public Result<MiniAccountPackageGoodsVo> getPackageGoodsByCode(@RequestBody String verifyCode) {
        MiniAccountPackageGoodsVo miniAccountPackageGoods = miniAccountPackageGoodsCodeService.getPackageGoodsByCode(verifyCode);
        return Result.ok(miniAccountPackageGoods);
    }

    /**
     * 后台核销权益包商品
     * @param miniAccountPackageGoodsDto
     * @return
     */
    @PostMapping("/writeOffPackageGoods")
    @ApiOperation(value = "后台核销权益包商品")
    public Result<MiniAccountPackageGoodsCode>writeOffPackageGoods(@RequestBody MiniAccountPackageGoodsDto miniAccountPackageGoodsDto){
        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = miniAccountPackageGoodsCodeService.writeOffPackageGoods(miniAccountPackageGoodsDto);
        return Result.ok(miniAccountPackageGoodsCode);
    }

    /**
     * pc获取权益包商品验证码
     * @param miniAccountPackageGoodsDto
     * @return
     */
    @PostMapping("/pcGetCode")
    @ApiOperation(value = "pc获取权益包商品验证码")
    public Result<MiniAccountPackageGoodsCodeVo> pcGetCode(@RequestBody MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        MiniAccountPackageGoodsCodeVo codeVo = miniAccountPackageGoodsCodeService.pcGetCode(miniAccountPackageGoodsDto);
        return Result.ok(codeVo);
    }

    /**
     * 导出核销单明细
     * @param param
     * @return
     */
    @PostMapping("/exportWriteOffDetail")
    @ApiOperation(value = "导出核销单明细")
    public void exportPackageGoodsCodeDetail(@RequestBody PackageGoodsCodeDetailParam param) {
        miniAccountPackageGoodsCodeService.exportPackageGoodsCodeDetail(param);
    }
}
