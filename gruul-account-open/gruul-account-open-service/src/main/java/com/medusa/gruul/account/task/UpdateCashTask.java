package com.medusa.gruul.account.task;

import com.medusa.gruul.account.service.IMiniAccountCommissionCashService;
import com.medusa.gruul.goods.api.constant.GoodsSkuStockRedisKey;
import com.medusa.gruul.goods.api.entity.SkuStock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Author: plh
 * @Description: UpdatePayStatusTask.java
 * @Date: Created in 16:34 2023/9/4
 */
@Slf4j
@Component
public class UpdateCashTask {

    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;

    @Scheduled(fixedRate = 1000 * 60 * 30)
    public void UpdatePayStatusTask() {
//        log.info("-----------佣金提现更新支付状态定时任务开启-----------");
//        miniAccountCommissionCashService.updatePayStatus();
//        log.info("-----------佣金提现更新支付状态定时任务结束-----------");
    }
}
