package com.medusa.gruul.account.mq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.medusa.gruul.account.api.constant.AccountQueueNameConstant;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.message.UpdateCommissionMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.model.dto.MiniAccountCouponDto;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketDto;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.order.api.constant.OrderQueueNameConstant;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.api.model.RevertMiniAccountBalanceMessage;
import com.medusa.gruul.shops.api.constant.QueueNameConstant;
import com.medusa.gruul.shops.api.enums.QueueEnum;
import com.medusa.gruul.shops.api.model.PrizeCouponMessage;
import com.medusa.gruul.shops.api.model.SendCouponMessage;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 用户队列监听
 * @data: 2019/12/11
 */
@Slf4j
@Component
public class AccountListener {

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;

    @Autowired
    private IApiMiniAccountCollectService miniAccountCollectService;

    @Autowired
    private IMiniAccountIntegralService miniAccountIntegralService;

    @Autowired
    private IMiniAccountPassTicketService miniAccountPassTicketService;

    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;

    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;
    @Autowired
    private IMiniAccountPackageOrderService miniAccountPackageOrderService;
    @Autowired
    private IMiniAccountCouponService miniAccountCouponService;

    @Autowired
    private IRemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private IMiniAccountService miniAccountService;

    /**
     * 订单支付成功->修改用户最后交易时间
     *
     * @param orderVo    com.medusa.gruul.order.api.model.OrderVo
     * @param properties org.springframework.amqp.core.MessageProperties
     * @param channel    com.rabbitmq.client.Channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE)
    public void orderPayOk(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("orderPayOk receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        miniAccountExtendsService.modifyLastDealTime(orderVo.getUserId(), orderVo.getPayTime(),orderVo.getMallOrderType(),orderVo.getShopId());

        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }


    /**
     * 订单支付成功->扣减用户积分
     *
     * @param orderVo    com.medusa.gruul.order.api.model.OrderVo
     * @param properties org.springframework.amqp.core.MessageProperties
     * @param channel    com.rabbitmq.client.Channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_INTEGRAL_ORDER_PAY_OK_QUEUE_CHANGE)
    public void integralOrderPayOk(OrderVo orderVo, MessageProperties properties, Channel channel)throws IOException{
        log.info("integralOrderPayOk receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        //更新用户积分信息
        miniAccountIntegralService.deductionIntegral(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    /**
     * 订单完成->更新用户积分记录，添加用户积分明细
     * @param orderVo
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_EXCHANGE)
    public void orderPayOkExchange(OrderVo orderVo, MessageProperties properties, Channel channel)throws IOException {
        log.info("orderPayOk receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        //更新用户积分信息
        // miniAccountIntegralService.handleIntegral(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    /**
     * 通惠证订单签收成功->添加通惠证订单信息
     * @param orderVo
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_TICKET)
    public void orderPayOkTicket(OrderVo orderVo, MessageProperties properties, Channel channel)throws IOException {
        log.info("orderPayOk receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        //添加消息队列发送通惠证订单信息
        if(orderVo.getType().equals(OrderTypeEnum.TICKET)){
            miniAccountPassTicketService.addMiniAccountPassTicketByMq(orderVo);
        }
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    /**
     * 权益包订单支付成功->添加权益包订单信息
     * @param orderVo
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_PAY_OK_QUEUE_CHANGE_PACKAGE)
    public void orderPayOkPackageGoods(OrderVo orderVo, MessageProperties properties, Channel channel)throws IOException {
        log.info("orderPayOk receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        //添加消息队列发送权益包订单消息
        if(orderVo.getType().equals(OrderTypeEnum.MALL)&&
                orderVo.getMallOrderType().equals(ProductTypeEnum.PACKAGE_PRODUCT.getStatus())){
            miniAccountPackageOrderService.addMiniAccountPackageOrderByMq(orderVo);
        }
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }


    /**
     * 订单完成通知;指定用户增加消费次数和消费金额
     *
     * @param orderVo    com.medusa.gruul.order.api.model.OrderVo
     * @param properties org.springframework.amqp.core.MessageProperties
     * @param channel    com.rabbitmq.client.Channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_COMPLETE_OK_QUEUE_CHANGE)
    public void orderCompleted(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("completed receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        //执行订单完成队列
        miniAccountExtendsService.orderCompleted(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }
    /**
     * 提现审核成功，微信打款队列
     * @param cashMessage
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_CASH_OK)
    public void accountCommissionCash(CashMessage cashMessage, MessageProperties properties, Channel channel) throws IOException {
        log.info("completed receive message:" + cashMessage.toString());
        try{
            miniAccountCommissionCashService.CashAmountToUser(cashMessage);
            channel.basicAck(properties.getDeliveryTag(), true);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            log.info("微信打款执行失败: deliveryTag{}", properties.getDeliveryTag());
            channel.basicAck(properties.getDeliveryTag(), true);
        }
    }

    /**
     * 订单完成通知;上级，上上级添加佣金
     * @param orderVo
     * @param properties
     * @param channel
     * @throws IOException
     */
//    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_COMPLETE_OK_QUEUE_CHANGE_ADD_COMMISSION)
//    public void orderCompletedAddCommission(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
//        log.info("completed receive message:" + orderVo.toString());
//        if (ObjectUtil.isNull(orderVo)) {
//            //手动确认
//            channel.basicAck(properties.getDeliveryTag(), true);
//            return;
//        }
//        //指定用户增加消费次数和消费金额
//        //miniAccountCommissionService.handleRewardScheme(orderVo);
//        //miniAccountCommissionService.handleCommission(orderVo);
//        //手动确认
//        channel.basicAck(properties.getDeliveryTag(), true);
//    }

    /**
     * 更新用户收藏 sql表数据
     *
     * @param collectMessage
     * @param properties
     * @param channel
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_COLLECT)
    public void userCollect(CollectMessage collectMessage, MessageProperties properties, Channel channel) throws IOException {
        log.info(" receive message:" + collectMessage.toString());
        //更新或插入 用户收藏数据
        miniAccountCollectService.updateAccountCollect(collectMessage);
    }

    /**
     * 修改权益包订单状态队列
     * @param message
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.PACKAGE_ORDER_STATUS_CHANGE)
    public void updatePackageOrderStatus(UpdatePackageOrderStatusMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("completed receive message:" + message.toString());
        try{
            miniAccountPackageOrderService.updatePackageOrderStatus(message);
            channel.basicAck(properties.getDeliveryTag(), true);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            log.info("修改权益包订单状态失败: deliveryTag{}", properties.getDeliveryTag());
            channel.basicAck(properties.getDeliveryTag(), true);
        }
    }

    @RabbitListener(queues = AccountQueueNameConstant.SEND_COUPON)
    public void sendCoupon(SendCouponMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("sendCoupon receive message:" + message.toString());
        try{
            miniAccountCouponService.sendCoupon(message);
            channel.basicAck(properties.getDeliveryTag(), true);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            log.info("修改权益包订单状态失败: deliveryTag{}", properties.getDeliveryTag());
            channel.basicAck(properties.getDeliveryTag(), true);
        }
    }

    /**
     * 更新用户佣金信息
     * @param message
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.MINI_ACCOUNT_COMMISSION_CASH_CHANGE)
    public void updateCommissionCash(UpdateCommissionMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("更新用户佣金信息，receive message: {}", JSONObject.toJSONString(message));
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        try {
            TenantContextHolder.setTenantId(message.getTenantId());
            //更新用户佣金信息
            miniAccountCommissionCashService.updateCommissionCash(message);
            channel.basicAck(properties.getDeliveryTag(), true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            channel.basicReject(properties.getDeliveryTag(), false);
        }

    }

    /**
     * 归还用户余额
     * @param message
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_REVERT_BALANCE)
    public void revertAccountBalance(AccountReturnBalanceMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("归还用户余额，receive message: {}", JSONObject.toJSONString(message));
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        try {
            TenantContextHolder.setTenantId(message.getTenantId());
            //归还用户余额
            remoteMiniAccountService.revertAccountBalance(message);
            channel.basicAck(properties.getDeliveryTag(), true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            channel.basicReject(properties.getDeliveryTag(), false);
        }

    }

    /**
     * 抽奖 - 发放奖项-优惠券
     * @param message
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = QueueNameConstant.SHOP_PRIZE_DISTRIBUTED_COUPON)
    public void couponDistribution(PrizeCouponMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("抽奖 - 发放奖项-优惠券，receive message: {}", JSONObject.toJSONString(message));
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        try {
            TenantContextHolder.setTenantId(message.getTenantId());
            ShopContextHolder.setShopId(message.getUserId());
            MiniAccountCouponDto dto = new MiniAccountCouponDto();
            BeanUtils.copyProperties(message,dto);
            AccountInfoDto infoDto = miniAccountService.accountInfo(message.getUserId(), Arrays.asList(1));
            if (infoDto!=null){
                dto.setUserId(infoDto.getMiniAccountunt().getUserId());
            }
            dto.setNotCheckFlag(true);
            miniAccountCouponService.add(dto);

            channel.basicAck(properties.getDeliveryTag(), true);
        } catch (Exception e) {
            log.error("处理优惠券奖品发放消息失败: {}", e.getMessage(), e);
            channel.basicAck(properties.getDeliveryTag(), true);
        }

    }

    @RabbitListener(queues =  AccountQueueNameConstant.ACCOUNT_UPGRADE_MEMBER_LEVEL)
    public void upgradeMemberLevel(UpgradeMemberLevelMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("upgradeMemberLevel，receive message: {}", JSONObject.toJSONString(message));
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        try {
            TenantContextHolder.setTenantId(message.getTenantId());
            if(StringUtils.isNotEmpty(message.getShopId())){
                ShopContextHolder.setShopId(message.getShopId());
            }
            remoteMiniAccountService.handCompletedOrder(message);
            channel.basicAck(properties.getDeliveryTag(), true);
        } catch (Exception e) {
            log.error("处理升级会员等级消息失败: {}", e.getMessage(), e);
            channel.basicReject(properties.getDeliveryTag(), false);
        }

    }

    @RabbitListener(queues = AccountQueueNameConstant.ACCOUNT_ORDER_DELETE)
    public void orderDelete(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("orderDelete receive message:" + orderVo.toString());
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        miniAccountService.deleteOrder(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }
}
