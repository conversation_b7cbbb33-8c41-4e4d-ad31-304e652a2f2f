package com.medusa.gruul.account.web.controller.api;

import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.model.vo.ApiMemberLevelVo;
import com.medusa.gruul.account.model.dto.ApiApplyRegionMemberDto;
import com.medusa.gruul.account.model.param.MiniAccountApplyRegionMemberParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:12 2025/3/28
 */
@RestController
@RequestMapping("/api/member-level")
@Api(tags = "小程序会员等级相关接口")
public class ApiMemberLevelController {

    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;

    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Autowired
    private IMiniAccountApplyRegionMemberService miniAccountApplyRegionMemberService;
    @Autowired
    private IMiniAccountService miniAccountService;


    @GetMapping("/getMemberLevel")
    @ApiOperation(value = "获取小程序会员等级")
    public Result getMemberLevel(@RequestParam(value = "memberTypeId",required = true) Long memberTypeId) {
        MemberLevel memberLevel = memberLevelService.getMemberLevel(memberTypeId);
        return Result.ok(memberLevel);
    }

    @GetMapping("/getApiMemberLevel")
    @ApiOperation(value = "获取小程序我的等级数据")
    public Result getApiMemberLevel(@RequestParam(value = "memberTypeId",required = false) Long memberTypeId) {
        ApiMemberLevelVo apiMemberLevel = memberLevelService.getApiMemberLevel(memberTypeId);
        return Result.ok(apiMemberLevel);
    }

    /**
     * 根据会员类型id获取跳转首页标识
     * @param memberTypeId
     * @return
     */
    @GetMapping("/getHomeFlag")
    @ApiOperation(value = "根据会员类型id获取跳转首页标识")
    public Result getHomeFlag(@RequestParam(value = "memberTypeId",required = true) Long memberTypeId){
        Integer homeFlag = memberLevelRuleMessageService.getHomeFlagByMemberTypeId(memberTypeId);
        return Result.ok(homeFlag);
    }

    /**
     * 根据会员类型id获取购买升级商品列表
     * @param memberTypeId
     * @return
     */
    @GetMapping("/getUpgradeMemberData")
    @ApiOperation(value = "根据会员类型id获取购买升级商品列表")
    public Result getUpgradeMemberData(@RequestParam(value = "memberTypeId",required = true) Long memberTypeId){
        List<UpgradeMemberDataVo>list = memberLevelRuleMessageService.getUpgradeMemberData(memberTypeId);
        return Result.ok(list);
    }


    /**
     * 是否区域会员
     * @return
     */
    @GetMapping("/getRegionFlag")
    @ApiOperation(value = "是否区域会员")
    public Result getRegionMemberFlag(){
        Boolean result = memberLevelRelationService.getRegionMemberFlag();
        return Result.ok(result);
    }

    /**
     * 获取区域会员申请信息
     * @return
     */
    @GetMapping("/getApplyRegionMember")
    @ApiOperation(value = "获取区域会员申请信息")
    public Result getApplyRegionMember(){
        ApiApplyRegionMemberVo apiApplyRegionMemberVo = memberLevelRelationService.getApplyRegionMember();
        return Result.ok(apiApplyRegionMemberVo);
    }

    /**
     * 区域会员申请
     * @param dto
     * @return
     */
    @PostMapping("/applyRegionMember")
    @ApiOperation(value = "区域会员申请")
    public Result applyRegionMember(@RequestBody ApiApplyRegionMemberDto dto){
        memberLevelRelationService.applyRegionMember(dto);
        return Result.ok();
    }

    /**
     * 获取当前用户区域会员等级信息
     * @return
     */
    @PostMapping("/getRegionMemberLevel")
    @ApiOperation(value = "获取当前用户区域会员等级信息")
    public Result getRegionMemberLevel(){
        RegionMemberLevelVo regionMemberLevel = memberLevelRelationService.getRegionMemberLevel();
        return Result.ok(regionMemberLevel);
    }

    /**
     * 分页查询区域会员审核列表
     * @param param
     * @return
     */
    @PostMapping("/miniAccountApplyRegionMember/page")
    @ApiOperation(value = "分页查询区域会员审核列表")
    public Result setDefaultSale(@RequestBody MiniAccountApplyRegionMemberParam param) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        param.setUserId(miniAccount.getUserId());
        PageUtils<MiniAccountApplyRegionMemberVo> page = miniAccountApplyRegionMemberService.page(param);
        return Result.ok(page);
    }




}
