package com.medusa.gruul.account.web.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.api.entity.MiniAccountGolden;
import com.medusa.gruul.account.model.param.MiniAccountCommissionParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenParam;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenVo;
import com.medusa.gruul.account.model.vo.UserCommissionVo;
import com.medusa.gruul.account.model.vo.UserGoldenVo;
import com.medusa.gruul.account.service.IMiniAccountCommissionService;
import com.medusa.gruul.account.service.IMiniAccountGoldenService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/mini-account-golden")
@Api(tags = "小程序用户-金豆相关接口")
public class ApiMiniAccountGoldenController {

    @Autowired
    private IMiniAccountGoldenService miniAccountGoldenService;


    /**
     * 获取当前用户金豆信息
     * @return
     */
    @GetMapping("/getMiniAccountGoldenVo")
    @ApiOperation(value = "获取当前用户金豆信息")
    public Result<UserGoldenVo> getMiniAccountGoldenVo() {
        UserGoldenVo userGoldenVo = miniAccountGoldenService.getMiniAccountGoldenVo();
        return Result.ok(userGoldenVo);
    }

    /**
     * 分页查询用户个人的金豆明细记录
     * @param miniAccountGoldenParam
     * @return
     */
    @PostMapping("/pageMyGolden")
    @ApiOperation(value = "分页查询用户个人的金豆明细记录")
    public Result<PageUtils<MiniAccountGoldenVo>> pageUserCommission(@RequestBody MiniAccountGoldenParam miniAccountGoldenParam) {
        IPage<MiniAccountGoldenVo> page = miniAccountGoldenService.pageMyGolden(miniAccountGoldenParam) ;
        return Result.ok(new PageUtils<>(page));
    }
}
