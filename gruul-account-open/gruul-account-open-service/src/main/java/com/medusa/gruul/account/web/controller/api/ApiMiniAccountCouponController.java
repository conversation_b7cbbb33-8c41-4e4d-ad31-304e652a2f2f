package com.medusa.gruul.account.web.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountCoupon;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponVo;
import com.medusa.gruul.account.api.model.MiniAccountCouponByOrderDto;
import com.medusa.gruul.account.model.dto.MiniAccountCouponDto;
import com.medusa.gruul.account.model.param.MiniAccountCouponParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponByOrderVo;
import com.medusa.gruul.account.service.IMiniAccountCouponService;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.model.dto.api.MiniOrderCouponDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:44 2024/8/27
 */
@RestController
@RequestMapping("/api/mini-account-coupon")
@Api(tags = "小程序用户-优惠券相关接口")
public class ApiMiniAccountCouponController {

    @Autowired
    private IMiniAccountCouponService miniAccountCouponService;


    /**
     * 小程序-领取优惠券
     * @param miniAccountCouponDto
     * @return
     */
    @PostMapping("/addMiniAccountCoupon")
    @ApiOperation(value = "小程序-领取优惠券")
    public Result<MiniAccountCoupon> addMiniAccountCoupon(@RequestBody @Validated MiniAccountCouponDto miniAccountCouponDto){
        MiniAccountCoupon miniAccountCoupon = miniAccountCouponService.add(miniAccountCouponDto);
        return Result.ok(miniAccountCoupon);
    }

    /**
     * 小程序-获取用户领取优惠券数
     * @return
     */
    @GetMapping("/getMiniAccountCouponNum")
    @ApiOperation(value = "小程序-获取用户领取优惠券数")
    public Result getMiniAccountCouponNum(){
        Integer count = miniAccountCouponService.getMiniAccountCouponNum();
        return Result.ok(count);
    }

    /**
     * 分页获取用户优惠券信息
     *
     * @param miniAccountCouponParam 分页数据
     * @return
     */
    @PostMapping("/pageUserCoupon")
    @ApiOperation(value = "分页获取用户优惠券信息")
    public Result<IPage<MiniAccountCouponVo>> pageUserCoupon(@RequestBody MiniAccountCouponParam miniAccountCouponParam) {
        IPage<MiniAccountCouponVo> page = miniAccountCouponService.pageMyCoupon(miniAccountCouponParam) ;
        return Result.ok(page);
    }

    /**
     * 获取用户可用的优惠券
     * @return
     */
    @PostMapping("/getCouponByUser")
    @ApiOperation(value = "分页获取用户优惠券信息")
    public Result<List<MiniAccountCouponByOrderVo>> getCouponByUser(@RequestBody MiniAccountCouponByOrderDto miniOrderCouponDto){
       miniOrderCouponDto.setAllFlag(true);
       List<MiniAccountCouponByOrderVo> list = miniAccountCouponService.getCouponByUser(miniOrderCouponDto);
       return Result.ok(list);
    }

}
