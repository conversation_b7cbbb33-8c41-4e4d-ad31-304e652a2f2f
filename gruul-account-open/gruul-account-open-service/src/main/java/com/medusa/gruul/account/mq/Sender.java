package com.medusa.gruul.account.mq;

import cn.hutool.core.util.IdUtil;
import com.medusa.gruul.account.api.constant.AccountQueueNameConstant;
import com.medusa.gruul.account.api.enums.AccountQueueEnum;
import com.medusa.gruul.account.api.model.message.AccountCommissionCashMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.constant.OrderQueueEnum;
import com.medusa.gruul.order.api.model.CreateOrderOutStockMessage;
import com.medusa.gruul.order.api.model.OrderDeliveryProxyMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.payment.api.enums.PaymentQueueEnum;
import com.medusa.gruul.payment.api.model.message.WxTransferV3Message;
import com.medusa.gruul.platform.api.constant.ExchangeConstant;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.enums.QueueEnum;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.shops.api.model.PrizeMemberMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * @data 2020/2/24 13:35
 */
@Slf4j
@Component
public class Sender {
    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    public void sendShoppingCartMessage(CollectMessage collectMessage) {
        log.info("send message:" + collectMessage.toString());
        amqpTemplate.convertAndSend(AccountQueueNameConstant.ACCOUNT_COLLECT, collectMessage);
    }

    public void sendCashMessage(CashMessage cashMessage){
        log.info("send message:" + cashMessage.toString());
        convertAndSend(AccountQueueEnum.QUEUE_ACCOUNT_CASH, cashMessage);
    }
    public void sendTransferV3Message(WxTransferV3Message wxTransferV3Message){
        log.info("send message:" + wxTransferV3Message.toString());
        convertAndSend(PaymentQueueEnum.QUEUE_PAYMENT_TRANSFER_V3, wxTransferV3Message);
    }
    public void sendOrderStockOutMessage(CreateOrderOutStockMessage orderOutStockMessage){
        log.info("send message:" + orderOutStockMessage.toString());
        convertAndSend(OrderQueueEnum.QUEUE_ORDER_STOCK_OUT,orderOutStockMessage);
    }

    private void convertAndSend(OrderQueueEnum queue,Object message){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }

    private void convertAndSend(AccountQueueEnum queue, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }

    private void convertAndSend(PaymentQueueEnum queue, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }

    public void sendWxMessage(WxSendMessageDto dto) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        log.info("sendWxMpMessage message:" + dto.toString());
        rabbitTemplate.convertAndSend(QueueEnum.PLATFORM_WX_SEND_MESSAGE.getExchange(),
                QueueEnum.PLATFORM_WX_SEND_MESSAGE.getRouteKey(), dto, correlationData);
    }

    /**
     * 发送佣金提现消息
     * @param message
     */
    public void sendCommissionCashMessage(AccountCommissionCashMessage message) {
        log.info("sendCommissionCashMessage message:" + message.toString());
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(ExchangeConstant.PLATFORM_EXCHANGE,
                QueueNameConstant.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE, message, correlationData);
    }

    public void sendUpgradeMemberLevelMessage(UpgradeMemberLevelMessage message) {
        log.info("sendCommissionCashMessage message:" + message.toString());
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(AccountQueueEnum.QUEUE_ACCOUNT_UPGRADE_MEMBER_LEVEL.getExchange(),
                AccountQueueEnum.QUEUE_ACCOUNT_UPGRADE_MEMBER_LEVEL.getRouteKey(), message, correlationData);
    }
    public void sendPrizeMemberUpMessage(PrizeMemberMessage message) {
        log.info("sendPrizeMemberUpMessage message:" + message.toString());
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(com.medusa.gruul.shops.api.enums.QueueEnum.QUEUE_SHOP_PRIZE_MEMBER_PRIZE.getExchange(),
                com.medusa.gruul.shops.api.enums.QueueEnum.QUEUE_SHOP_PRIZE_MEMBER_PRIZE.getRouteKey(), message, correlationData);
    }


}
