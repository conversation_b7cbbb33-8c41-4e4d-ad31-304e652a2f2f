package com.medusa.gruul.account.web.controller;



import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberLevelRights;
import com.medusa.gruul.account.api.model.MemberLevelDto;
import com.medusa.gruul.account.model.dto.AuditDataDto;
import com.medusa.gruul.account.model.param.MemberLevelParam;
import com.medusa.gruul.account.model.param.MiniAccountApplyRegionMemberParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMemberLevelRightsService;
import com.medusa.gruul.account.service.IMemberLevelService;
import com.medusa.gruul.account.service.IMiniAccountApplyRegionMemberService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 *会员等级信息
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@RestController
@RequestMapping("/member-level")
@Api(tags = "会员等级相关接口")
public class MemberLevelController {

    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLevelRightsService memberLevelRightsService;
    @Autowired
    private IMiniAccountApplyRegionMemberService miniAccountApplyRegionMemberService;

    @PostMapping("/disableMemberLevel")
    @ApiOperation(value = "停用会员等级 0-未停用 1-已停用")
    //@EscapeLogin
    public Result disableMemberLevel(@RequestBody MemberLevel memberLevel) {
        Map<String, Object> map= memberLevelService.disable(memberLevel);
        if(!(boolean)map.get("success")){
            return Result.failed(map.get("str"));
        }
        return Result.ok(map.get("str"));
    }

    @PostMapping("/getRightsList")
    @ApiOperation(value = "获取会员权益信息")
    //@EscapeLogin
    public Result getRightsList() {
        List<MemberLevelRights> rightsVos = memberLevelRightsService.list();
        return Result.ok(rightsVos);
    }

    /**
     *@EscapeLogin 免登录注解
     */
    @PostMapping("/addOrUpdateMemberLevel")
    @ApiOperation(value = "添加或者修改会员等级信息")
    //@EscapeLogin
    public Result addOrUpdateMemberLevel(@RequestBody List<MemberLevelVo> memberLevel) {
        memberLevelService.addOrUpdate(memberLevel);
        return Result.ok("", "保存成功");
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除会员等级")
    //@EscapeLogin
    public Result delete(@RequestBody MemberLevel memberLevel) {
        memberLevelService.delete(memberLevel);
        return Result.ok("", "删除成功");
    }

    @PostMapping("/selectMemberLevelList")
    @ApiOperation(value = "查询会员等级信息")
    //@EscapeLogin
    public Result selectMemberLevelList(@RequestBody MemberLevelParam param) {
        List<MemberLevelVo> memberLevelList = memberLevelService.selectList(param);
        return Result.ok(memberLevelList);
    }

    @PostMapping("/getMemberLevelList")
    @ApiOperation(value = "获取启用的会员等级信息")
    public Result getMemberLevelList() {
        List<MemberLevelVo> memberLevelList = memberLevelService.selectAllList();
        return Result.ok(memberLevelList);
    }

    /**
     * 查询全部会员等级信息-根据会员类型分组
     * @return
     */
    @PostMapping("/getMemberLevelListGroupByMemberTypeId")
    @ApiOperation(value = "查询全部会员等级信息-根据会员类型分组")
    public Result getMemberLevelListGroupByMemberTypeId() {
        List<MemberLevelGroupByMemberTypeVo> list = memberLevelService.selectAllListGroupByMemberTypeId();
        return Result.ok(list);
    }

    /**
     * 根据会员类型id获取启用会员等级
     * @param param
     * @return
     */
    @PostMapping("/getMemberLevelByMemberTypeId")
    @ApiOperation(value = "根据会员类型id获取启用会员等级")
    //@EscapeLogin
    public Result getMemberLevelByMemberTypeId(@RequestBody MemberLevelParam param) {
        List<MemberLevelVo> memberLevelList = memberLevelService.getMemberLevelByMemberTypeId(param.getMemberTypeId());
        return Result.ok(memberLevelList);
    }


    @PostMapping("/getMemberLevelPriceList")
    @ApiOperation(value = "获取会员等级价格信息")
    public Result getMemberLevelPriceList(@RequestBody MemberLevelDto memberLevelDto) {
        IPage<MemberLevePriceVo> page = new Page<>(memberLevelDto.getCurrent(), memberLevelDto.getSize());
        List<MemberLevePriceVo> memberLevePriceVos = memberLevelService.selectPrice(memberLevelDto.getProductId());
        PageUtils<MemberLevePriceVo> pageUtils = new PageUtils(page.setRecords(memberLevePriceVos));
        return Result.ok(pageUtils);
    }

    /**
     * 设置默认会员等级
     */
    @PostMapping("/defaultLevel/{id}")
    @ApiOperation(value = "设置默认会员等级")
    public Result setDefaultSale(@ApiParam(value = "会员等级id", required = true) @PathVariable(name = "id") Long id) {
        this.memberLevelService.defaultLevel(id);
        return Result.ok(null, "操作成功");
    }

    /**
     * 分页查询区域会员审核列表
      * @param param
     * @return
     */
    @PostMapping("/miniAccountApplyRegionMember/page")
    @ApiOperation(value = "分页查询区域会员审核列表")
    public Result setDefaultSale(@RequestBody MiniAccountApplyRegionMemberParam param) {
        PageUtils<MiniAccountApplyRegionMemberVo> page = miniAccountApplyRegionMemberService.page(param);
        return Result.ok(page);
    }

    /**
     * 区域会员审核
     * @param dto
     * @return
     */
    @PostMapping("/miniAccountApplyRegionMember/audit")
    @ApiOperation(value = "区域会员审核")
    public Result miniAccountApplyRegionMemberAudit(@RequestBody AuditDataDto dto) {
        miniAccountApplyRegionMemberService.miniAccountApplyRegionMemberAudit(dto);
        return Result.ok();
    }



}
