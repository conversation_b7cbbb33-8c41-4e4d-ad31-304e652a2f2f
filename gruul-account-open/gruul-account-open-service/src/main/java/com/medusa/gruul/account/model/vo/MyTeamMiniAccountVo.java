package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 我的团队成员vo
 * @Date: Created in 11:06 2023/8/30
 */
@Data
public class MyTeamMiniAccountVo {

    /**
     * 团员名字
     */
    @ApiModelProperty(value = "团员名字")
    private String name;

    /**
     * 加入时间
     */
    @ApiModelProperty(value = "加入时间")
    private LocalDateTime createTime;

    /**
     * 推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendName;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private String memberLevel;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    private  String avatarUrl;
}
