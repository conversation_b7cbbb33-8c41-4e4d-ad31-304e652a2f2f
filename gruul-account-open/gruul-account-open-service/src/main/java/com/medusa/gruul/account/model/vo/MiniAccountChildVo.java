package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:45 2024/5/13
 */
@Data
public class MiniAccountChildVo {

    @ApiModelProperty(value = "用户id")
    private String  userId;

    @ApiModelProperty(value = "用户昵称")
    private String nikeName;

    @ApiModelProperty(value = "用户手机")
    private String phone;

    @ApiModelProperty(value = "客户等级")
    private String memberLevel;
}
