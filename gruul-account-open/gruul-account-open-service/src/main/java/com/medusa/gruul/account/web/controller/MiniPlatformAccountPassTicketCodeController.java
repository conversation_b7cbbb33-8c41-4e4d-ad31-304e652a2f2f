package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.service.IMiniAccountPassTicketCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序-后台用户(商家)-通惠证验证码控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mini-platform/passTicketCode")
@Api(tags = "小程序后台用户-通惠证验证码相关接口")
public class MiniPlatformAccountPassTicketCodeController {

    @Autowired
    private IMiniAccountPassTicketService miniAccountPassTicketService;

    @Autowired
    private IMiniAccountPassTicketCodeService miniAccountPassTicketCodeService;

    /**
     * 通过验证码获取通惠证记录
     *
     * @return
     */
    @PostMapping("/getPassTicketByCode")
    @ApiOperation(value = "通过验证码获取通惠证记录")
    public Result<ShopPassTicket> getPassTicketByCode(@RequestBody String verifyCode) {
        ShopPassTicket shopPassTicket = miniAccountPassTicketCodeService.getPassTicketByCode(verifyCode);
        return Result.ok(shopPassTicket);
    }

    /**
     * 核销验证码
     *
     * @return
     */
    @PostMapping("/verifyPassTicketCode")
    @ApiOperation(value = "核销验证码")
    public Result<MiniAccountPassTicketCode> verifyPassTicketCode(@RequestBody String verifyCode) {
        MiniAccountPassTicketCode code = miniAccountPassTicketCodeService.verifyCode(verifyCode);
        return Result.ok(code);
    }

    /**
     * 分页获取商家用户查询自己核销的记录
     *
     * @param miniAccountPassTicketCodeParam 分页数据
     * @return
     */
    @PostMapping("/pageUserPassTicket")
    @ApiOperation(value = "分页获取商家用户查询自己核销的记录")
    public Result<IPage<ShopPassTicketCodeVo>> pageShopUserVerifyCode(@RequestBody MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam) {
        IPage<ShopPassTicketCodeVo> page = miniAccountPassTicketCodeService.pageShopUserVerifyCode(miniAccountPassTicketCodeParam) ;
        return Result.ok(page);
    }


}
