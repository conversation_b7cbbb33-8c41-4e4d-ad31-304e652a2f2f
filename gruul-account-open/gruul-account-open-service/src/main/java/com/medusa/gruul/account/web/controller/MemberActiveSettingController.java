package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.model.dto.AddOrUpdateMemberTypeDto;
import com.medusa.gruul.account.model.dto.MemberActiveSettingDto;
import com.medusa.gruul.account.model.vo.MemberActiveSettingVo;
import com.medusa.gruul.account.service.IMemberActiveSettingService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:27 2025/6/3
 */
@RestController
@RequestMapping("/member-active-setting")
@Api(tags = "会员有效期设置相关接口")
public class MemberActiveSettingController {

    @Autowired
    private IMemberActiveSettingService memberActiveSettingService;

    /**
     * 获取会员有效期设置
     * @return
     */
    @PostMapping("/getMemberActiveSetting")
    @ApiOperation(value = "获取会员有效期设置")
    public Result getMemberActiveSetting() {
        MemberActiveSettingVo memberActiveSetting = memberActiveSettingService.getMemberActiveSetting();
        return Result.ok(memberActiveSetting);
    }

    /**
     * 添加或更新会员有效期设置
     * @param dto
     * @return
     */
    @PostMapping("/addOrUpdate")
    @ApiOperation(value = "添加或更新会员有效期设置")
    public Result addOrUpdate(@RequestBody MemberActiveSettingDto dto) {
        memberActiveSettingService.addOrUpdate(dto);
        return Result.ok();
    }
}
