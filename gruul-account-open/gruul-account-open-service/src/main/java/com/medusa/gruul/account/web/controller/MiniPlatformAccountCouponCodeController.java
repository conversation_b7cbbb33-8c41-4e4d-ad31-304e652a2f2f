package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountCouponCode;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponCodeParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopCouponCodeVo;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.service.IMiniAccountCouponCodeService;
import com.medusa.gruul.account.service.IMiniAccountCouponService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.model.AccountCouponVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mini-platform/couponCode")
@Api(tags = "小程序后台用户-优惠券验证码相关接口")
public class MiniPlatformAccountCouponCodeController {


    @Autowired
    private IMiniAccountCouponService miniAccountCouponService;

    @Autowired
    private IMiniAccountCouponCodeService miniAccountCouponCodeService;

    /**
     * 通过验证码获取优惠券记录
     *
     * @return
     */
    @PostMapping("/getCouponByCode")
    @ApiOperation(value = "通过验证码获取优惠券记录")
    public Result<AccountCouponVo> getCouponByCode(@RequestBody String verifyCode) {
        AccountCouponVo accountCouponVo = miniAccountCouponCodeService.getCouponByCode(verifyCode);
        return Result.ok(accountCouponVo);
    }

    /**
     * 核销验证码
     *
     * @return
     */
    @PostMapping("/verifyCouponCode")
    @ApiOperation(value = "核销验证码")
    public Result<MiniAccountCouponCode> verifyCouponCode(@RequestBody String verifyCode) {
        MiniAccountCouponCode code = miniAccountCouponCodeService.verifyCode(verifyCode);
        return Result.ok(code);
    }

    /**
     * 分页获取商家用户查询自己核销的记录
     *
     * @param miniAccountCouponCodeParam 分页数据
     * @return
     */
    @PostMapping("/pageUserCoupon")
    @ApiOperation(value = "分页获取商家用户查询自己核销的记录")
    public Result<IPage<ShopCouponCodeVo>> pageShopUserVerifyCode(@RequestBody MiniAccountCouponCodeParam miniAccountCouponCodeParam) {
        IPage<ShopCouponCodeVo> page = miniAccountCouponCodeService.pageShopUserVerifyCode(miniAccountCouponCodeParam);
        return Result.ok(page);
    }


}
