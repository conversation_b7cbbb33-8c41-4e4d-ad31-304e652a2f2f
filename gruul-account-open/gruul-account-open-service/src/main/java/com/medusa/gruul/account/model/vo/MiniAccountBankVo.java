package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:35 2025/5/27
 */
@Data
@ApiModel(value = "会员银行卡查询列表vo")
public class MiniAccountBankVo {

    @ApiModelProperty(value = "银行卡id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String bankName;

    /**
     * 卡号
     */
    @ApiModelProperty(value = "卡号")
    private String bankNo;

    /**
     * 开户人姓名
     */
    @ApiModelProperty(value = "开户人姓名")
    private String bankUserName;

    /**
     * 对公公司名称
     */
    @ApiModelProperty(value = "对公公司名称")
    private String companyName;
}
