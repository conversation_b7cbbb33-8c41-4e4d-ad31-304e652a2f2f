package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.model.param.MiniAccountCommissionManageParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenManageParam;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionManageVo;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenManageVo;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenVo;
import com.medusa.gruul.account.service.IMiniAccountGoldenService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:01 2025/7/21
 */
@RestController
@RequestMapping("/mini-account-golden")
@Api(tags = "管理后台-金豆相关接口")
public class MiniAccountGoldenController {

    @Autowired
    private IMiniAccountGoldenService miniAccountGoldenService;

    /**
     * 分页查询金豆明细
     * @param param
     * @return
     */
    @PostMapping("/searchMiniAccountGoldenDet")
    @ApiOperation(value = "分页查询金豆明细")
    public Result<PageUtils<MiniAccountGoldenManageVo>> searchMiniAccountGoldenDet(@RequestBody MiniAccountGoldenManageParam param){
        PageUtils<MiniAccountGoldenManageVo> pageUtils = miniAccountGoldenService.searchMiniAccountGoldenDet(param);
        return Result.ok(pageUtils);
    }

    /**
     * 导出金豆明细
     * @param param
     */
    @PostMapping("/exportMiniAccountGoldenManage")
    @ApiOperation(value = "导出金豆明细")
    public void exportMiniAccountGoldenManage(@RequestBody MiniAccountGoldenManageParam param) {
        miniAccountGoldenService.exportMiniAccountGoldenManage(param);
    }
}
