package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionDto;
import com.medusa.gruul.account.model.param.MiniAccountCommissionManageParam;
import com.medusa.gruul.account.model.param.MiniAccountRoyaltyDetParam;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionManageVo;
import com.medusa.gruul.account.model.vo.MiniAccountRoyaltyDetVo;
import com.medusa.gruul.account.service.IMiniAccountCommissionService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:06 2025/3/14
 */
@RestController
@RequestMapping("/mini-account-commission")
@Api(tags = "管理后台-佣金相关接口")
public class MiniAccountCommissionController {

    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;

    /**
     * 分页查询提成明细
     * @param param
     * @return
     */
    @PostMapping("/searchMiniAccountRoyaltyDet")
    @ApiOperation(value = "分页查询提成明细")
    public Result<PageUtils<MiniAccountRoyaltyDetVo>> searchMiniAccountRoyaltyDet(@RequestBody MiniAccountRoyaltyDetParam param){
        PageUtils<MiniAccountRoyaltyDetVo> pageUtils = miniAccountCommissionService.searchMiniAccountRoyaltyDet(param);
        return Result.ok(pageUtils);
    }
    /**
     * 手动设置佣金
     * @param miniAccountCommissionDto
     * @return
     */
    @PostMapping("/manualAddCommission")
    @ApiOperation(value = "手动设置佣金")
    public Result<MiniAccountCommission> manualAddCommission(@RequestBody MiniAccountCommissionDto miniAccountCommissionDto){
        return Result.ok(miniAccountCommissionService.manualAddCommission(miniAccountCommissionDto));
    }
    /**
     * 导出提成明细
     * @param param
     */
    @PostMapping("/exportMiniAccountRoyaltyDet")
    @ApiOperation(value = "导出提成明细")
    public void exportMiniAccountRoyaltyDet(@RequestBody MiniAccountRoyaltyDetParam param) {
        miniAccountCommissionService.exportMiniAccountRoyaltyDet(param);
    }

    /**
     * 分页查询佣金明细
     * @param param
     * @return
     */
    @PostMapping("/searchMiniAccountCommissionDet")
    @ApiOperation(value = "分页查询佣金明细")
    public Result<PageUtils<MiniAccountCommissionManageVo>> searchMiniAccountCommissionDet(@RequestBody MiniAccountCommissionManageParam param){
        PageUtils<MiniAccountCommissionManageVo> pageUtils = miniAccountCommissionService.searchMiniAccountCommissionDet(param);
        return Result.ok(pageUtils);
    }

    /**
     * 导出佣金明细
     * @param param
     */
    @PostMapping("/exportMiniAccountCommissionManage")
    @ApiOperation(value = "导出佣金明细")
    public void exportMiniAccountCommissionManage(@RequestBody MiniAccountCommissionManageParam param) {
        miniAccountCommissionService.exportMiniAccountCommissionManage(param);
    }
}
