package com.medusa.gruul.account.web.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountFootMark;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.model.dto.*;
import com.medusa.gruul.account.model.param.MyTeamMiniAccountParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IApiMiniAccountFootMarkService;
import com.medusa.gruul.account.service.IMiniAccountRestrictService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * APP用户
 */
@RestController
@RequestMapping("/app/app-account")
@Api(tags = "APP用户相关接口")
public class AppAccountController {

    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMiniAccountRestrictService miniAccountRestrictService;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private IApiMiniAccountFootMarkService miniAccountFootMarkService;


    @PostMapping("/login")
    @EscapeLogin
    @ApiOperation(value = "登录接口", tags = {"用户登录"})
    public Result<LoginBaseInfoVo> appLogin(@RequestBody @Validated AppLoginDto tenementLoginDto) {
        if(tenementLoginDto.getLoginType()==null){
            tenementLoginDto.setLoginType(0);
        }
        LoginBaseInfoVo accountInfoVo = miniAccountService.appLogin(tenementLoginDto, true);
        return Result.ok(accountInfoVo);
    }

    @PostMapping("/register")
    @EscapeLogin
    @ApiOperation(value = "注册接口", tags = {"用户注册"})
    public Result appRegister(@RequestBody @Validated AppRegisterDto registerDto) {
        miniAccountService.appRegister(registerDto);
        return Result.ok("注册成功");
    }

    @PostMapping("/uploadAvatar")
    @ApiOperation(value = "上传用户头像")
    public Result uploadAvatar(@RequestBody @Validated UpdateUserBaseInfoDto updateUserBaseInfoDto) {
        miniAccountService.uploadAvatar(updateUserBaseInfoDto);
        return Result.ok();
    }

}
