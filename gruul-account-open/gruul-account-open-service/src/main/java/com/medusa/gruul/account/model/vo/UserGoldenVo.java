package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "用户金豆信息")
public class UserGoldenVo {

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "推荐人")
    private String recommendName;

    @ApiModelProperty(value = "头像url")
    private String avatarUrl;

    @ApiModelProperty(value = "总金豆")
    private BigDecimal golden;

    @ApiModelProperty(value = "已用金豆")
    private BigDecimal usedGolden;

    @ApiModelProperty(value = "当前金豆")
    private BigDecimal currentGolden;

}
