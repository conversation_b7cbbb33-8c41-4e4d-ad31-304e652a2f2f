package com.medusa.gruul.account.web.controller.api;

import cn.hutool.db.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoodsCode;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopPackageGoodsCodeVo;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.model.dto.MiniAccountPackageGoodsDto;
import com.medusa.gruul.account.model.param.ApiMiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.param.ApiPackageGoodsCodeParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.account.service.IMiniAccountPackageOrderService;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:08 2024/9/6
 */
@RestController
@RequestMapping("/api/packageGoods")
public class ApiMiniAccountPackageGoodsController {


    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;
    @Autowired
    private IMiniAccountPackageGoodsCodeService miniAccountPackageGoodsCodeService;
    @Autowired
    private IMiniAccountPackageOrderService miniAccountPackageOrderService;


    /**
     * 分页获取小程序用户权益包管理信息
     * @param param
     * @return
     */
    @PostMapping("/getApiMiniAccountPackageOrder")
    @ApiOperation(value = "分页获取小程序用户权益包管理信息")
    public Result<PageUtils<ApiMiniAccountPackageOrderVo>>getApiMiniAccountPackageOrder(@RequestBody ApiMiniAccountPackageOrderParam param){
        //更新用户权益包订单状态
        miniAccountPackageOrderService.updateMiniAccountPackageOrderStatus();
        //获取用户权益包管理信息
        IPage<ApiMiniAccountPackageOrderVo> pageList = miniAccountPackageOrderService.getApiMiniAccountPackageOrder(param);
        return Result.ok(new PageUtils<>(pageList));
    }

    /**
     * 分页根据小程序用户权益包订单获取可核销权益包商品
     * @param param
     * @return
     */
    @PostMapping("/getMiniAccountPackageGoods")
    @ApiOperation(value = "分页根据用户权益包订单获取权益包商品")
    public Result<PageUtils<MiniAccountPackageGoodsVo>>getMiniAccountPackageGoods(@RequestBody MiniAccountPackageGoodsParam param){
        //更新用户权益包商品状态
        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(param.getMainId());
        //分页根据小程序用户权益包订单获取可核销权益包商品
        IPage<MiniAccountPackageGoodsVo> pageList = miniAccountPackageGoodsService.getMiniAccountPackageGoods(param);
        return Result.ok(new PageUtils<>(pageList));
    }

    /**
     * 获取权益包商品验证码
     * @param miniAccountPackageGoodsDto
     * @return
     */
    @PostMapping("/getCode")
    @ApiOperation(value = "获取权益包商品验证码")
    public Result<MiniAccountPackageGoodsCodeVo> getCode(@RequestBody MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        MiniAccountPackageGoodsCodeVo codeVo = miniAccountPackageGoodsCodeService.getCode(miniAccountPackageGoodsDto);
        return Result.ok(codeVo);
    }

    /**
     * 分页获取小程序用户核销记录
     * @param param
     * @return
     */
    @PostMapping("/pageApiPackageGoodsCode")
    @ApiOperation(value = "分页获取小程序用户核销记录")
    public Result<PageUtils<ApiPackageGoodsCodeVo>> pageApiPackageGoodsCode(@RequestBody ApiPackageGoodsCodeParam param){
        IPage<ApiPackageGoodsCodeVo> pageList = miniAccountPackageGoodsCodeService.pageApiPackageGoodsCode(param);
        return Result.ok(new PageUtils<>(pageList));
    }


    /**
     * 分页获取小程序用户权益包订单商品
     * @param miniAccountPackageGoodsParam
     * @return
     */
    @PostMapping("/getPageList")
    @ApiOperation(value = "分页获取小程序用户权益包订单商品")
    public Result<PageUtils<MiniAccountPackageGoodsVo>> getMiniAccountCouponSearchVo(@RequestBody MiniAccountPackageGoodsParam miniAccountPackageGoodsParam){

        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(miniAccountPackageGoodsParam.getMainId());
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        miniAccountPackageGoodsParam.setUserId(userId);
        IPage<MiniAccountPackageGoodsVo> pageList = miniAccountPackageGoodsService.getPageList(miniAccountPackageGoodsParam);
        return Result.ok(new PageUtils<>(pageList));
    }

    /**
     * 根据用户权益包订单id获取权益包商品
     * @param mainId
     * @return
     */
    @GetMapping("/getApiPackageGoods")
    @ApiOperation(value = "根据用户权益包订单id获取权益包商品")
    public Result<List<ApiPackageGoodsVo>>  getApiPackageGoods(@RequestParam(value = "mainId",required = true) String mainId){
        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(mainId);
        List<ApiPackageGoodsVo> list = miniAccountPackageGoodsService.getApiPackageGoods(mainId);
        return Result.ok(list);
    }
    /**
     * 获取用户受赠的权益包商品
     * @return
     */
    @GetMapping("/getGiftPackageGoods")
    @ApiOperation(value = "获取用户受赠的权益包商品")
    public Result<List<ApiPackageGoodsVo>>  getGiftPackageGoods(){
        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(null);
        List<ApiPackageGoodsVo> list = miniAccountPackageGoodsService.getGiftPackageGoods();
        return Result.ok(list);
    }
}
