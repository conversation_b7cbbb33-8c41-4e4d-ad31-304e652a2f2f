package com.medusa.gruul.account.web.controller;


import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.model.dto.MemberLevelDto;
import com.medusa.gruul.account.model.dto.UserListDto;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMemberLevelService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 外部系统获取会员信息
 */
@RestController
@RequestMapping("/out/mini-account")
@Api(tags = "外部系统获取会员相关接口")
public class OutMiniAccountController {

    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMemberLevelService memberLevelService;

    @PostMapping("/externalUserList")
    @ApiOperation(value = "外部系统获取用户列表")
    public Result<PageUtils<List<UserListVo>>> externalUserList(
            @ApiParam(value = "指定页数") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "数据条数") @RequestParam(defaultValue = "10") Integer size
    ) {
        PageUtils<List<UserListVo>> voPageUtils = miniAccountService.externalUserList( page, size);
        return Result.ok(voPageUtils);
    }


    @PostMapping("/getUserList")
    @ApiOperation(value = "外部系统获取用户列表-不获取会员等级，会员标签")
    public Result<PageUtils<List<UserListVo>>> getUserList(
            @ApiParam(value = "指定页数") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "数据条数") @RequestParam(defaultValue = "10") Integer size
    ) {
        PageUtils<List<UserListVo>> voPageUtils = miniAccountService.getUserList( page, size);
        return Result.ok(voPageUtils);
    }

    @PostMapping("/saveUser")
    @ApiOperation(value = "保存外部系统接口对接的用户")
    public Result saveUser(@RequestBody UserListDto userListDto) {
        MiniAccount miniAccount = this.miniAccountService.outSave(userListDto);
        return Result.ok(miniAccount, "保存成功");
    }



    @PostMapping("/externalMemberLevelList")
    @ApiOperation(value = "外部系统获取会员卡列表")
    public Result <PageUtils<List<MemberLevelVo>>>externalMemberLevelList(
            @ApiParam(value = "指定页数") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "数据条数") @RequestParam(defaultValue = "10") Integer size
    ){
        PageUtils<List<MemberLevelVo>> voPageUtils = memberLevelService.externalMemberList( page, size);
        return Result.ok(voPageUtils);
    }
    @PostMapping("/saveMemberLevel")
    @ApiOperation(value = "添加或者修改会员等级信息")
    //@EscapeLogin
    public Result saveMemberLevel(@RequestBody MemberLevelDto memberLevelDto) {
        MemberLevel memberLevel = memberLevelService.outSave(memberLevelDto);
        return Result.ok(memberLevel, "保存成功");
    }



}
