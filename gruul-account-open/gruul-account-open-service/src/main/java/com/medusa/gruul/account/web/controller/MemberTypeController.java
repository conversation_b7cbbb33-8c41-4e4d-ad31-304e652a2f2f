package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.model.dto.AddOrUpdateMemberTypeDto;
import com.medusa.gruul.account.model.dto.MemberTypeDto;
import com.medusa.gruul.account.model.param.MemberTypeParam;
import com.medusa.gruul.account.model.vo.MemberTypeVo;
import com.medusa.gruul.account.service.IMemberTypeService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:44 2025/5/20
 */
@RestController
@RequestMapping("/member-type")
@Api(tags = "会员类型相关接口")
public class MemberTypeController {

    @Autowired
    private IMemberTypeService memberTypeService;

    /**
     * 新增/修改会员类型
     * @param dto
     * @return
     */
    @PostMapping("/addOrUpdateMemberType")
    @ApiOperation(value = "新增/修改会员类型")
    public Result addOrUpdateMemberType(@RequestBody AddOrUpdateMemberTypeDto dto) {
        memberTypeService.addOrUpdateMemberType(dto);
        return Result.ok("提交成功");
    }

    /**
     * 删除会员类型
     * @param dto
     * @return
     */
    @PostMapping("/deleteMemberType")
    @ApiOperation(value = "删除会员类型")
    public Result deleteMemberType(@RequestBody MemberTypeDto dto) {
        memberTypeService.deleteMemberType(dto);
        return Result.ok("删除成功");
    }

    /**
     * 获取会员类型
     * @param param
     * @return
     */
    @PostMapping("/getMemberType")
    @ApiOperation(value = "获取会员类型")
    public Result getMemberType(@RequestBody MemberTypeParam param) {
        List<MemberTypeVo> list = memberTypeService.getMemberType(param);
        return Result.ok(list);
    }

    /**
     * 启用/停用会员类型
     * @param dto
     * @return
     */
    @PostMapping("/updateStatus")
    @ApiOperation(value = "启用/停用会员类型")
    public Result updateStatus(@RequestBody MemberTypeDto dto) {
        String result = memberTypeService.updateStatus(dto);
        return Result.ok(result);
    }

    /**
     * 设置为默认会员类型
     * @param dto
     * @return
     */
    @PostMapping("/setDefaultType")
    @ApiOperation(value = "设置为默认会员类型")
    public Result setDefaultType(@RequestBody MemberTypeDto dto) {
        memberTypeService.setDefaultType(dto);
        return Result.ok("设置成功");
    }

}
