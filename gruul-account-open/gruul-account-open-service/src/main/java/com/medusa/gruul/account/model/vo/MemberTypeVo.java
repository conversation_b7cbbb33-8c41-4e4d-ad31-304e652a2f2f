package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:03 2025/5/20
 */
@Data
@ApiModel(value = "会员类型查询列表vo")
public class MemberTypeVo {

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String name;

    /**
     * 默认会员类型->0-否，1-是
     */
    @ApiModelProperty(value = "默认会员类型->0-否，1-是")
    private Integer defaultType;

    /**
     * 区域类型标识->0-否，1-是
     */
    @ApiModelProperty(value = "区域类型标识->0-否，1-是")
    private Integer regionFlag;

    /**
     * 并存类型标识->0-否，1-是
     */
    @ApiModelProperty(value = "并存类型标识->0-否，1-是")
    private Integer togetherFlag;

    /**
     * 状态->0-停用，1-启用
     */
    @ApiModelProperty(value = "状态->0-停用，1-启用")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
