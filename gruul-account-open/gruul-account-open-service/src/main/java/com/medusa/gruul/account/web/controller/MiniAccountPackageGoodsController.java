package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsShowVo;
import com.medusa.gruul.account.model.dto.MiniAccountPackageGoodsDto;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketCodeDto;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsCodeVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.account.model.vo.MiniAccountPassTicketCodeVo;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:43 2024/9/5
 */
@RestController
@RequestMapping("/mini-account-package-goods")
@Api(tags = "用户权益包订单商品相关接口")
public class MiniAccountPackageGoodsController {

    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;


    /**
     * 分页获取用户权益包订单商品
     * @param  miniAccountPackageGoodsParam
     * @return
     */
    @PostMapping("/getPageList")
    @ApiOperation(value = "分页获取用户权益包订单商品")
    public Result<PageUtils<MiniAccountPackageGoodsVo>> getMiniAccountCouponSearchVo(@RequestBody MiniAccountPackageGoodsParam miniAccountPackageGoodsParam){
        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(miniAccountPackageGoodsParam.getMainId());
        IPage<MiniAccountPackageGoodsVo> pageList = miniAccountPackageGoodsService.getPageList(miniAccountPackageGoodsParam);
        return Result.ok(new PageUtils<>(pageList));
    }

    /**
     * 获取权益包商品核销记录
     * @param orderId
     * @param packageId
     * @return
     */
    @GetMapping("/getPackageGoodsShowVo")
    @ApiOperation(value = "获取权益包商品核销记录")
    public Result<List<PackageGoodsShowVo>>getPackageGoodsShowVo(@ApiParam(value = "orderId", required = true) @RequestParam String orderId,
                                                                 @ApiParam(value = "packageId", required = true) @RequestParam String packageId){
        List<PackageGoodsShowVo>list = miniAccountPackageGoodsService.getPackageGoodsShowVo(orderId,packageId);
        return Result.ok(list);
    }
    @PostMapping("/exportSaleDetail")
    @ApiOperation(value = "销售明细-导出")
    public void exportMiniAccountPackageGoods(@RequestBody MiniAccountPackageGoodsParam miniAccountPackageGoodsParam){
        miniAccountPackageGoodsService.updateMiniAccountPackageGoodsStatus(miniAccountPackageGoodsParam.getMainId());
        miniAccountPackageGoodsService.export(miniAccountPackageGoodsParam);

    }
}
