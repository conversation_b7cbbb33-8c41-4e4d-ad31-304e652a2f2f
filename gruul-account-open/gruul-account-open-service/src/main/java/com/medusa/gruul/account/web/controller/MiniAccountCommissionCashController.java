package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.model.param.MiniAccountCommissionCashParam;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashVo;
import com.medusa.gruul.account.model.vo.ReceiveMoneyVo;
import com.medusa.gruul.account.service.IMiniAccountCommissionCashService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.dto.ApproveDataParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: plh
 * @Description: 后台管理-提现相关接口
 * @Date: Created in 11:00 2023/8/31
 */
@RestController
@RequestMapping("/mini-account-commission")
@Api(tags = "后台管理-提现相关接口")
public class MiniAccountCommissionCashController {

    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;

    /**
     * 审批提现金额
     * @param approveDataParam
     * @return
     */
    @PostMapping("/approve")
    @ApiOperation(value = "审批提现金额")
    public Result  approve(@RequestBody @Validated ApproveDataParam approveDataParam, HttpServletRequest request){
        miniAccountCommissionCashService.approve(approveDataParam,request);
        return Result.ok("审批完成");
    }

    /**
     * 申请打款
     * @param id
     * @param request
     * @return
     */
    @GetMapping("/payCommission")
    @ApiOperation(value = "申请打款")
    public Result  payCommission(@RequestParam(value = "id",required = true) String id, HttpServletRequest request){
        miniAccountCommissionCashService.payCommission(id,request);
        return Result.ok("申请打款成功，请静候结果！");
    }

    /**
     * 批量审批提现金额
     * @param list
     * @return
     */
    @PostMapping("/batchApprove")
    @ApiOperation(value = "批量审批提现金额")
    public Result batchApprove(@RequestBody @Validated List<ApproveDataParam> list,HttpServletRequest request){
        miniAccountCommissionCashService.batchApprove(list,request);
        return Result.ok("审批完成");
    }
    /**
     * 获取用户提现列表
     * @param miniAccountCommissionCashParam
     * @return
     */
    @PostMapping("/getMiniAccountCommissionCashVo")
    @ApiOperation(value = "获取用户提现列表")
    public Result<PageUtils<MiniAccountCommissionCashVo>>getMiniAccountCommissionCashVo(@RequestBody @Validated MiniAccountCommissionCashParam miniAccountCommissionCashParam){
        PageUtils<MiniAccountCommissionCashVo> miniAccountCommissionCashVoPageUtils = miniAccountCommissionCashService.getMiniAccountCommissionCashVo(miniAccountCommissionCashParam);
        return Result.ok(miniAccountCommissionCashVoPageUtils);
    }

    /**
     * 更新佣金提现记录支付状态
     * @return
     */
    @PostMapping("/updatePayStatus")
    @ApiOperation(value = "更新佣金提现记录支付状态")
    public Result updatePayStatus(){
        miniAccountCommissionCashService.updatePayStatus();
        return Result.ok("更新成功");
    }

    /**
     * 导出用户提现列表
     * @param miniAccountCommissionCashParam
     * @return
     */
    @PostMapping("/export/miniAccountCommissionCash")
    @ApiOperation(value = "导出用户提现列表")
    public void exportMiniAccountCommissionCash(@RequestBody @Validated MiniAccountCommissionCashParam miniAccountCommissionCashParam){
        miniAccountCommissionCashService.exportMiniAccountCommissionCashVo(miniAccountCommissionCashParam);
    }

    @GetMapping("/receiveMoney")
    @ApiOperation(value = "确认收款")
    public Result<ReceiveMoneyVo>  receiveMoney(@RequestParam(value = "id",required = true) String id){
        ReceiveMoneyVo receiveMoneyVo = miniAccountCommissionCashService.receiveMoney(id);
        return Result.ok(receiveMoneyVo);
    }
    @GetMapping("/updateTransferState")
    @ApiOperation(value = "更新微信转账单状态")
    public Result updateTransferState(@RequestParam(value = "id",required = true) String id){
        miniAccountCommissionCashService.updateTransferState(id);
        return Result.ok("更新成功！");
    }
}
