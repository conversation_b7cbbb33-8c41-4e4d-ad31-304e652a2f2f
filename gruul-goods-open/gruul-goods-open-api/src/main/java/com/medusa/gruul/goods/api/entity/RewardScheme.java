package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 奖励方案
 * @Date: Created in 17:20 2025/3/10
 */
@Data
@Accessors(chain = true)
@TableName("t_reward_scheme")
@ApiModel(value = "RewardScheme对象", description = "奖励方案")
public class RewardScheme extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 商铺id
     */
    @ApiModelProperty(value = "商铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    @TableField(value = "bill_no")
    private String billNo;

    /**
     * 单据日期
     */
    @ApiModelProperty(value = "单据日期")
    @TableField("bill_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime billDate;

    /**
     * 有效期-开始时间
     */
    @ApiModelProperty(value = "有效期-开始时间")
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 有效期-结束时间
     */
    @ApiModelProperty(value = "有效期-结束时间")
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 经手人id
     */
    @ApiModelProperty(value = "经手人id")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 经手人名称
     */
    @ApiModelProperty(value = "经手人名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    @TableField(value = "name")
    private String name;

    /**
     * 状态：0->草稿；1->生效中；-1->失效；-2->停止
     */
    @ApiModelProperty(value = "状态：0->草稿；1->生效中；-1->失效；-2->停止")
    @TableField(value = "status")
    private Integer status;

    /**
     * 审核状态:100->待审核;101->已审核;200->审核不通过
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->已审核;200->审核不通过")
    @TableField(value = "approval_status")
    private Integer approvalStatus;

    /**
     * 审核原因
     */
    @ApiModelProperty(value = "审核原因")
    @TableField(value = "approval_reason")
    private String approvalReason;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField("approval_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime approvalTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "remark")
    private String remark;
}
