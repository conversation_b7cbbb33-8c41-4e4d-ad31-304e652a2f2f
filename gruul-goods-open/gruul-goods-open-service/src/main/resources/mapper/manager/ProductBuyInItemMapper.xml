<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.mapper.manager.ProductBuyInItemMapper">

    <resultMap id="ProductBuyInItemVoMap" type="com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInItemVo">
        <result column="id" property="id"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="product_id" property="productId"/>
        <result column="productName" property="productName"/>
        <result column="unit" property="unit"/>
        <result column="sku_id" property="skuId"/>
        <result column="skuSpecs" property="skuSpecs"/>
        <result column="instore_qty" property="instoreQty"/>
        <result column="instore_price" property="instorePrice"/>
        <result column="instore_amount" property="instoreAmount"/>
        <result column="remarks" property="remarks"/>
        <result column="unit_id" property="unitId"/>
        <result column="specs2" property="specs2"/>
    </resultMap>
    <resultMap id="ProductBuyInItemVoAllMap" type="com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInItemAllVo">
        <result column="id" property="id"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="product_id" property="productId"/>
        <result column="productName" property="productName"/>
        <result column="unit" property="unit"/>
        <result column="sku_id" property="skuId"/>
        <result column="skuSpecs" property="skuSpecs"/>
        <result column="instore_qty" property="instoreQty"/>
        <result column="instore_price" property="instorePrice"/>
        <result column="instore_amount" property="instoreAmount"/>
        <result column="remarks" property="itemRemarks"/>
        <result column="unit_id" property="unitId"/>
        <result column="specs2" property="specs2"/>

        <result column="buy_date" property="buyDate"/>
        <result column="buy_no" property="buyNo"/>
        <result column="handlerName" property="handlerName"/>
        <result column="preparerName" property="preparerName"/>
        <result column="supplierName" property="supplierName"/>
        <result column="warehouse" property="warehouse"/>
        <result column="buyTypeName" property="buyTypeName"/>
        <result column="main_remarks" property="remarks"/>
    </resultMap>


    <select id="getByProductBuyInId" resultMap="ProductBuyInItemVoMap">
        SELECT pr.name as productName
            ,d.specs as skuSpecs
            ,d.specs2 as specs2
            , c.unit as unit
            ,itme.* FROM t_product_buy_in_item as itme LEFT JOIN t_product as pr on itme.product_id=pr.id
            left join t_product_unit c on itme.unit_id = c.id left join t_sku_stock as d on d.id = itme.sku_id
        where itme.is_deleted='0' and itme.product_buy_in_id=#{productBuyInId}
    </select>

    <select id="selectList" resultMap="ProductBuyInItemVoAllMap">
        SELECT pr.name as productName
        ,d.specs as skuSpecs
        ,d.specs2 as specs2
        , c.unit as unit
        ,itme.*, buy.buy_date as buy_Date,  buy.buy_no as buy_no,
        buy.handler_id as handler_id, buy.preparer_id as preparer_id, buy.supplier_id as supplier_id, buy.warehouse_id as warehouse_id,
        buy.buy_type as buy_type, buy.remarks as main_remarks,
        info.nike_name as preparerName
        ,supplier.name as supplierName
        ,wfn.warehouse_full_name as warehouse
        ,item.item_text as buyTypeName
        ,tpai.nike_name as handlerName
        FROM t_product_buy_in_item as itme
        LEFT JOIN t_product as pr on itme.product_id=pr.id
        left join t_product_unit c on itme.unit_id = c.id
        left join t_sku_stock as d on d.id = itme.sku_id
        left join t_product_buy_in as buy on buy.id = itme.product_buy_in_id
        left join t_platform_account_info as info on info.id = buy.preparer_id
        left join t_supplier as supplier on supplier.id = buy.supplier_id
        left join t_warehouse as wfn on wfn.id = buy.warehouse_id
        left join t_dict_item as item on item.item_value = buy.buy_type
        left join t_dict as  dict on dict.id= item.dict_id
        left join t_platform_account_info as tpai on tpai.id=buy.handler_id
        where itme.is_deleted=0 and buy.is_deleted=0  and dict.dict_code='warehousingType'
        <if test="productBuyInItemParam.productName!=null and productBuyInItemParam.productName!=''">
            and pr.name like CONCAT('%',#{productBuyInItemParam.productName},'%')
        </if>
        <if test="productBuyInItemParam.specs!=null and productBuyInItemParam.specs!=''">
            and (d.specs like CONCAT('%',#{productBuyInItemParam.specs},'%')
            or d.specs2 like CONCAT('%',#{productBuyInItemParam.specs},'%')
            )
        </if>
        <if test="productBuyInItemParam.warehouseName!=null and productBuyInItemParam.warehouseName!=''">
            and wfn.warehouse_full_name like CONCAT('%',#{productBuyInItemParam.warehouseName},'%')
        </if>
        <if test="productBuyInItemParam.buyType!=null and productBuyInItemParam.buyType!=''">
            and buy.buy_type=#{productBuyInItemParam.buyType}
        </if>
        <if test="productBuyInItemParam.startDate!=null and productBuyInItemParam.startDate!=''">
            and buy.buy_date>=#{productBuyInItemParam.startDate}
        </if>
        <if test="productBuyInItemParam.endDate!=null and productBuyInItemParam.endDate!=''">
            and buy.buy_date &lt;= #{productBuyInItemParam.endDate}
        </if>
        <if test="productBuyInItemParam.buyNo!=null and productBuyInItemParam.buyNo!='' ">
            and buy.buy_no like CONCAT('%',#{productBuyInItemParam.buyNo},'%')
        </if>
        <if test="productBuyInItemParam.remarks!=null and productBuyInItemParam.remarks!='' ">
            and buy.remarks like CONCAT('%',#{productBuyInItemParam.remarks},'%')
        </if>
        <if test="productBuyInItemParam.createUserName!=null and productBuyInItemParam.createUserName!='' ">
            and info.nike_name like CONCAT('%',#{productBuyInItemParam.createUserName},'%')
        </if>
        <if test="productBuyInItemParam.supplierName!=null and productBuyInItemParam.supplierName!='' ">
            and supplier.name like CONCAT('%',#{productBuyInItemParam.supplierName},'%')
        </if>
        ORDER BY buy.buy_date DESC,buy.id DESC
    </select>
</mapper>