package com.medusa.gruul.goods.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.model.param.api.ApiProductParam;
import com.medusa.gruul.goods.api.model.vo.api.ApiAliveProductVo;
import com.medusa.gruul.goods.api.model.vo.api.ApiProductVo;
import com.medusa.gruul.goods.api.model.vo.api.ApiShowCategoryProductVo;
import com.medusa.gruul.goods.service.api.IApiProductService;
import com.medusa.gruul.goods.service.manager.IWarehouseService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 小程序商品信息 前端控制器
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@RestController
@RequestMapping("/api/product")
public class ApiProductController {

    @Autowired
    private IApiProductService apiProductService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Autowired
    private IWarehouseService warehouseService;
    /**
     * 商品详情
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "商品详情")
    public Result<ApiProductVo> getProductById(@PathVariable("id") Long id,
                                               @ApiParam(value = "纬度", required = false) @RequestParam(value = "latitude",required = false) Double latitude,
                                               @ApiParam(value = "经度", required = false) @RequestParam(value = "longitude",required = false) Double longitude,
                                               @ApiParam(value = "价格类型", required = false) @RequestParam(value = "memberTypeId",required = false) Long memberTypeId) {
        ApiProductVo product = apiProductService.getProductById(id,latitude,longitude,memberTypeId);
        Result<ApiProductVo> result = Result.ok(product);
        return result;
    }

    @GetMapping("/get/getWarehouse")
    @ApiOperation(value = "获取仓库列表")
    public Result<List<Warehouse>>getWarehouse(@RequestParam(value = "shopId",required = false) String shopId){
        ShopContextHolder.setShopId(shopId);
        LambdaQueryWrapper<Warehouse>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Warehouse::getDeleted,0);
        wrapper.eq(Warehouse::getState,1);
        List<Warehouse> list = warehouseService.list(wrapper);
        return Result.ok(list);
    }

    /**
     * 首页商品列表-搜索
     */
    @GetMapping("/index/list")
    @ApiOperation(value = "首页商品列表")
    @EscapeLogin
    public Result<PageUtils<ApiAliveProductVo>> getIndexList(ApiProductParam productParam) {
        //更新过期权益包商品为下架
        apiProductService.updatePackageGoodsStatus();
        PageUtils<ApiAliveProductVo> pageUtils = new PageUtils<>(apiProductService.getPageList(productParam));
        return Result.ok(pageUtils);
    }



    /**
     * 商超系统分类列表
     */
    @GetMapping("/supermarket/list")
    @ApiOperation(value = "商超系统分类")
    @EscapeLogin
    public Result<PageUtils<ApiAliveProductVo>> getSupermarketList(ApiProductParam productParam) {
        PageUtils<ApiAliveProductVo> pageUtils = new PageUtils<>(apiProductService.getSupermarketList(productParam));
        return Result.ok(pageUtils);
    }

    //=============================================商品组件根据商品集合匹配未删除的商品===================================================

    /**
     * 根据商品数组匹配未删除的商品
     *
     * @param ids
     * @param launchArea
     * @param saleMode
     * @return
     */
    @GetMapping("/alive/product/list/{ids}")
    @ApiOperation(value = "根据商品数组匹配未删除的商品")
    @EscapeLogin
    public Result<List<ApiAliveProductVo>> getAliveProductList(@ApiParam(value = "商品ids", required = true) @PathVariable(name = "ids") Long[] ids,
                                                               @ApiParam(value = "投放区域id") @RequestParam(value = "launchArea",required = false) String launchArea,
                                                               @ApiParam(value = "商品专区") @RequestParam(value = "saleMode",required = false) Long saleMode,
                                                               @ApiParam(value = "纬度", required = false) @RequestParam(value = "latitude",required = false) Double latitude,
                                                               @ApiParam(value = "经度", required = false) @RequestParam(value = "longitude",required = false) Double longitude,
                                                               @ApiParam(value = "会员类型id", required = false) @RequestParam(value = "memberTypeId",required = false) Long memberTypeId,
                                                               @ApiParam(value = "是否分区展示", required = false) @RequestParam(value = "partitionType",required = false) Integer partitionType) {
        List<ApiAliveProductVo> saveList = apiProductService.getAliveProductList(ids, launchArea, saleMode,latitude,longitude,memberTypeId,partitionType);
        return Result.ok(saveList);
    }



    //=============================================商品组件根据商品集合匹配未删除的商品===================================================

    //=============================================商品分类页组件根据商品分类集合匹配对应分类下的商品===================================================

    /**
     * pc商品分类集合匹配对应分类下的商品 无投放区域与商品专区
     *
     * @param ids
     * @return
     */
    @GetMapping("pc/get/alive/product/list/group/by/category/{ids}")
    @ApiOperation(value = "pc商品分类集合匹配对应分类下的商品")
    @EscapeLogin
    public Result<List<ApiShowCategoryProductVo>> getAliveProductListGroupByCategory(@ApiParam(value = "分类ids", required = true) @PathVariable(name = "ids") Long[] ids) {
        List<ApiShowCategoryProductVo> saveList = apiProductService.getAliveProductListGroupByCategory(ids);
        return Result.ok(saveList);
    }

    /**
     * 商品分类集合匹配对应分类下的商品
     *
     * @param ids
     * @param saleMode
     * @return
     */
    @GetMapping("get/alive/product/list/by/category/{ids}")
    @ApiOperation(value = "商品分类集合匹配对应分类下的商品")
    @EscapeLogin
    public Result<List<ApiShowCategoryProductVo>> getAliveProductListByCategory(@ApiParam(value = "分类ids", required = true) @PathVariable(name = "ids") Long[] ids,
                                                                                @ApiParam(value = "商品专区", required = false) @RequestParam(value = "saleMode",required = false) Long saleMode,
                                                                                @ApiParam(value = "纬度", required = false) @RequestParam(value = "latitude",required = false) Double latitude,
                                                                                @ApiParam(value = "经度", required = false) @RequestParam(value = "longitude",required = false) Double longitude,
                                                                                @ApiParam(value = "会员类型id", required = false) @RequestParam(value = "memberTypeId",required = false) Long memberTypeId) {
        List<ApiShowCategoryProductVo> saveList = apiProductService.getAliveProductListByCategory(ids,saleMode,latitude,longitude,memberTypeId);
        return Result.ok(saveList);
    }

    /**
     * 更新过期权益包商品为下架
     * @return
     */
    @PostMapping("update/alive/packageGoods/Status")
    @ApiOperation(value = "更新过期权益包商品为下架")
    @EscapeLogin
    public Result<?>updatePackageGoodsStatus(){

        apiProductService.updatePackageGoodsStatus();

        return Result.ok("更新成功");
    }


    /**
     *
     * @return
     */
    @GetMapping("/getOrderHistoryGoods/list")
    @ApiOperation(value = "获取历史订单商品")
    @EscapeLogin
    public Result<List<ApiAliveProductVo>>getOrderHistoryGoods(){
        List<ApiAliveProductVo> list = apiProductService.getOrderHistoryGoods();
        return Result.ok(list);
    }




}
