package com.medusa.gruul.goods.web.controller.api;

import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.model.param.manager.MiniIntegralProductParam;
import com.medusa.gruul.goods.api.model.vo.api.ApiProductVo;
import com.medusa.gruul.goods.api.model.vo.manager.IntegralProductVo;
import com.medusa.gruul.goods.api.model.vo.manager.MiniIntegralProductVo;
import com.medusa.gruul.goods.service.api.IApiProductService;
import com.medusa.gruul.goods.service.manager.IIntegralProductService;
import com.medusa.gruul.goods.service.manager.IShowCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: plh
 * @Description: 小程序积分商城 前端控制器
 * @Date: Created in 17:39 2023/8/23
 */
@RestController
@RequestMapping("/api/integral-product")
@Api(tags = "积分商城接口")
public class ApiIntegralProductController {

    @Autowired
    private IShowCategoryService showCategoryService;

    @Autowired
    private IIntegralProductService integralProductService;
    @Autowired
    private IApiProductService apiProductService;
    /**
     * 获取积分商城分类
     * @return
     */
    @GetMapping("/getIntegralShowCategory")
    @ApiOperation(value = "获取积分商城分类")
    public Result<List<String>> getIntegralShowCategory(){
        List<String> list = showCategoryService.getIntegralShowCategory();
        return Result.ok(list);
    }

    /**
     * 获取积分商城商品
     * @param miniIntegralProductParam
     * @return
     */
    @GetMapping("/getIntegralProduct")
    @ApiOperation(value = "获取积分商城商品")
    public Result<PageUtils<MiniIntegralProductVo> > getIntegralProduct(MiniIntegralProductParam miniIntegralProductParam){
        PageUtils<MiniIntegralProductVo> page = new PageUtils<>(integralProductService.getIntegralProduct(miniIntegralProductParam));
        return Result.ok(page);
    }
    /**
     * 积分商品详情
     */
    @GetMapping("/get/Integral/{id}")
    @ApiOperation(value = "积分商品详情")
    public Result<ApiProductVo> getIntegralProductById(@PathVariable("id") Long id,
                                                       @ApiParam(value = "纬度", required = false) @RequestParam(value = "latitude",required = false) Double latitude,
                                                       @ApiParam(value = "经度", required = false) @RequestParam(value = "longitude",required = false) Double longitude) {
        ApiProductVo product = apiProductService.getIntegralProductById(id, latitude, longitude);
        Result<ApiProductVo> result = Result.ok(product);
        return result;
    }
}
