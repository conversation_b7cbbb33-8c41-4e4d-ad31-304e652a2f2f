package com.medusa.gruul.goods.web.controller.manager;


import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.entity.SaleMode;
import com.medusa.gruul.goods.api.model.dto.manager.SaleModeDto;
import com.medusa.gruul.goods.api.model.param.manager.SaleModeParam;
import com.medusa.gruul.goods.api.model.vo.manager.SaleModeVo;
import com.medusa.gruul.goods.service.manager.ISaleModeService;
import com.medusa.gruul.goods.web.enums.SaleModeEnum;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 商品自定义专区 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@RestController
@RequestMapping("/manager/sale/mode")
public class SaleModeController {

    @Autowired
    private ISaleModeService saleModeService;

    /**
     * 获取商品专区列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "商品专区列表")
    public Result<PageUtils<SaleModeVo>> getSaleModeList(SaleModeParam saleModeParam) {
        PageUtils<SaleModeVo> pageUtils = new PageUtils<>(saleModeService.getSaleModeList(saleModeParam));
        return Result.ok(pageUtils);
    }

    /**
     * 获取商品专区列表
     */
    @GetMapping("/manage/list")
    @ApiOperation(value = "查询专区列表-商品管理")
    public Result<PageUtils<SaleModeVo>> getSaleModeListgetManageSaleModeList(SaleModeParam saleModeParam) {
        PageUtils<SaleModeVo> pageUtils = new PageUtils<>(saleModeService.getManageSaleModeList(saleModeParam));
        return Result.ok(pageUtils);
    }

    /**
     * 获取所有商品专区列表
     */
    @GetMapping("/get/all")
    @ApiOperation(value = "商品所有专区列表")
    public Result<List<SaleModeVo>> getAllSaleModeList(@ApiParam(value = "店铺id", required = false) @RequestParam(name = "shopId",required = false) String shopId) {
        List<SaleModeVo> saleModeVos = saleModeService.getAllSaleModeList(shopId);
        return Result.ok(saleModeVos);
    }

    /**
     * 获取所有商品专区列表
     */
    @GetMapping("/get/manage/all")
    @ApiOperation(value = "商品所有专区列表-商品管理")
    public Result<List<SaleModeVo>> getAllManageSaleModeList() {
        List<SaleModeVo> saleModeVos = saleModeService.getAllManageSaleModeList();
        return Result.ok(saleModeVos);
    }

    /**
     * 设置默认商品专区
     */
    @PostMapping("/setDefaultSale/{id}")
    @ApiOperation(value = "设置默认商品专区")
    public Result setDefaultSale(@ApiParam(value = "展示分类id", required = true) @PathVariable(name = "id") Long id) {
        List<SaleMode> list=saleModeService.list();
        //全部改为不是默认专区
       for(SaleMode sm:list){
           sm.setDefaultSale(SaleModeEnum.NOT_DEFAULT_VALUE.getSaleMode());
       }
//       改为默认专区
        saleModeService.updateBatchById(list);
        SaleMode saleMode=saleModeService.getById(id);
        saleMode.setDefaultSale(SaleModeEnum.DEFAULT_VALUE.getSaleMode());
        saleModeService.updateById(saleMode);
        return Result.ok();
    }

    /**
     * 商品专区新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "商品专区新增")
    public Result addSaleMode(@RequestBody @Validated List<SaleModeDto> saleModeDtos) {
        saleModeService.addSaleMode(saleModeDtos);
        return Result.ok();
    }

    /**
     * 商品专区修改
     */
    @PutMapping("/update")
    @ApiOperation(value = "商品专区修改")
    public Result updateSaleMode(@RequestBody @Validated SaleModeDto saleModeDto) {
        saleModeService.updateSaleMode(saleModeDto);
        return Result.ok();
    }

    /**
     * 商品专区删除
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "商品专区删除")
    public Result deleteShowCategoryList(@ApiParam(value = "展示分类id", required = true) @PathVariable(name = "id") Long id) {
        saleModeService.deleteSaleMode(id);
        return Result.ok();
    }

    /**
     * 商品专区排序
     */
    @PutMapping("/sort")
    @ApiOperation(value = "商品专区排序")
    public Result updateSaleModeSort(@RequestBody List<SaleModeDto> saleModeDtos) {
        saleModeService.updateSaleModeSort(saleModeDtos);
        return Result.ok();
    }



}
