package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.vo.UserIntegralVo;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.constant.IntegralGoodsExchangeNumRedisKey;
import com.medusa.gruul.goods.api.entity.IntegralActivity;
import com.medusa.gruul.goods.api.entity.IntegralProduct;
import com.medusa.gruul.goods.api.enums.ProjectStatusEnum;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralProductDetDto;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralProductDto;
import com.medusa.gruul.goods.api.model.param.manager.IntegralProductListParam;
import com.medusa.gruul.goods.api.model.param.manager.MiniIntegralProductParam;
import com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartVo;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.api.param.OperateExchangeNumParam;
import com.medusa.gruul.goods.mapper.manager.IntegralProductMapper;
import com.medusa.gruul.goods.service.manager.IIntegralActivityService;
import com.medusa.gruul.goods.service.manager.IIntegralProductService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: 积分商品服务实现类
 * @Date: Created in 11:48 2023/8/21
 */
@Service
@Log4j2
public class IntegralProductServiceImpl extends ServiceImpl<IntegralProductMapper, IntegralProduct> implements IIntegralProductService {

    @Autowired
    private IIntegralActivityService integralActivityService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addIntegralProduct(IntegralProductDto integralProductDto) {
        //积分商品列表
        List<IntegralProductDetDto> list = integralProductDto.getList();
        //积分活动id
        Long activityId = integralProductDto.getActivityId();
        //积分活动
        IntegralActivity integralActivity = integralActivityService.getById(activityId);
        if(integralActivity==null){
            throw new ServiceException("积分活动不存在！");
        }

        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //开始时间
        LocalDateTime startTime = integralActivity.getStartTime();

        //结束时间
        LocalDateTime endTime = integralActivity.getEndTime();
        List<String>productList = new ArrayList<>();
        for (IntegralProductDetDto integralProductDetDto : list) {
            //商品id
            Long productId = integralProductDetDto.getProductId();
            //规格id
            String skuId = integralProductDetDto.getSkuId();
            String productSkuId = productId+"-"+skuId;

            if(productList.contains(productSkuId)){
                throw new ServiceException("积分商品不能重复上传！");
            }else{
                productList.add(productSkuId);
            }
            Map map = new HashMap();
            map.put("productId",productId);
            map.put("skuId",skuId);
            map.put("startTime",startTime.format(fmt));
            map.put("endTime",endTime.format(fmt));
            Integer count = this.baseMapper.getIntegralProductByEndTime(map);
            if(count>0){
                throw new ServiceException("在活动日期内已存在该商品");
            }
            IntegralProduct integralProduct = new IntegralProduct();
            BeanUtils.copyProperties(integralProductDetDto,integralProduct);
            integralProduct.setActivityId(activityId);
            //新增积分商品默认已兑数量为0
            integralProduct.setAlreadyExchangeNum(new BigDecimal(0));
            CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
            integralProduct.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
            integralProduct.setCreateUserName(pcUserInfoDto.getNikeName());
            integralProduct.setChangeFlag(0);
            this.baseMapper.insert(integralProduct);
            IntegralGoodsExchangeNumRedisKey integralGoodsExchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
            BigDecimal allExchangeNum = integralProduct.getAllExchangeNum();
            BigDecimal alreadyExchangeNum = integralProduct.getAlreadyExchangeNum();
            BigDecimal canExchangeNum = allExchangeNum.subtract(alreadyExchangeNum);
            integralGoodsExchangeNumRedisKey.set(integralProduct.getId().toString(), canExchangeNum.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyIntegralProduct(IntegralProductDto integralProductDto) {
        //积分商品列表
        List<IntegralProductDetDto> list = integralProductDto.getList();
        //积分活动id
        Long activityId = integralProductDto.getActivityId();
        //积分活动
        IntegralActivity integralActivity = integralActivityService.getById(activityId);
        if(integralActivity==null){
            throw new ServiceException("积分活动不存在！");
        }
        for (IntegralProductDetDto integralProductDetDto : list) {
            IntegralProduct integralProduct = new IntegralProduct();
            BeanUtils.copyProperties(integralProductDetDto,integralProduct);
            integralProduct.setActivityId(activityId);
            //新增积分商品默认已兑数量为0
            integralProduct.setAlreadyExchangeNum(new BigDecimal(0));
            CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
            integralProduct.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
            integralProduct.setCreateUserName(pcUserInfoDto.getNikeName());
            this.baseMapper.insert(integralProduct);

        }
    }

    @Override
    public List<IntegralProductVo> getList(Long activityId) {
        return this.baseMapper.getListByActivityId(activityId);
    }


    @Override
    public IPage<MiniIntegralProductVo>  getIntegralProduct(MiniIntegralProductParam miniIntegralProductParam) {
        IPage<MiniIntegralProductVo> page = new Page<>(miniIntegralProductParam.getCurrent(), miniIntegralProductParam.getSize());
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3));
        UserIntegralVo userIntegral = remoteMiniAccountService.getUserIntegral(accountInfoDto.getMiniAccountunt().getUserId());
        miniIntegralProductParam.setCurrentIntegral(userIntegral.getCurrentIntegral());
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        miniIntegralProductParam.setDate(date);
        List<MiniIntegralProductVo> list = this.baseMapper.getIntegralProductVo(page, miniIntegralProductParam);

        Map<String, List<MiniIntegralProductVo>> listMap = list.stream().collect(Collectors.groupingBy(MiniIntegralProductVo::getProductId));
        List<MiniIntegralProductVo> list2 = new ArrayList<>(listMap.size());
        for (Map.Entry<String, List<MiniIntegralProductVo>> entry : listMap.entrySet()) {
            List<MiniIntegralProductVo> datalist = entry.getValue();
            MiniIntegralProductVo miniIntegralProductVo = datalist.stream().min(Comparator.comparing(MiniIntegralProductVo::getIntegral)).get();
            list2.add(miniIntegralProductVo);
        }
        page.setRecords(list2);
        return page;

    }
    @Override
    public Boolean batchSubtractExchangeNum(List<OperateExchangeNumParam> list) {
        IntegralGoodsExchangeNumRedisKey exchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
        List<Long> integralProductIdList = list.stream().map(OperateExchangeNumParam::getIntegralProductId).collect(Collectors.toList());
        List<IntegralProduct> integralProductList = baseMapper.selectBatchIds(integralProductIdList);
        Map<Long, IntegralProduct> integralProductMap = integralProductList.stream().collect(Collectors.toMap(IntegralProduct::getId, v -> v));
        integralProductList.clear();
        boolean re = false;
        for (OperateExchangeNumParam operateExchangeNumParam : list) {
            IntegralProduct integralProduct = integralProductMap.get(operateExchangeNumParam.getIntegralProductId());
            BigDecimal subtract = integralProduct.getAllExchangeNum().subtract(integralProduct.getAlreadyExchangeNum());
            if(subtract.compareTo(new BigDecimal(operateExchangeNumParam.getNumber()))>=0){
                integralProduct.setAlreadyExchangeNum(integralProduct.getAlreadyExchangeNum().add(new BigDecimal(operateExchangeNumParam.getNumber())));
                if (this.updateById(integralProduct)) {
                    BigDecimal allExchangeNum = integralProduct.getAllExchangeNum();
                    BigDecimal alreadyExchangeNum = integralProduct.getAlreadyExchangeNum();
                    BigDecimal canExchangeNum = allExchangeNum.subtract(alreadyExchangeNum);
                    //判断用户可兑换数量
                    exchangeNumRedisKey.set(integralProduct.getId().toString(), canExchangeNum.toString());
                    re = true;
                }
            }
        }
        return re;
    }

    @Override
    public Boolean batchIntegralProductAddExchangeNum(List<OperateExchangeNumParam> list) {
        IntegralGoodsExchangeNumRedisKey exchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
        List<Long> integralProductIdList = list.stream().map(OperateExchangeNumParam::getIntegralProductId).collect(Collectors.toList());
        List<IntegralProduct> integralProductList = baseMapper.selectBatchIds(integralProductIdList);
        Map<Long, IntegralProduct> integralProductMap = integralProductList.stream().collect(Collectors.toMap(IntegralProduct::getId, v -> v));
        boolean re = false;
        for (OperateExchangeNumParam operateExchangeNumParam : list) {
            IntegralProduct integralProduct = integralProductMap.get(operateExchangeNumParam.getIntegralProductId());
            integralProduct.setAlreadyExchangeNum(integralProduct.getAlreadyExchangeNum().subtract(new BigDecimal(operateExchangeNumParam.getNumber())));
            if (this.updateById(integralProduct)) {
                BigDecimal allExchangeNum = integralProduct.getAllExchangeNum();
                BigDecimal alreadyExchangeNum = integralProduct.getAlreadyExchangeNum();
                BigDecimal canExchangeNum = allExchangeNum.subtract(alreadyExchangeNum);
                //判断用户可兑换数量
                exchangeNumRedisKey.set(integralProduct.getId().toString(), canExchangeNum.toString());
                re = true;
            }
        }
        return re;
    }

    @Override
    public void updateIntegralProductRedis() {
        IntegralGoodsExchangeNumRedisKey integralGoodsExchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
        LambdaQueryWrapper<IntegralActivity>wrapper = new LambdaQueryWrapper<>();
        List<Integer>list = new ArrayList<>();
        list.add(ProjectStatusEnum.TO_BE_RELEASED.getStatus());
        list.add(ProjectStatusEnum.IN_PROGRESS.getStatus());
        wrapper.in(IntegralActivity::getProjectStatus,list);
        wrapper.isNotNull(IntegralActivity::getStartTime);
        wrapper.isNotNull(IntegralActivity::getEndTime);
        List<IntegralActivity> IntegralActivityList = integralActivityService.list(wrapper);
        if(IntegralActivityList!=null&&IntegralActivityList.size()>0){
            for (IntegralActivity integralActivity : IntegralActivityList) {
                List<IntegralProductVo> IntegralProductVoList = this.getList(integralActivity.getId());
                if(IntegralProductVoList!=null&&IntegralProductVoList.size()>0){
                    for (IntegralProductVo integralProductVo : IntegralProductVoList) {
                        BigDecimal allExchangeNum = integralProductVo.getAllExchangeNum();
                        BigDecimal alreadyExchangeNum = integralProductVo.getAlreadyExchangeNum();
                        BigDecimal canExchangeNum = allExchangeNum.subtract(alreadyExchangeNum);
                        //判断用户可兑换数量
                        integralGoodsExchangeNumRedisKey.set(integralProductVo.getId().toString(), canExchangeNum.toString());
                    }
                }
            }
        }

    }

    @Override
    public List<IntegralProductDetVo> getIntegralProductDetVoByActivityId(Long activityId) {

        return this.baseMapper.getIntegralProductDetVoByActivityId(activityId);
    }

    @Override
    public List<ShowCategoryIntegralProductVo> getShowCategoryIntegralProductVo() {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<ShowCategoryIntegralProductVo> list = this.baseMapper.getShowCategoryIntegralProductVo();
        ShopContextHolder.setShopId(shopId);
        return list;
    }

    @Override
    public List<ShowCategoryIntegralProductVo> getShowCategoryIntegralProductVoByParentId(String parentId) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<ShowCategoryIntegralProductVo> list = this.baseMapper.getShowCategoryIntegralProductVoByParentId(parentId);
        ShopContextHolder.setShopId(shopId);
        return list;
    }

    @Override
    public PageUtils<IntegralProductListVo> getIntegralProductListVo(IntegralProductListParam integralProductListParam) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        IPage<IntegralProductListVo> integralProductListVo = this.baseMapper.getIntegralProductListVo(new Page<>(integralProductListParam.getCurrent(), integralProductListParam.getSize()), integralProductListParam);
        ShopContextHolder.setShopId(shopId);
        return new PageUtils<>(integralProductListVo);
    }




}
