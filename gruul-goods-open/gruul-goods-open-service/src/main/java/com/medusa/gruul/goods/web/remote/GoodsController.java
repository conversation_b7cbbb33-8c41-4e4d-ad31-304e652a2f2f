package com.medusa.gruul.goods.web.remote;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.model.dto.manager.AccountCollectMemberLevelGoodsPriceDto;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeDetParam;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.api.param.OperateExchangeNumParam;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.goods.service.api.IApiProductService;
import com.medusa.gruul.goods.service.api.IApiShoppingCartService;
import com.medusa.gruul.goods.service.manager.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * sku的库存 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@RestController(value = "remoteGoodsController")
@RequestMapping("/")
public class GoodsController {
    @Autowired
    private ISkuStockService skuStockService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IApiProductService apiProductService;
    @Autowired
    private IApiShoppingCartService apiShoppingCartService;
    @Autowired
    private ISupplierService supplierService;
    @Autowired
    private IApiShoppingCartService shoppingCartService;
    @Autowired
    private IMemberLevelGoodsPriceService memberLevelGoodsPriceService;
    @Autowired
    private IProductStockService productStockService;
    @Autowired
    private IIntegralRuleService integralRuleService;
    @Autowired
    private IIntegralProductService integralProductService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private IProductPackageService productPackageService;
    @Autowired
    private IPackageCouponService packageCouponService;
    @Autowired
    private IRewardSchemeService rewardSchemeService;
    @Autowired
    private IRewardSchemeDetService rewardSchemeDetService;
    @Autowired
    private IShowCategoryService showCategoryService;
    @Autowired
    private IProductShowCategoryService productShowCategoryService;


    //=============================================库存部分===================================================

    /**
     * 查询单个库存
     *
     * @param skuId
     * @return com.medusa.gruul.common.core.util.Result<com.medusa.gruul.goods.api.entity.SkuStock>
     * <AUTHOR>
     * @Date 2019/10/4 16:08
     */
    @GetMapping("stock")
    @EscapeLogin
    public SkuStock findSkuStockById(@RequestParam(value = "skuId", required = true) Long skuId) {
        return skuStockService.getById(skuId);
    }

    /**
     * 批量查询库存
     *
     * @param skuIds
     * @return com.medusa.gruul.common.core.util.Result<java.util.Collection               <               com.medusa.gruul.goods.api.entity.SkuStock>>
     * <AUTHOR>
     * @Date 2019/10/4 16:03
     */
    @GetMapping("stock/batch")
    @EscapeLogin
    public Collection<SkuStock> findSkuStockListByIds(@RequestParam(value = "skuIds", required = true) Set<Long> skuIds) {
        return skuStockService.listByIds(skuIds);
    }

    /**
     * 批量查询库存
     *
     * @param skuIds
     * @return com.medusa.gruul.common.core.util.Result<java.util.Collection               <               com.medusa.gruul.goods.api.entity.SkuStock>>
     * <AUTHOR>
     * @Date 2019/10/4 16:03
     */
    @PostMapping("productStockBySkuIds")
    @EscapeLogin
    public List<ProductStock> productStockBySkuIds(@RequestParam(value = "skuIds", required = true) List<Long> skuIds,@RequestParam(value = "warehouseId", required = true) Long warehouseId) {
        LambdaQueryWrapper<ProductStock> lambdaQueryWrapper=new LambdaQueryWrapper<ProductStock>();
        lambdaQueryWrapper.in(ProductStock::getSkuId,skuIds)
                .eq(ProductStock::getWarehouseId,warehouseId);
        return productStockService.list(lambdaQueryWrapper);
    }

    /**
     * 根据Product Id批量查询库存
     *
     * @param productIds
     * @return com.medusa.gruul.common.core.util.Result<java.util.Collection               <               com.medusa.gruul.goods.api.entity.SkuStock>>
     * <AUTHOR>
     * @Date 2019/10/4 16:03
     */
    @GetMapping("product/stock/batch")
    @EscapeLogin
    public Collection<SkuStock> findSkuStockListByProductIds(@RequestParam(value = "productIds", required = true) Set<Long> productIds) {
        return skuStockService.list(new QueryWrapper<SkuStock>().in("product_id", productIds));
    }

    /**
     * 减少库存
     *
     * @param operateStockDto
     * @return com.medusa.gruul.common.core.util.Result<java.lang.Boolean>
     * <AUTHOR>
     * @Date 2019/8/10 15:37
     */
    @PutMapping("stock/subtract")
    @EscapeLogin
    public Boolean subtractStock(@RequestBody OperateStockDto operateStockDto) {
        return skuStockService.subtractStock(operateStockDto);
    }


    /**
     * 批量减少库存（商城使用）
     *
     * @param operateStockDtoList
     * @return com.medusa.gruul.common.core.util.Result<java.lang.Boolean>
     */
    @PutMapping("stock/subtract/batch")
    @EscapeLogin
    public Boolean batchSubtractStock(@RequestBody List<OperateStockDto> operateStockDtoList) {
        return skuStockService.batchSubtractStock(operateStockDtoList);
    }

    /**
     * 核销批量减少库存
     * @param operateStockDtoList
     * @return
     */
    @PutMapping("verify/subtract/batch")
    @EscapeLogin
    public Boolean VerifySubtractStock(@RequestBody List<OperateStockDto> operateStockDtoList) {
        return skuStockService.VerifySubtractStock(operateStockDtoList);
    }

    /**
     * 批量减少库存 库存表(有仓库id的)
     * @param productStockList
     * @return
     */
    @PostMapping("productStock/subtract/batch")
    @EscapeLogin
    public Boolean batchProductSubtractStock(@RequestBody List <ProductStock> productStockList) {
        return productStockService.updateBatchById(productStockList);
    }


    /**
     * 批量归还库存
     *
     * @param operateStockDtoList
     * @return com.medusa.gruul.common.core.util.Result<java.lang.Boolean>
     */
    @PutMapping("stock/revert/batch")
    @EscapeLogin
    public Boolean batchRevertStock(@RequestBody List<OperateStockDto> operateStockDtoList) {
        return skuStockService.batchRevertStock(operateStockDtoList);
    }

    /**
     * 订单结算删除结算的购物车商品数据
     *
     * @param skuIds
     * @param userId
     * @return com.medusa.gruul.common.core.util.Result<java.lang.Boolean>
     */
    @PutMapping("delete/shopping/cart")
    @EscapeLogin
    public Boolean deleteShoppingCartByOrder(@RequestBody List<Long> skuIds, @RequestParam("userId") String userId) {
        return apiShoppingCartService.deleteShoppingCartByOrder(skuIds, userId);
    }

    /**
     * 订单结算删除结算的购物车商品数据（复购价）
     * @param jsonStr
     * @param userId
     * @return
     */
    @GetMapping("delete/shopping/cart/json")
    @EscapeLogin
    Boolean deleteShoppingCartByJson(@RequestParam(value = "jsonStr", required = true) String jsonStr,
                                     @RequestParam(value = "userId", required = true) String userId){
        return apiShoppingCartService.deleteShoppingCartByJson(jsonStr, userId);
    }
    //=============================================商品部分===================================================


    /**
     * 查询单个商品详情
     *
     * @param productId
     * @return com.medusa.gruul.goods.api.entity.Product
     * <AUTHOR>
     * @Date 2019/10/5 10:32
     */
    @GetMapping("product")
    @EscapeLogin
    public ProductVo findProductById(@RequestParam(value = "productId", required = true) Long productId) {
        ProductVo productVo = productService.findProductById(productId);
        return productVo;
    }

    /**
     * 根据商品classCode获取商品
     * @param productCode
     * @return
     */
    @GetMapping("findProductByClassCode")
    @EscapeLogin
    ProductVo findProductByClassCode(@RequestParam(value = "productCode", required = true)String productCode){
        LambdaQueryWrapper<Product>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getClassCode,productCode);
        List<Product> list = productService.list(wrapper);
        ProductVo productVo = new ProductVo();
        if(list!=null&&list.size()>0){
            Product product = list.get(0);
            BeanUtils.copyProperties(product,productVo);
            LocalDateTime packageStartTime = product.getPackageStartTime();
            LocalDateTime packageEndTime = product.getPackageEndTime();
            LocalDateTime packageShowStartTime = product.getPackageShowStartTime();
            LocalDateTime packageShowEndTime = product.getPackageShowEndTime();
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
            if(packageStartTime!=null){
                productVo.setPackageStartTime(df.format(packageStartTime));
            }
            if(packageEndTime!=null){
                productVo.setPackageEndTime(df.format(packageEndTime));
            }
            if(packageShowStartTime!=null){
                productVo.setPackageShowStartTime(df.format(packageShowStartTime));
            }
            if(packageShowEndTime!=null){
                productVo.setPackageShowEndTime(df.format(packageShowEndTime));
            }
        }
        return productVo;
    }


    @GetMapping("get/productPackage")
    @EscapeLogin
    public  List<ProductAllPackageVo>  findProductPackageByPackageId(@RequestParam(value = "packageId", required = true) Long packageId){
        List<ProductAllPackageVo>list = productPackageService.findProductPackageByPackageId(packageId);
        return list;

    }

    @GetMapping("get/packageCoupon")
    @EscapeLogin
    public  List<PackageCoupon>  findPackageCouponByPackageId(@RequestParam(value = "packageId", required = true) Long packageId){
        LambdaQueryWrapper<PackageCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageCoupon::getPackageId,packageId);
        List<PackageCoupon> list = packageCouponService.list(wrapper);
        return list;

    }


    /**
     * 批量查询多个商品详情
     *
     * @param productIds
     * @return java.util.Collection<com.medusa.gruul.goods.api.entity.Product>
     * <AUTHOR>
     * @Date 2019/10/5 10:33
     */
    @GetMapping("product/batch")
    @EscapeLogin
    public Collection<Product> findProductListByIds(@RequestParam(value = "productIds", required = true) Set<Long> productIds) {
        return productService.listByIds(productIds);
    }

    /**
     * 查询收藏里的商品
     * @param productIdList
     * @return
     */

    @GetMapping("product/accountCollectGoodList")
    @EscapeLogin
    public List<Product> accountCollectGoodList(@RequestParam(value = "productIdList", required = true) List<Long> productIdList){
        LambdaQueryWrapper<Product> lambdaQueryWrapper=new LambdaQueryWrapper<Product>();
        lambdaQueryWrapper.in(Product::getId,productIdList);
        return productService.list(lambdaQueryWrapper);
    }

    /**
     * 查询会员价格表的商品数据
     *
     * @param memberId
     * @return MemberGoodsPrice
     * <AUTHOR>
     * @Date 2022/02/17
     */
    @GetMapping("memberLevelGoodsPrice")
    @EscapeLogin
    public List<AccountCollectMemberLevelGoodsPriceDto> memberLevelGoodsPrice(@RequestParam(value = "memberId", required = true) String memberId, @RequestParam(value = "productIds", required = true) List<Long> productIds) {

        return memberLevelGoodsPriceService.selecetMemberLevelGoodsPrice(memberId,productIds);
    }

    /**
     * 查询会员等级关联的商品会员等级价格表的记录数
     * @param memberLevelId
     * @return
     */
    @GetMapping("getCountByMemberLevelId")
    @EscapeLogin
    public Integer getCountByMemberLevelId(@RequestParam(value = "memberLevelId", required = true) String memberLevelId) {
        return memberLevelGoodsPriceService.getCountByMemberLevelId(memberLevelId);
    }

    /**
     * 获取单个sku关联商品
     *
     * @param skuIds
     * @return ItemVo
     * <AUTHOR>
     * @Date 2019/12/27
     */
    @GetMapping("itemVo")
    @EscapeLogin
    public List<ItemVo> findItemVoByIds(@RequestParam(value = "skuIds", required = true) List<Long> skuIds) {
        return productService.findItemVoByIds(skuIds);
    }

    @GetMapping("findItemVoByItemVo")
    @EscapeLogin
    public  List<ItemVo> findItemVoByItemVo(@RequestParam(value = "jsonStr", required = true) String jsonStr){
        return productService.findItemVoByItemVo(jsonStr);
    }

    /**
     * 查询会员价格表的商品数据
     *
     * @param memberId
     * @return MemberGoodsPrice
     * <AUTHOR>
     * @Date 2022/02/17
     */
    @GetMapping("memberGoodsPrice")
    @EscapeLogin
    public List<MemberLevelGoodsPrice> selectMemberGoodsPrice(@RequestParam(value = "memberId", required = true) String memberId) {
        return productService.selectMemberGoodsPrice(memberId);
    }

    /**
     * 查询商品（或者商品规格）会员等级价格信息
     * @param memberId
     * @param productId
     * @param skuId
     * @return
     */
    @GetMapping("selectMemberGoodsPriceByProductId")
    @EscapeLogin
    public List<MemberLevelGoodsPrice> selectMemberGoodsPriceByProductId(@RequestParam(value = "memberId", required = true) String memberId,
                                                                         @RequestParam(value = "productId") Long productId, @RequestParam(value = "skuId") Long skuId) {
        return productService.selectMemberGoodsPriceByProductId(memberId, productId, skuId);
    }

    /**
     * 查询商品（或者商品规格）会员等级复购价格信息
     * @param memberId
     * @param productId
     * @param skuId
     * @return
     */
    @GetMapping("selectMemberGoodsAgainPriceByProductId")
    @EscapeLogin
    public List<MemberLevelGoodsAgainPrice> selectMemberGoodsAgainPriceByProductId(@RequestParam(value = "memberId", required = true) String memberId,
                                                                                   @RequestParam(value = "productId") Long productId, @RequestParam(value = "skuId") Long skuId) {
        return productService.selectMemberGoodsAgainPriceByProductId(memberId, productId, skuId);
    }

    @GetMapping("memberGoodsAgainPrice")
    @EscapeLogin
    List<MemberLevelGoodsAgainPrice> selectMemberGoodsAgainPrice(@RequestParam(value = "memberId", required = true) String memberId){
        return productService.selectMemberGoodsAgainPrice(memberId);
    }

    /**
     * 批量更新商品得分信息
     * @param productList
     * @return
     */
    @PostMapping("product/score/batch")
    @EscapeLogin
    public Boolean batchUpdateProductScore(@RequestBody List <Product> productList){
        return productService.updateScoreBatch(productList);
    }

    //=============================================物流部分===================================================


    /**
     * 查询运费模版id是否被商品使用--物流服务调用
     *
     * @param templateId
     * @return Boolean
     * <AUTHOR>
     * @Date 2020/03/14 10:33
     */
    @GetMapping("get/logistics/check/product")
    @EscapeLogin
    public Boolean checkProductByTemplateId(@RequestParam(value = "templateId", required = true) Long templateId) {
        return productService.checkProductByTemplateId(templateId);
    }

    /**
     * 批量查询多个商品详情
     *
     * @param productIds
     * @return List<DiscountProductVo>
     */
    @GetMapping("get/discount/product/type/list")
    @EscapeLogin
    public List<DiscountProductVo> findDiscountProductTypeList(@RequestParam(value = "productIds", required = true) List<Long> productIds) {
        return productService.getDiscountProductTypeList(productIds);
    }

    /**
     * 查询上架商品的数量
     *
     * @return Integer
     */
    @GetMapping("get/product/count")
    @EscapeLogin
    public Integer findProductCount() {
        return productService.getProductCount();
    }


    //=============================================数据服务接口===================================================

    /**
     * 根据供应商id数组查询供应商信息
     *
     * @param supplierIds
     * @return List<SupplierVo>
     */
    @GetMapping("get/data/supplier/list")
    @EscapeLogin
    public List<SupplierVo> findDataSetSupplierList(@RequestParam(value = "supplierIds", required = true) List<Long> supplierIds){
        return supplierService.getDataSetSupplierList(supplierIds);
    }

    /**
     * 根据积分规则类型获取积分规则
     * @param ruleType
     * @return
     */
    @GetMapping("get/data/integralRule/ruleType")
    @EscapeLogin
    public IntegralRuleVo getIntegralRuleVoByRuleType(@RequestParam(value = "ruleType", required = true)Integer ruleType,
                                                      @RequestParam(value = "tenantId", required = true)String tenantId){
        return integralRuleService.getIntegralRuleVoByRuleType(ruleType,tenantId);
    }

    @GetMapping("get/data/IntegralProduct/id")
    @EscapeLogin
    public IntegralProduct getIntegralProduct(@RequestParam(value = "id", required = true) String id) {
        // 通过id进行查询，取消自动加入店铺id进行查询，否则提交订单的时候会提示积分商品不存在
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        IntegralProduct p = integralProductService.getById(id);
        ShopContextHolder.setShopId(oldShopId);
        return p;
    }

    /**
     * 批量减少商品可兑换数量
     * @param list
     * @return
     */
    @PostMapping("integralProduct/subtract/batch")
    @EscapeLogin
    public Boolean batchIntegralProductSubtractExchangeNum(@RequestBody List <OperateExchangeNumParam> list) {
        // 通过id进行查询，取消自动加入店铺id进行查询，否则提交订单的时候会提示积分商品不存在
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        boolean result = integralProductService.batchSubtractExchangeNum(list);
        ShopContextHolder.setShopId(oldShopId);
        return result;
    }

    /**
     * 批量增加商品可兑换数量
     * @param list
     * @return
     */
    @PostMapping("integralProduct/add/batch")
    @EscapeLogin
    public Boolean batchIntegralProductAddExchangeNum(@RequestBody List <OperateExchangeNumParam> list) {
        return integralProductService.batchIntegralProductAddExchangeNum(list);
    }

    //=============================================数据服务接口===================================================

    /**
     * 获取默认仓库
     * @return
     */
    @GetMapping("get/data/warehouseStock")
    @EscapeLogin
    Warehouse getWarehouseStock(@RequestParam(value = "shopId", required = false)String shopId){
        ShopContextHolder.setShopId(shopId);
        LambdaQueryWrapper<Warehouse>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Warehouse::getStockFlag,1);
        wrapper.eq(Warehouse::getDeleted,0);
        wrapper.eq(Warehouse::getState,1);

        List<Warehouse> list = warehouseService.list(wrapper);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }else{
            return null;
        }
    }

    /**
     * 获取默认仓库
     * @return
     */
    @GetMapping("get/data/warehouse/id")
    @EscapeLogin
    Warehouse getWarehouseById(@RequestParam(value = "warehouseId", required = true)Long warehouseId){
       return warehouseService.getById(warehouseId);
    }

    /**
     * 根据classCode获取仓库
     * @param classCode
     * @return
     */
    @GetMapping("get/data/warehouse/classCode")
    @EscapeLogin
    List<Warehouse>getWarehouseByClassCode(@RequestParam(value = "classCode", required = true)String classCode){
        LambdaQueryWrapper<Warehouse>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Warehouse::getClassCode,classCode);
        return warehouseService.list(wrapper);
    }

    /**
     * 通过skuId查询单个商品详情
     *
     * @param skuId
     */
    @GetMapping("get/product/skuId")
    @EscapeLogin
    public ProductVo findProductBySkuId(@RequestParam(value = "skuId", required = true) Long skuId) {
        SkuStock skuStock = skuStockService.getById(skuId);
        Product product = productService.getById(skuStock.getProductId());
        ProductVo productVo = new ProductVo();
        BeanUtils.copyProperties(product,productVo);
        LocalDateTime packageStartTime = product.getPackageStartTime();
        LocalDateTime packageEndTime = product.getPackageEndTime();
        LocalDateTime packageShowStartTime = product.getPackageShowStartTime();
        LocalDateTime packageShowEndTime = product.getPackageShowEndTime();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
        if(packageStartTime!=null){
            productVo.setPackageStartTime(df.format(packageStartTime));
        }
        if(packageEndTime!=null){
            productVo.setPackageEndTime(df.format(packageEndTime));
        }
        if(packageShowStartTime!=null){
            productVo.setPackageShowStartTime(df.format(packageShowStartTime));
        }
        if(packageShowEndTime!=null){
            productVo.setPackageShowEndTime(df.format(packageShowEndTime));
        }
        return productVo;
    }

    /**
     * 通过skuId查询关联商品详情
     *
     * @param skuId
     */
    @GetMapping("get/linkProduct/skuId")
    @EscapeLogin
    public ProductVo findLinkProductBySkuId(@RequestParam(value = "skuId", required = true) Long skuId) {
        SkuStock skuStock = skuStockService.getById(skuId);
        ProductVo productVo = null;
        if(null != skuStock.getLinkProductId()){
            Product product = productService.getById(skuStock.getLinkProductId());
            if(null != product){
                productVo = new ProductVo();
                BeanUtils.copyProperties(product,productVo);
                LocalDateTime packageStartTime = product.getPackageStartTime();
                LocalDateTime packageEndTime = product.getPackageEndTime();
                LocalDateTime packageShowStartTime = product.getPackageShowStartTime();
                LocalDateTime packageShowEndTime = product.getPackageShowEndTime();
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
                if(packageStartTime!=null){
                    productVo.setPackageStartTime(df.format(packageStartTime));
                }
                if(packageEndTime!=null){
                    productVo.setPackageEndTime(df.format(packageEndTime));
                }
                if(packageShowStartTime!=null){
                    productVo.setPackageShowStartTime(df.format(packageShowStartTime));
                }
                if(packageShowEndTime!=null){
                    productVo.setPackageShowEndTime(df.format(packageShowEndTime));
                }
            }

        }

        return productVo;
    }

    /**
     * 根据订单完成时间，奖励活动类型获取奖励活动记录
     * @param completeTime
     * @param type
     * @return
     */
    @GetMapping("get/rewardScheme/data")
    @EscapeLogin
    List<RewardSchemeDataVo> getRewardSchemeData(@RequestParam(value = "completeTime", required = true)String completeTime,
                                                 @RequestParam(value = "type", required = true)Integer type){
        rewardSchemeService.updateStatus();

        List<RewardSchemeDataVo> dataList =  rewardSchemeDetService.getRewardSchemeData(completeTime,type);
        return dataList;
    }

    /**
     * 根据id查询商品分类
     * @param categoryId
     * @return
     */
    @GetMapping("category")
    @EscapeLogin
    CategoryVo findCategoryById(@RequestParam(value = "categoryId", required = true) String categoryId){
        CategoryVo categoryVo = showCategoryService.getCategoryById(categoryId);
        return categoryVo;
    }

    @GetMapping("get/skuStock/categoryId")
    @EscapeLogin
    List<SkuStock>findSkuStockByCategoryId(@RequestParam(value = "categoryId", required = true) String categoryId){
        List<SkuStock> list= skuStockService.getByCategoryId(categoryId);
        return list;
    }

    /**
     * 获取奖励方案明细
     * @param param
     * @return
     */
    @PostMapping("get/rewardScheme/det")
    @EscapeLogin
    List<RewardSchemeDetVo> getRewardSchemeDet(@RequestBody RewardSchemeDetParam param){
        List<RewardSchemeDetVo> rewardSchemeDetList = rewardSchemeDetService.getRewardSchemeDetVo(param);
        return rewardSchemeDetList;
    }

    @GetMapping("get/getAddProductPackageList")
    @EscapeLogin
    List<AddProductPackageVo> getAddProductPackageList(){
        List<AddProductPackageVo> list = productService.getAddProductPackageList();
        return list;
    }
}
