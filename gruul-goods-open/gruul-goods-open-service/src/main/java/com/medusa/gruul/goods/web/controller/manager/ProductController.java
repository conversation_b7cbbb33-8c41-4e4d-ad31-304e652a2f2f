package com.medusa.gruul.goods.web.controller.manager;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.model.dto.manager.ProductDto;
import com.medusa.gruul.goods.api.model.dto.manager.ProductShowCategoryDto;
import com.medusa.gruul.goods.api.model.param.manager.*;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.service.manager.IProductPackageService;
import com.medusa.gruul.goods.service.manager.IProductService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@RestController
@RequestMapping("/manager/product")
public class ProductController {

    @Autowired
    private IProductService productService;
    @Autowired
    private IProductPackageService productPackageService;


    /**
     * 商品信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "商品信息列表")
    @EscapeLogin
    public Result<PageUtils<ProductVo>> getProductList(ProductParam productParam) {
        PageUtils<ProductVo> pageUtils = new PageUtils(productService.getProductList(productParam));
        return Result.ok(pageUtils);
    }



    /**
     * 分页获取可添加的权益包商品
     * @param addPackageProductParam
     * @return
     */
    @PostMapping("/getAddProductPackageVo")
    @ApiOperation(value = "分页获取可添加的权益包商品")
    public Result<PageUtils<AddProductPackageVo>> getAddProductPackageVo(@RequestBody AddPackageProductParam addPackageProductParam) {
        PageUtils<AddProductPackageVo> pageUtils = productService.getAddProductPackageVo(addPackageProductParam);
        return Result.ok(pageUtils);
    }

    /**
     * 分页获取权益包明细
     * @param param
     * @return
     */
    @PostMapping("/getProductPackageDetailVo")
    @ApiOperation(value = "分页获取可添加的权益包商品")
    public Result<PageUtils<ProductPackageDetailVo>> getProductPackageDetailVo(@RequestBody ProductPackageDetailParam param) {
        PageUtils<ProductPackageDetailVo> pageUtils = productPackageService.getProductPackageDetailVo(param);
        return Result.ok(pageUtils);
    }



    /**
     * 获取单个商品基础信息
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "单个商品基础信息")
    public Result<ProductVo> getProductById(@ApiParam(value = "商品id", required = true) @PathVariable("id") Long id) {
        ProductVo productVo = productService.getProductById(id);
        return Result.ok(productVo);
    }


    /**
     * 获取单个商品sku信息
     */
    @GetMapping("/get/sku/stock/{id}")
    @ApiOperation(value = "单个商品sku信息")
    public Result<List<SkuStockVo>> getSkuStockAndMemberPriceById(@ApiParam(value = "商品id", required = true) @PathVariable("id") Long id) {
        List<SkuStockVo> skuStockVos = productService.getSkuStockAndMemberPriceById(id);
        return Result.ok(skuStockVos);
    }

    /**
     * 商品发布
     */
    @PostMapping("/issue")
    @ApiOperation(value = "商品发布")
    public Result issueProduct(@RequestBody @Validated ProductDto productDto) {
        productService.issueProduct(productDto);
        return Result.ok(productDto);
    }

    /**
     * 商品信息修改
     */
    @PutMapping("/update")
    @ApiOperation(value = "商品信息修改")
    public Result updateProduct(@RequestBody @Validated ProductDto productDto) {
        productService.updateProduct(productDto);
        return Result.ok();
    }

    /**
     * 商品展示分类批量设置
     */
    @PostMapping("/updateProductShowCategory/{productIds}")
    @ApiOperation(value = "商品展示分类批量设置")
    public Result updateProductShowCategory(@ApiParam(value = "商品ids", required = true) @PathVariable(name = "productIds") Long[] productIds,
                                            @RequestBody List<ProductShowCategoryDto> productShowCategoryDtos) {
        productService.updateProductShowCategory(productShowCategoryDtos, productIds);
        return Result.ok();
    }

    /**
     * 商品专区变更
     */
    @PutMapping("/updateSaleMode/{saleMode}")
    @ApiOperation(value = "商品专区变更(0--商超系统，2--限时秒杀)")
    public Result updateProductSaleMode(@ApiParam(value = "商品ids", required = true) @RequestBody Long[] ids,
                                        @ApiParam(value = "商品专区值", required = true) @PathVariable("saleMode") Long saleMode) {
        productService.updateProductSaleMode(ids, saleMode);
        return Result.ok();
    }


    /**
     * 商品上下架
     */
    @PutMapping("/updateStatus/{status}/{productType}")
    @ApiOperation(value = "商品上下架")
    public Result updateProductStatus(@ApiParam(value = "商品ids", required = true) @RequestBody Long[] ids,
                                      @ApiParam(value = "商品状态值", required = true) @PathVariable("status") Integer status,
                                      @ApiParam(value = "商品类型值", required = true) @PathVariable("productType") Integer productType) {
        productService.updateProductStatus(ids, status, productType);
        return Result.ok();
    }

    /**
     * 商品信息删除
     */
    @DeleteMapping("/delete/{ids}")
    @ApiOperation(value = "商品信息删除")
    public Result deleteProductList(@ApiParam(value = "商品ids", required = true) @PathVariable(name = "ids") Long[] ids) {
        try {
            productService.deleteProductList(ids);
            return Result.ok();
        }catch (Exception e){
            return Result.failed(e.getMessage());
        }

    }

    //=============================================商品组件根据商品集合匹配未删除的商品===================================================

    /**
     * 组件获取所有商品信息列表
     */
    @GetMapping("/discount/list")
    @ApiOperation(value = "组件获取所有商品信息列表")
    public Result<PageUtils<DiscountProductVo>> getDiscountProductList(DiscountProductParam discountProductParam) {
        PageUtils<DiscountProductVo> pageUtils = new PageUtils(productService.getDiscountProductList(discountProductParam));
        return Result.ok(pageUtils);
    }

    /**
     * 根据商品数组匹配未删除的商品
     *
     * @param ids
     * @return
     */
    @GetMapping("/alive/product/list/{ids}")
    @ApiOperation(value = "根据商品数组匹配未删除的商品")
    public Result<List<DiscountProductVo>> getAliveProductList(@ApiParam(value = "商品ids", required = true) @PathVariable(name = "ids") Long[] ids) {
        List<DiscountProductVo> saveIds = productService.getAliveProductList(ids);
        return Result.ok(saveIds);
    }

    /**
     * 获取商品是否显示
     * @param id
     * @return
     */
    @GetMapping("/getProductShowFlag")
    @ApiOperation(value = "获取商品是否显示")
    public Result<Boolean> getProductShowFlag(@RequestParam(name="id",required=true) Long id){
        Boolean productShowFlag = productService.getProductShowFlag(id);
        return Result.ok(productShowFlag);
    }

    /**
     * 淘宝商品csv文件导入
     *
     * @param file 商品csv文件
     * @return Result
     */
    @PostMapping("/import/csv/product")
    @ApiOperation(value = "淘宝商品csv文件导入")
    public Result importCsvProduct(MultipartFile file) {
        return productService.importCsvProduct(file);
    }

    /**
     * 素材库商品信息列表
     */
    @GetMapping("/csv/list")
    @ApiOperation(value = "商品信息列表")
    public Result<PageUtils<ProductVo>> getCsvProductList(ProductParam productParam) {
        PageUtils<ProductVo> pageUtils = new PageUtils(productService.getCsvProductList(productParam));
        return Result.ok(pageUtils);
    }
    /**
     * 获取商品编号
     */
    @PostMapping("/getGoodsCode")
    @ApiOperation(value = "获取商品编号")
    public Result getGoodsCode() {
        //获取最大商品编号
       String goodsCode=productService.getGoodsCode();
       if(goodsCode!=null && !"".equals(goodsCode)){
          String code=goodsCode.substring(2);
          Long codenumber=Long.parseLong(code);
           goodsCode="SP"+(codenumber+1);
       }else{
           goodsCode="SP10000001";
       }
        return Result.ok(goodsCode);
    }

    /**
     * 获取权益包商品编号
     * @return
     */
    @PostMapping("/getPackageGoodsCode")
    @ApiOperation(value = "获取权益包商品编号")
    public Result getPackageGoodsCode() {
        //获取最大商品编号
        String packageGoodsCode=productService.getPackageGoodsCode();
        if(packageGoodsCode!=null && !"".equals(packageGoodsCode)){
            String code=packageGoodsCode.substring(2);
            Long codenumber=Long.parseLong(code);
            packageGoodsCode="TB"+(codenumber+1);
        }else{
            packageGoodsCode="TB10000001";
        }
        return Result.ok(packageGoodsCode);
    }

    /**
     * 根据商品名字、编号获取商品详情
     */
    @PostMapping("/getByNameOrNumber")
    @ApiOperation(value = "根据商品名字、编号获取商品详情")
    public Result getByNameOrNumber(@RequestBody ProductDto productDto) {
        PageUtils<ProductVo> pageProductVo = new PageUtils(productService.getByNameOrNumber(productDto) );
        return Result.ok(pageProductVo);
    }

    /**
     * 获取参加活动商品
     * @param param
     * @return
     */
    @PostMapping("/getActivityProduct")
    @ApiOperation(value = "获取参加活动商品")
    public Result getActivityProduct(@RequestBody ActivityProductParam param){
        PageUtils<ActivityProductVo> addProductPackageVo = productService.getActivityProduct(param);
        return Result.ok(addProductPackageVo);
    }

    /**
     * 获取sku-商品信息分页
     */
    @GetMapping("/querySkuProductList")
    @ApiOperation(value = "获取sku-商品信息分页")
    public Result<PageUtils<ProductVo>> querySkuProductList(ProductParam productParam) {
        PageUtils<ProductVo> pageUtils = new PageUtils(productService.querySkuProductList(productParam));
        return Result.ok(pageUtils);
    }

    /**
     * 获取可选择商品枚举
     * @return
     */
    @GetMapping("/getProductMap")
    @ApiOperation(value = "获取可选择商品枚举")
    public Result<List<Map>> getProductMap() {
        List<Map> productMap = productService.getProductMap();
        return Result.ok(productMap);
    }

    /**
     * 商品批量修改运费模板
     */
    @PutMapping("/updateProductFreightTemplate/{freightTemplateId}")
    @ApiOperation(value = "商品批量修改运费模板")
    public Result updateProductFreightTemplate(@ApiParam(value = "商品ids", required = true) @RequestBody Long[] ids,
                                               @ApiParam(value = "运费模板ID", required = true) @PathVariable("freightTemplateId")  Long freightTemplateId) {
        productService.updateProductFreightTemplate(ids, freightTemplateId);
        return Result.ok();
    }
    /**
     * 导出商品列表
     */
    @GetMapping("/export")
    @ApiOperation(value = "导出商品列表")
    public void exportProductList(ProductParam productParam) {
        productService.exportProduct(productParam);
    }
    /**
     * 导出权益包列表
     */
    @GetMapping("/exportProductPackage")
    @ApiOperation(value = "导出权益包列表")
    public void exportProductPackageList(ProductParam productParam) {
        productService.exportProductPackageList(productParam);
    }

    /**
     * 导出权益明细列表
     * @param param
     * @return
     */
    @PostMapping("/exportProductPackageDetailVo")
    @ApiOperation(value = "导出权益明细列表")
    public void  exportProductPackageDetailVo(@RequestBody ProductPackageDetailParam param) {
        productPackageService.exportProductPackageDetailVo(param);
    }

}
