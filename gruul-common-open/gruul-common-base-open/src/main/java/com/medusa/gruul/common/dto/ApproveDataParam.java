package com.medusa.gruul.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 审核数据param
 * @Date: Created in 17:05 2023/8/30
 */
@ApiModel(value = "ApproveDataParam 实体", description = "审核数据param")
@Data
public class ApproveDataParam {

    /**
     * 数据id
     */
    @ApiModelProperty(value = "数据id")
    @NotNull(message = "数据id不能为空")
    private String id;
    /**
     * 状态:0->审核中;1->审核通过;-1->已驳回;-2->提现失败;2->提现成功
     */
    @ApiModelProperty(value = "状态:0->审核中;1->审核通过;-1->已驳回;-2->提现失败;2->提现成功")
    @NotNull(message = "审批状态不能为空")
    private String approvalStatus;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private String approvalReason;

}
