package com.medusa.gruul.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SendCodeVerifyDto {

    @ApiModelProperty(value = "验证码")
    private String  code;

    @ApiModelProperty(value = "手机号")
    private String  phone;

    @ApiModelProperty(value = "校验类型,1001-短信登录校验," +
            "1002-账号注册验证手机号," +
            "1003-用户手机号换绑校验," +
            "1004-用户修改密码," +
            "1005-用户信息修改," +
            "1006-用户删除自身店铺," +
            "1007-用户忘记密码找回," +
            "1008-代理申请," +
            "1009-代理银行卡手机号校验," +
            "1010-代理银行卡手机号换绑," +
            "1011-代理账号换绑," +
            "1012-代理修改密码," +
            "1013-修改支付密码," +
            "1014-验证代用户下单," +
            "1015-支付短信验证码," +
            "")
    private Integer  type;

    public SendCodeVerifyDto() {
    }

    public SendCodeVerifyDto(String code, String phone, Integer type) {
        this.code = code;
        this.phone = phone;
        this.type = type;
    }
}
