package com.medusa.gruul.common.core.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.medusa.gruul.common.core.annotation.AutoFill;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动填充处理器 - 终极简化版
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
public class AutoFillProcessor {
    
    /**
     * 处理对象的自动填充
     * 支持单个对象、List、PageUtils
     */
    public static void process(Object result) {
        if (result == null) return;
        
        List<?> dataList = extractList(result);
        if (CollectionUtils.isEmpty(dataList)) return;
        
        processAutoFillFields(dataList);
    }
    
    /**
     * 提取数据列表
     */
    private static List<?> extractList(Object result) {
        if (result instanceof List) {
            return (List<?>) result;
        } else if (result instanceof PageUtils) {
            return ((PageUtils<?>) result).getList();
        } else {
            return Collections.singletonList(result);
        }
    }
    
    /**
     * 处理自动填充字段
     */
    private static void processAutoFillFields(List<?> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return;
        
        Class<?> clazz = dataList.get(0).getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            AutoFill autoFill = field.getAnnotation(AutoFill.class);
            if (autoFill != null) {
                processField(dataList, field, autoFill);
            }
        }
    }
    
    /**
     * 处理单个字段
     */
    private static void processField(List<?> dataList, Field field, AutoFill autoFill) {
        try {
            // 1. 获取关联键字段
            Field keyField = ReflectionUtils.findField(dataList.get(0).getClass(), autoFill.key());
            if (keyField == null) {
                log.warn("找不到关联键字段: {}", autoFill.key());
                return;
            }
            
            // 2. 提取关联键
            List<Object> keys = extractKeys(dataList, keyField);
            if (CollectionUtils.isEmpty(keys)) return;
            
            // 3. 获取服务和方法
            String serviceName = getServiceName(field, autoFill);
            Object service = SpringContextHolder.getBean(serviceName);
            if (service == null) {
                log.warn("找不到服务: {}", serviceName);
                return;
            }
            
            Method method = findMethod(service, field, keys.get(0).getClass());
            if (method == null) {
                log.warn("找不到批量查询方法: {}", serviceName);
                return;
            }
            
            // 4. 批量查询数据
            Object result = method.invoke(service, keys);
            if (!(result instanceof Map)) {
                log.warn("方法返回值不是Map类型: {}", method.getName());
                return;
            }
            
            Map<Object, ?> dataMap = (Map<Object, ?>) result;
            
            // 5. 填充数据
            fillData(dataList, keyField, field, dataMap);
            
        } catch (Exception e) {
            log.error("自动填充失败: {}", field.getName(), e);
        }
    }
    
    /**
     * 获取服务名称
     */
    private static String getServiceName(Field field, AutoFill autoFill) {
        if (StringUtils.hasText(autoFill.service())) {
            return autoFill.service();
        }
        
        // 自动推导：positionList -> positionService
        String fieldName = field.getName();
        if (fieldName.endsWith("List")) {
            fieldName = fieldName.substring(0, fieldName.length() - 4);
        }
        return fieldName + "Service";
    }
    
    /**
     * 查找批量查询方法
     */
    private static Method findMethod(Object service, Field field, Class<?> keyType) {
        String fieldName = field.getName();
        if (fieldName.endsWith("List")) {
            fieldName = fieldName.substring(0, fieldName.length() - 4);
        }
        
        // 尝试多种方法名模式
        String[] patterns = {
            "get" + capitalize(fieldName) + "sByEmployeeIds",
            "get" + capitalize(fieldName) + "sBy" + "Ids", 
            "batch" + capitalize(fieldName),
            "findBy" + "Ids"
        };
        
        Method[] methods = service.getClass().getMethods();
        for (String pattern : patterns) {
            for (Method method : methods) {
                if (method.getName().equals(pattern) && 
                    method.getParameterCount() == 1 &&
                    List.class.isAssignableFrom(method.getParameterTypes()[0])) {
                    return method;
                }
            }
        }
        
        // 如果找不到，尝试包含关键字的方法
        for (Method method : methods) {
            String methodName = method.getName().toLowerCase();
            if ((methodName.contains("batch") || methodName.contains("getby") || methodName.contains("findby")) &&
                method.getParameterCount() == 1 &&
                List.class.isAssignableFrom(method.getParameterTypes()[0])) {
                return method;
            }
        }
        
        return null;
    }
    
    /**
     * 提取关联键
     */
    private static List<Object> extractKeys(List<?> dataList, Field keyField) {
        keyField.setAccessible(true);
        return dataList.stream()
                .map(item -> {
                    try {
                        return keyField.get(item);
                    } catch (IllegalAccessException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 填充数据
     */
    private static void fillData(List<?> dataList, Field keyField, Field targetField, Map<Object, ?> dataMap) {
        keyField.setAccessible(true);
        targetField.setAccessible(true);
        
        // 判断是一对一还是一对多
        boolean isOneToMany = isListType(targetField);
        
        for (Object item : dataList) {
            try {
                Object key = keyField.get(item);
                if (key != null) {
                    Object data = dataMap.get(key);
                    if (data != null) {
                        if (isOneToMany) {
                            // 一对多
                            if (data instanceof List) {
                                targetField.set(item, data);
                            } else {
                                targetField.set(item, Collections.singletonList(data));
                            }
                        } else {
                            // 一对一
                            if (data instanceof List && !((List<?>) data).isEmpty()) {
                                targetField.set(item, ((List<?>) data).get(0));
                            } else {
                                targetField.set(item, data);
                            }
                        }
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("填充数据失败", e);
            }
        }
    }
    
    /**
     * 判断字段是否为List类型
     */
    private static boolean isListType(Field field) {
        return List.class.isAssignableFrom(field.getType());
    }
    
    /**
     * 首字母大写
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
