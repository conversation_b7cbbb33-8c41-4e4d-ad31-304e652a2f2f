package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:04 2025/5/20
 */
@Getter
public enum MemberFlagEnum {

    NO(0, "否"),
    YES(1, "是");
    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    MemberFlagEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
