package com.medusa.gruul.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自动填充注解 - 终极简化版
 * 只需要一个注解，自动推导一切
 * 
 * 使用示例：
 * @AutoFill
 * private List<PlatformPositionVo> positionList;
 * 
 * 约定：
 * 1. 字段名positionList -> 推导出关联键为id
 * 2. 自动查找positionService.getPositionsByEmployeeIds方法
 * 3. 自动判断一对一还是一对多关系
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoFill {
    
    /**
     * 服务名称，如果不指定则自动推导
     * 例如：positionList -> positionService
     */
    String service() default "";
    
    /**
     * 关联键字段，如果不指定则使用id
     */
    String key() default "id";
}
