package com.medusa.gruul.common.core.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateUtils {

    private static final String []WEEK_CN = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    public static LocalDateTime timestampCoverLocalDateTime(long timestamp) {
        DateTime expiresIn = DateUtil.date(timestamp);
        return expiresIn.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static long localDateTimeCoverTimestamp(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static String formatWeek(String week){
        String result = "";
        String weeks[] = week.split(",");
        for(String w : weeks){

        }
        return null;
    }

    /**
     * 返回时间是周几
     * @param sourceDate 时间
     * @return
     */
    public static String weekDay(Date sourceDate){
        int week = DateUtil.dayOfWeek(sourceDate);
        return WEEK_CN[week - 1];
    }

}
