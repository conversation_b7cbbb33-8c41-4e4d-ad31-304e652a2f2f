<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.medusa</groupId>
    <artifactId>gruul</artifactId>
    <version>0.1</version>
    <packaging>pom</packaging>


    <modules>
        <module>gruul-common-open</module>
        <module>gruul-gateway-open</module>
        <module>gruul-sms-open</module>
        <module>gruul-oss-open</module>
        <module>gruul-platform-open</module>
        <module>gruul-shops-open</module>
        <module>gruul-payment-open</module>
        <module>gruul-account-open</module>
        <module>gruul-afs-open</module>
        <module>gruul-goods-open</module>
        <module>gruul-logistics-open</module>
        <module>gruul-order-open</module>
        <module>gruul-api-open</module>
    </modules>

    <properties>
        <gruul.version>0.1</gruul.version>
        <spring-boot.version>2.1.6.RELEASE</spring-boot.version>
        <spring-cloud.version>Greenwich.SR2</spring-cloud.version>
        <spring-cloud-alibaba.version>0.9.0.RELEASE</spring-cloud-alibaba.version>
        <spring-platform.version>Cairo-SR8</spring-platform.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot-admin.version>2.1.6</spring-boot-admin.version>
        <hutool.version>5.3.10</hutool.version>
        <swagger.fox.version>2.9.2</swagger.fox.version>
        <swagger-bootstrap-ui.version>1.9.4</swagger-bootstrap-ui.version>
        <curator.version>2.10.0</curator.version>
        <velocity.version>1.7</velocity.version>
        <jjwt.version>0.9.1</jjwt.version>
        <google.zxing.version>3.3.1</google.zxing.version>

        <lcn.version>5.0.2.RELEASE</lcn.version>
        <ttl.version>2.10.2</ttl.version>
        <!--  WxJava （微信开发 Java SDK） 总版本号     -->
        <wxjava.version>3.8.7.B</wxjava.version>

        <mp.version>3.4.0</mp.version>
        <statemachine.version>2.1.3.RELEASE</statemachine.version>
        <druid.version>1.1.20</druid.version>
        <mysql.connector.version>8.0.16</mysql.connector.version>
        <swagger.core.version>1.5.22</swagger.core.version>
        <mp.weixin.version>3.6.0</mp.weixin.version>

        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <aliyun.oss.version>2.8.3</aliyun.oss.version>
        <qcloud.cos.version>5.6.19</qcloud.cos.version>
        <alibaba.fastjson>1.2.61</alibaba.fastjson>
        <xxl-job>2.1.0</xxl-job>

        <redis.version>2.9.3</redis.version>

        <rabbitmq.client.version>3.5.0.RELEASE</rabbitmq.client.version>
        <amqp.version>5.8.0</amqp.version>

        <registry.url>192.168.0.13:5000</registry.url>
        <log-path>/tmp/</log-path>
        <poi.version>4.1.2</poi.version>
    </properties>

    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!--断路器依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--redis-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.version}</version>
        </dependency>
        <!--     wxjava.version > 3.8.0之后所需   -->
        <dependency>
            <groupId>com.github.jedis-lock</groupId>
            <artifactId>jedis-lock</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--rabbitmq-->
        <!--        <dependency>-->
        <!--            <groupId>com.rabbitmq</groupId>-->
        <!--            <artifactId>http-client</artifactId>-->
        <!--            <version>${rabbitmq.client.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.rabbitmq</groupId>-->
        <!--            <artifactId>amqp-client</artifactId>-->
        <!--            <version>${amqp.version}</version>-->
        <!--        </dependency>-->
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-common-base-open</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring-platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud alibaba-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--fastjson 模块-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${alibaba.fastjson}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.5</version>
            </plugin>
        </plugins>
    </build>


    <repositories>
<!--        &lt;!&ndash;阿里云&ndash;&gt;-->
<!--        <repository>-->
<!--            <id>nexus</id>-->
<!--            <url>http://**************:8081/repository/maven-public/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--                <updatePolicy>always</updatePolicy>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--                <updatePolicy>always</updatePolicy>-->
<!--            </snapshots>-->
<!--        </repository>-->
    </repositories>

<!--该处为jar包读取位置 nexus私服-->
    <distributionManagement>
        <repository>
            <id>medusa-releases</id>
            <url>http://nexus地址/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>medusa-snapshots</id>
            <url>http://nexus地址/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
