package com.medusa.gruul.afs.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:00 2025/6/12
 */
@Data
@ApiModel(value = "申请售后单信息Vo")
public class ApplyAfsOrderItemVo {

    /**
     * 商品sku编号
     */
    @ApiModelProperty(value = "商品sku编号")
    private Long productSkuId;
    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer productQuantity;

    /**
     * 会员类型id
     */
    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;


}
