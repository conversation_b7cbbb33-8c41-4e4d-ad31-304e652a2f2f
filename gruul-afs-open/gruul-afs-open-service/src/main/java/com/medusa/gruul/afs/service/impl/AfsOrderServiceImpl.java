package com.medusa.gruul.afs.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.enums.AfsOrderCloseTypeEnum;
import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.afs.api.enums.AfsOrderTypeEnum;
import com.medusa.gruul.afs.api.model.AfsSimpleVo;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.afs.mapper.AfsOrderItemMapper;
import com.medusa.gruul.afs.mapper.AfsOrderMapper;
import com.medusa.gruul.afs.model.*;
import com.medusa.gruul.afs.mp.Sender;
import com.medusa.gruul.afs.mp.model.BaseAfsOrderMessage;
import com.medusa.gruul.afs.service.IAfsNegotiateHistoryService;
import com.medusa.gruul.afs.service.IAfsOrderService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.LocalDateTimeUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.logistics.api.feign.RemoteLogisticsFeginService;
import com.medusa.gruul.logistics.model.vo.LogisticsAddressVo;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryItemVo;
import com.medusa.gruul.order.api.model.ManageOrderDeliveryVo;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 售后工单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Slf4j
@Service
public class AfsOrderServiceImpl extends ServiceImpl<AfsOrderMapper, AfsOrder> implements IAfsOrderService {
    @Resource
    private RemoteOrderService orderService;
    @Resource
    private AfsOrderItemMapper afsOrderItemMapper;
    @Resource
    private Sender sender;
    @Resource
    private IAfsNegotiateHistoryService negotiateHistoryService;
    @Resource
    private RemoteLogisticsFeginService remoteLogisticsFeginService;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;

    /**
     * 新增售后详情
     *
     * @param dto            the dto
     * @param orderVo        the order vo
     * @param orderItemList the apply order item
     * @param afsOrder       the afs order
     * @return the afs order item
     */
    public List<AfsOrderItem> saveAfsOrderItem(UserApplyDto dto, OrderVo orderVo, List<OrderItem> orderItemList,
                                         AfsOrder afsOrder) {
        List<AfsOrderItem>afsOrderItemList = new ArrayList<>();
        for (OrderItem orderItem : orderItemList) {
            AfsOrderItem afsOrderItem = new AfsOrderItem();
            afsOrderItem.setAfsId(afsOrder.getId());
            afsOrderItem.setProductId(orderItem.getProductId());
            afsOrderItem.setProductSkuId(orderItem.getProductSkuId());
            afsOrderItem.setProductQuantity(orderItem.getProductQuantity());
            afsOrderItem.setOrderId(orderVo.getId());
            afsOrderItem.setProductPic(orderItem.getProductPic());
            afsOrderItem.setProductName(orderItem.getProductName());
            afsOrderItem.setProductPrice(orderItem.getProductPrice());
            afsOrderItem.setSpecs(orderItem.getSpecs());
            afsOrderItem.setRefundAmount(dto.getRefundAmount());
            afsOrderItem.setLinkProductId(orderItem.getLinkProductId());
            afsOrderItem.setLinkSkuId(orderItem.getLinkSkuId());
            afsOrderItem.setSpecs2(orderItem.getSpecs2());
            afsOrderItem.setPriceType(orderItem.getPriceType());
            afsOrderItem.setMemberTypeId(orderItem.getMemberTypeId());
            afsOrderItemMapper.insert(afsOrderItem);
            afsOrderItemList.add(afsOrderItem);
        }
        return afsOrderItemList;
    }

    /**
     * 用户申请售后
     *
     * @param dto
     * @return com.medusa.gruul.afs.api.entity.AfsOrder
     * <AUTHOR>
     * @date 2021/3/17 22:26
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AfsOrder userApply(UserApplyDto dto) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        OrderVo orderVo = orderService.orderInfo(dto.getOrderId());

        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("订单获取失败，请刷新后重试");
        }
        if (orderVo.getType().equals(OrderTypeEnum.EXCHANGE) || orderVo.getType().equals(OrderTypeEnum.REPLENISH)) {
            Long orderId = baseMapper.selectOriginalOrderByOrderId(dto.getOrderId());
            orderVo = orderService.orderInfo(orderId);
        }
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("订单获取失败，请刷新后重试");
        }
        //查询之前的申请次数
        Integer userAfterSaleNum = getUserApplyNumber(orderVo.getId());

        //获取订单设置
        OrderSetting orderSetting = orderService.getOrderSetting();
        if (ObjectUtil.isNull(orderSetting)) {
            throw new ServiceException("订单设置获取失败，请管理员检查设置并稍后重试");
        }

        //优惠券
        Long couponId = orderVo.getCouponId();




        //扣减金额
        BigDecimal deductionPrice = orderVo.getDeductionPrice();
        if(deductionPrice == null){
            deductionPrice = BigDecimal.ZERO;
        }
        //退款金额
        BigDecimal realAmount = BigDecimal.ZERO;

        //根据订单id获取售后订单
        LambdaQueryWrapper<AfsOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfsOrder::getReceiptBillId,orderVo.getId());
        List<AfsOrder> afsOrderList = this.list(wrapper);

        Boolean allAfsFlag = false;

        if(afsOrderList!=null&&afsOrderList.size()>0){
            for (AfsOrder afsOrder : afsOrderList) {
                //售后单是否生效
                Boolean b = false;
                if(afsOrder.getCloseType()!=null){
                    if(!(afsOrder.getCloseType().equals(AfsOrderCloseTypeEnum.USER_CANCEL)
                            &&afsOrder.getStatus().equals(AfsOrderStatusEnum.CLOSE))){
                        b = true;
                        allAfsFlag = true;
                    }
                }else{
                    b = true;
                    allAfsFlag = true;
                }
                if(b){
                    LambdaQueryWrapper<AfsOrderItem>afsOrderItemWrapper = new LambdaQueryWrapper<>();
                    afsOrderItemWrapper.eq(AfsOrderItem::getAfsId,afsOrder.getId());
                    List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(afsOrderItemWrapper);
                    if(afsOrderItemList!=null&&afsOrderItemList.size()>0){
                        for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                            for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
                                if(orderItemVo.getProductSkuId().equals(afsOrderItem.getProductSkuId())){
                                    orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-afsOrderItem.getProductQuantity());
                                }
                            }
                        }
                    }
                }
            }
        }


        //获取申请的订单详情
        List<OrderItem> orderItemList = new ArrayList<>();
        Long deliveryOrderId = dto.getDeliveryOrderId();
        Long orderItemId = dto.getOrderItemId();
        Integer allAfsOrderFlag = dto.getAllAfsOrderFlag();
        if(allAfsOrderFlag == null){
            allAfsOrderFlag = 0;
        }

        List<BaseApplyDetDto> baseApplyDetDtoList = new ArrayList<>();
        //使用优惠券或者使用扣减金额，整单退

        BigDecimal payAmount = orderVo.getPayAmount();
        BigDecimal totalAmount = orderVo.getTotalAmount();
        BigDecimal youhuiAmount = totalAmount.subtract(payAmount);

        Boolean youhuiFlag = false;
        if(((couponId!=null&&!couponId.equals(""))
                ||deductionPrice.compareTo(BigDecimal.ZERO)>0)){
            youhuiFlag = true;
        }


        //退款
        if(dto.getType().equals(AfsOrderTypeEnum.REFUND)){
            List<OrderItemVo> dataList = new ArrayList<>();
            Boolean lastAfsOrder = true;
            for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
                if(orderItemVo.getId().equals(orderItemId)){
                    orderItemVo.setProductQuantity(orderItemVo.getUnDeliveryQuantity());
                    orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-orderItemVo.getUnDeliveryQuantity());
                    dataList.add(orderItemVo);
                }
                if(orderItemVo.getCanAfsQty()!=0){
                    lastAfsOrder = false;
                }
            }
            if(dataList!=null&&dataList.size()>0){
                OrderItemVo orderItemVo = dataList.get(0);
                orderItemList.add(orderItemVo);
                BaseApplyDetDto baseApplyDetDto = new BaseApplyDetDto();
                baseApplyDetDto.setMemberTypeId(orderItemVo.getMemberTypeId());
                baseApplyDetDto.setProductSkuId(orderItemVo.getProductSkuId());
                baseApplyDetDto.setProductQuantity(orderItemVo.getProductQuantity());
                baseApplyDetDtoList.add(baseApplyDetDto);
                realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(baseApplyDetDto.getProductQuantity())));
                orderItemVo.setRealAmount(realAmount);
                if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                    realAmount = realAmount.add(orderVo.getFreightAmount());
                }
            }
        }else{
            if(deliveryOrderId == null){
                throw new ServiceException("发货单id不能为空！");
            }
            //退货退款
            ManageOrderDeliveryVo manageOrderDeliveryVo = orderService.getOrderDeliveryById(deliveryOrderId);
            List<ManageOrderDeliveryItemVo> itemList = manageOrderDeliveryVo.getItemList();

            List<OrderItemVo> dataList = new ArrayList<>();
            Boolean lastAfsOrder = true;
            for (ManageOrderDeliveryItemVo manageOrderDeliveryItemVo : itemList) {
                List<OrderItemVo> orderItemVoList = orderVo.getOrderItemList().stream().filter(e -> e.getProductSkuId().equals(manageOrderDeliveryItemVo.getProductSkuId()))
                        .collect(Collectors.toList());
                OrderItemVo orderItemVo = orderItemVoList.get(0);
                orderItemVo.setProductQuantity(manageOrderDeliveryItemVo.getProductQuantity());
                //商品数量
                Integer productQuantity = orderItemVo.getProductQuantity();
                //可申请售后数量
                Integer canAfsQty = orderItemVo.getCanAfsQty();

                if(canAfsQty - productQuantity!=0){
                    lastAfsOrder = false;
                }
                dataList.add(orderItemVo);
            }
            if(dataList!=null&&dataList.size()>0){
                for (OrderItemVo orderItemVo : dataList) {
                    orderItemList.add(orderItemVo);
                    BaseApplyDetDto baseApplyDetDto = new BaseApplyDetDto();
                    baseApplyDetDto.setMemberTypeId(orderItemVo.getMemberTypeId());
                    baseApplyDetDto.setProductSkuId(orderItemVo.getProductSkuId());
                    baseApplyDetDto.setProductQuantity(orderItemVo.getProductQuantity());
                    baseApplyDetDtoList.add(baseApplyDetDto);
                    realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(baseApplyDetDto.getProductQuantity())));
                    orderItemVo.setRealAmount(realAmount);
                }
                if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                    realAmount = realAmount.add(orderVo.getFreightAmount());
                }
            }
        }

        if(youhuiFlag&&!allAfsFlag){
            if(youhuiAmount.compareTo(realAmount)>0){
                throw new ServiceException("优惠金额大于当前退款金额！");
            }
            realAmount = realAmount.subtract(youhuiAmount);
        }
        dto.setRefundAmount(realAmount);
        dto.setList(baseApplyDetDtoList);

        //权益包类订单需要判断是否进行过核销
        Integer mallOrderType = orderVo.getMallOrderType();
        if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            //验证订单是否已经进行过核销，进行过核销不允许申请退款
            Boolean result = remoteMiniAccountService.vailPackageOrder(orderVo.getId());
            if(!result){
                throw new ServiceException("权益包订单已核销或已失效，不能申请退款");
            }
        }
        if (ObjectUtil.isEmpty(orderItemList)) {
            throw new ServiceException("无效的商品信息");
        }

        //检查订单状态
        checkOrderStatus(orderVo, dto, orderSetting, userAfterSaleNum,deliveryOrderId);
        //检查售后入参
        checkAfsOrderParam(orderVo, dto);
        //按照售后类型检查是否满足申请条件
        switch (dto.getType()) {
            //退款
            case REFUND:
                checkRefundParam(dto, orderVo);
                break;
            //退货退款
            case RETURN_REFUND:
                checkRefundParam(dto, orderVo);
                    checkReturnParam(dto, orderVo);
                break;
            default:
                throw new ServiceException("不是有效的售后类型");
        }
        // 退款金额前端已经加上邮费，这里重复添加了
//        if (orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)) {
//            dto.setRefundAmount(dto.getRefundAmount().add(orderVo.getFreightAmount()));
//        }

        AfsOrder afsOrder = saveAfsOrder(dto, curUserDto, orderVo, userAfterSaleNum, orderSetting, null,deliveryOrderId,allAfsOrderFlag);

        List<AfsOrderItem> afsOrderItemList = saveAfsOrderItem(dto, orderVo, orderItemList, afsOrder);

        //通惠证订单申请退款，根据订单id冻结用户通惠证记录
        if(orderVo.getType().equals(OrderTypeEnum.TICKET)){
            Boolean b = remoteMiniAccountService.freezeAccountPassTicket(orderVo.getId());
            if(b){
                log.info("冻结用户通惠证记录成功");
            }
        }
        //发送修改权益包订单状态消息
        if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            UpdatePackageOrderStatusMessage updatePackageOrderStatusMessage = new UpdatePackageOrderStatusMessage();
            updatePackageOrderStatusMessage.setOrderId(orderVo.getId()+"");
            updatePackageOrderStatusMessage.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
            sender.sendUpdatePackageOrderStatusMessage(updatePackageOrderStatusMessage);
        }


        //初始化协商历史
        negotiateHistoryService.init(afsOrder,afsOrderItemList, dto, "买家");
        BaseAfsOrderMessage message = new BaseAfsOrderMessage();
        message.setId(afsOrder.getId());
        sender.sendMerchantAutoConfirmMessage(message,
                orderSetting.getMerchantConfirmOvertime() * TimeConstants.ONE_DAY);
        return afsOrder;

    }

    @Override
    @Transactional
    public AfsOrder userAllApply(UserAllApplyDto dto) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        OrderVo orderVo = orderService.orderInfo(dto.getOrderId());

        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("订单获取失败，请刷新后重试");
        }
        if (orderVo.getType().equals(OrderTypeEnum.EXCHANGE) || orderVo.getType().equals(OrderTypeEnum.REPLENISH)) {
            Long orderId = baseMapper.selectOriginalOrderByOrderId(dto.getOrderId());
            orderVo = orderService.orderInfo(orderId);
        }
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("订单获取失败，请刷新后重试");
        }
        //查询之前的申请次数
        Integer userAfterSaleNum = getUserApplyNumber(orderVo.getId());

        //获取订单设置
        OrderSetting orderSetting = orderService.getOrderSetting();
        if (ObjectUtil.isNull(orderSetting)) {
            throw new ServiceException("订单设置获取失败，请管理员检查设置并稍后重试");
        }

        //权益包类订单需要判断是否进行过核销
        Integer mallOrderType = orderVo.getMallOrderType();
        if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            //验证订单是否已经进行过核销，进行过核销不允许申请退款
            Boolean result = remoteMiniAccountService.vailPackageOrder(orderVo.getId());
            if(!result){
                throw new ServiceException("权益包订单已核销或已失效，不能申请退款");
            }
        }
        if (ObjectUtil.isEmpty(orderItemList)) {
            throw new ServiceException("无效的商品信息");
        }

        AfsOrder afsOrder = null;

        Long orderId = dto.getOrderId();
        String images = dto.getImages();
        String description = dto.getDescription();
        BigDecimal totalAmount = orderVo.getTotalAmount();
        BigDecimal payAmount = orderVo.getPayAmount();
        BigDecimal youhuiAmount = totalAmount.subtract(payAmount);
        BigDecimal freightAmount = orderVo.getFreightAmount();

        Boolean freightFlag = true;


        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItemVo orderItemVo : orderItemList) {
                if(orderItemVo.getUnDeliveryQuantity()>0){
                    List<OrderItem> orderItemList2 = new ArrayList<>();
                    UserApplyDto userApplyDto = new UserApplyDto();
                    userApplyDto.setOrderId(orderItemVo.getOrderId());
                    userApplyDto.setOrderItemId(orderItemVo.getId());
                    userApplyDto.setType(AfsOrderTypeEnum.REFUND);
                    userApplyDto.setDescription(description);
                    userApplyDto.setAllAfsOrderFlag(CommonConstants.NUMBER_ONE);
                    userApplyDto.setImages(images);
                    List<BaseApplyDetDto>baseApplyDetDtoList = new ArrayList<>();
                    BaseApplyDetDto baseApplyDetDto = new BaseApplyDetDto();
                    baseApplyDetDto.setMemberTypeId(orderItemVo.getMemberTypeId());
                    baseApplyDetDto.setProductSkuId(orderItemVo.getProductSkuId());
                    baseApplyDetDto.setProductQuantity(orderItemVo.getUnDeliveryQuantity());
                    baseApplyDetDtoList.add(baseApplyDetDto);

                    userApplyDto.setList(baseApplyDetDtoList);
                    BigDecimal realAmount = orderItemVo.getProductPrice().multiply(new BigDecimal(orderItemVo.getUnDeliveryQuantity()));

                    if(freightFlag){
                        realAmount = realAmount.add(freightAmount);
                        freightFlag = false;
                    }
                    if(youhuiAmount.compareTo(BigDecimal.ZERO)>0){
                        if(youhuiAmount.compareTo(realAmount)>=0){
                            userApplyDto.setRefundAmount(BigDecimal.ZERO);
                            youhuiAmount = youhuiAmount.subtract(realAmount);
                        }else{
                            userApplyDto.setRefundAmount(realAmount.subtract(youhuiAmount));
                            youhuiAmount = BigDecimal.ZERO;
                        }
                    }else{
                        userApplyDto.setRefundAmount(realAmount);
                    }

                    orderItemVo.setProductQuantity(orderItemVo.getUnDeliveryQuantity());
                    orderItemList2.add(orderItemVo);

                    if (NumberUtil.isLess(orderVo.getPayAmount(), userApplyDto.getRefundAmount())) {
                        throw new ServiceException("退款金额不得大于实际支付金额");
                    }
                    //检查订单状态
                    checkOrderStatus(orderVo, userApplyDto, orderSetting, userAfterSaleNum,null);
                    //检查售后入参
                    checkAfsOrderParam(orderVo, userApplyDto);

                    checkRefundParam(userApplyDto, orderVo);

                    afsOrder = saveAfsOrder(userApplyDto, curUserDto, orderVo, userAfterSaleNum, orderSetting, null,null,1);
                    userAfterSaleNum++;
                    List<AfsOrderItem> afsOrderItemList = saveAfsOrderItem(userApplyDto, orderVo, orderItemList2, afsOrder);

                    //通惠证订单申请退款，根据订单id冻结用户通惠证记录
                    if(orderVo.getType().equals(OrderTypeEnum.TICKET)){
                        Boolean b = remoteMiniAccountService.freezeAccountPassTicket(orderVo.getId());
                        if(b){
                            log.info("冻结用户通惠证记录成功");
                        }
                    }
                    //发送修改权益包订单状态消息
                    if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                        UpdatePackageOrderStatusMessage updatePackageOrderStatusMessage = new UpdatePackageOrderStatusMessage();
                        updatePackageOrderStatusMessage.setOrderId(orderVo.getId()+"");
                        updatePackageOrderStatusMessage.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                        sender.sendUpdatePackageOrderStatusMessage(updatePackageOrderStatusMessage);
                    }

                    //初始化协商历史
                    negotiateHistoryService.init(afsOrder,afsOrderItemList, userApplyDto, "买家");
                    BaseAfsOrderMessage message = new BaseAfsOrderMessage();
                    message.setId(afsOrder.getId());
                    sender.sendMerchantAutoConfirmMessage(message,
                            orderSetting.getMerchantConfirmOvertime() * TimeConstants.ONE_DAY);
                }
            }
        }
        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        orderDeliveryList.remove(0);
        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
            for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
                List<OrderItem> orderItemList2 = new ArrayList<>();
                UserApplyDto userApplyDto = new UserApplyDto();
                userApplyDto.setOrderId(orderId);
                userApplyDto.setDeliveryOrderId(manageOrderDeliveryVo.getId());
                userApplyDto.setType(AfsOrderTypeEnum.RETURN_REFUND);
                userApplyDto.setDescription(description);
                userApplyDto.setImages(images);
                userApplyDto.setAllAfsOrderFlag(CommonConstants.NUMBER_ONE);
                List<BaseApplyDetDto>baseApplyDetDtoList = new ArrayList<>();
                List<ManageOrderDeliveryItemVo> itemList = manageOrderDeliveryVo.getItemList();
                BigDecimal realAmount = BigDecimal.ZERO;
                for (ManageOrderDeliveryItemVo manageOrderDeliveryItemVo : itemList) {
                    BaseApplyDetDto baseApplyDetDto = new BaseApplyDetDto();

                    List<OrderItemVo> orderItemVoList = orderItemList.stream().filter(e -> e.getProductSkuId().equals(manageOrderDeliveryItemVo.getProductSkuId()))
                            .collect(Collectors.toList());
                    OrderItemVo orderItemVo = orderItemVoList.get(0);
                    baseApplyDetDto.setMemberTypeId(orderItemVo.getMemberTypeId());
                    baseApplyDetDto.setProductSkuId(manageOrderDeliveryItemVo.getProductSkuId());
                    baseApplyDetDto.setProductQuantity(manageOrderDeliveryItemVo.getProductQuantity());
                    userApplyDto.setList(baseApplyDetDtoList);
                    realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(manageOrderDeliveryItemVo.getProductQuantity())));
                    baseApplyDetDtoList.add(baseApplyDetDto);
                    orderItemVo.setProductQuantity(manageOrderDeliveryItemVo.getProductQuantity());
                    orderItemList2.add(orderItemVo);
                }

                if(freightFlag){
                    realAmount = realAmount.add(freightAmount);
                    freightFlag = false;
                }
                if(youhuiAmount.compareTo(BigDecimal.ZERO)>0){
                    if(youhuiAmount.compareTo(realAmount)>=0){
                        userApplyDto.setRefundAmount(BigDecimal.ZERO);
                        youhuiAmount = youhuiAmount.subtract(realAmount);
                    }else{
                        userApplyDto.setRefundAmount(realAmount.subtract(youhuiAmount));
                        youhuiAmount = BigDecimal.ZERO;
                    }
                }else{
                    userApplyDto.setRefundAmount(realAmount);
                }

                if (NumberUtil.isLess(orderVo.getPayAmount(), userApplyDto.getRefundAmount())) {
                    throw new ServiceException("退款金额不得大于实际支付金额");
                }
                //检查订单状态
                checkOrderStatus(orderVo, userApplyDto, orderSetting, userAfterSaleNum,manageOrderDeliveryVo.getId());
                //检查售后入参
                checkAfsOrderParam(orderVo, userApplyDto);

                checkRefundParam(userApplyDto, orderVo);

                afsOrder = saveAfsOrder(userApplyDto, curUserDto, orderVo, userAfterSaleNum, orderSetting, null,manageOrderDeliveryVo.getId(),1);
                userAfterSaleNum++;
                List<AfsOrderItem> afsOrderItemList = saveAfsOrderItem(userApplyDto, orderVo, orderItemList2, afsOrder);

                //通惠证订单申请退款，根据订单id冻结用户通惠证记录
                if(orderVo.getType().equals(OrderTypeEnum.TICKET)){
                    Boolean b = remoteMiniAccountService.freezeAccountPassTicket(orderVo.getId());
                    if(b){
                        log.info("冻结用户通惠证记录成功");
                    }
                }
                //发送修改权益包订单状态消息
                if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                    UpdatePackageOrderStatusMessage updatePackageOrderStatusMessage = new UpdatePackageOrderStatusMessage();
                    updatePackageOrderStatusMessage.setOrderId(orderVo.getId()+"");
                    updatePackageOrderStatusMessage.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    sender.sendUpdatePackageOrderStatusMessage(updatePackageOrderStatusMessage);
                }

                //初始化协商历史
                negotiateHistoryService.init(afsOrder,afsOrderItemList, userApplyDto, "买家");
                BaseAfsOrderMessage message = new BaseAfsOrderMessage();
                message.setId(afsOrder.getId());
                sender.sendMerchantAutoConfirmMessage(message,
                        orderSetting.getMerchantConfirmOvertime() * TimeConstants.ONE_DAY);
            }
        }
        return afsOrder;
    }


    /**
     * 保存售后单
     *
     * @param dto
     * @param curUserDto
     * @param orderVo
     * @param userAfterSaleNum
     * @param orderSetting
     * @param receiptBillId
     * @return com.medusa.gruul.afs.api.entity.AfsOrder
     * <AUTHOR>
     * @date 2021/3/17 22:27
     */
    @Override
    public AfsOrder saveAfsOrder(BaseApplyDto dto, CurUserDto curUserDto, OrderVo orderVo, Integer userAfterSaleNum,
                                 OrderSetting orderSetting, Long receiptBillId,Long deliveryOrderId,Integer allAfsOrderFlag) {
        AfsOrder afsOrder = new AfsOrder();
        afsOrder.setShopId(orderVo.getShopId());
        afsOrder.setNo(orderVo.getId().toString().concat(dto.getType().getName()).concat(String.valueOf(userAfterSaleNum + 1)));
        afsOrder.setType(dto.getType());
        afsOrder.setStatus(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_APPROVED);
        afsOrder.setReceiptBillId(receiptBillId);
        afsOrder.setAllAfsOrderFlag(allAfsOrderFlag);
        afsOrder.setUserId(curUserDto.getUserId());
        afsOrder.setUserName(curUserDto.getNikeName());
        afsOrder.setUserAvatarUrl(curUserDto.getAvatarUrl());
        //afsOrder.setProductSkuId(dto.getProductSkuId());
        afsOrder.setTemplateId(dto.getTemplateId());
        afsOrder.setRefundAmount(dto.getRefundAmount());
//        if (dto.getRefundAmount().equals(BigDecimal.ZERO)) {
//            afsOrder.setRefundAmount(OrderConstant.MIN_PAY_FEE);
//        }
        afsOrder.setDeadline(LocalDateTimeUtil.now().plusDays(orderSetting.getMerchantConfirmOvertime()));
        afsOrder.setDescription(dto.getDescription());

        afsOrder.setReceiptBillId(orderVo.getId());
        afsOrder.setImages(dto.getImages());
        if(deliveryOrderId!=null){
            afsOrder.setDeliveryOrderId(deliveryOrderId);
        }
        Boolean isLogistics = false;
        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){

            if(deliveryOrderId!=null){
                List<ManageOrderDeliveryVo> dataList = orderDeliveryList.stream().filter(e -> e.getId().equals(deliveryOrderId)).collect(Collectors.toList());
                if(dataList!=null&&dataList.size()>0){
                    for (ManageOrderDeliveryVo manageOrderDeliveryVo : dataList) {
                        if(manageOrderDeliveryVo.getDeliveryType().equals(DeliverTypeEnum.LOGISTICS)){
                            isLogistics = true;
                        }
                    }
                }else{
                    throw new ServiceException("配送信息不存在，不能申请售后！");
                }
            }

        }else{
            throw new ServiceException("配送信息不存在，不能申请售后！");
        }
        afsOrder.setIsLogistics(isLogistics);
        this.save(afsOrder);
        return afsOrder;
    }

    /**
     * 检查订单状态
     *
     * @param order
     * @param dto
     * @param orderSetting
     * @param userAfterSaleNum
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:27
     */
    @Override
    public void checkOrderStatus(OrderVo order, BaseApplyDto dto, OrderSetting orderSetting, Integer userAfterSaleNum,Long deliveryOrderId) {
        if (ObjectUtil.isNull(order)) {
            throw new ServiceException("订单不存在，请刷新后再试");
        }
//        if (userAfterSaleNum >= orderSetting.getAfsApplyNumber()) {
//            throw new ServiceException(String.format("订单%s申请售后次数已经超过最大限制，不能继续申请售后", order.getId()));
//        }
        if (OrderStatusEnum.isClose(order.getStatus())) {
            throw new ServiceException(String.format("订单%s已经关闭，不能继续申请售后", order.getId()));
        }
        List<ManageOrderDeliveryVo> orderDeliveryList = order.getOrderDeliveryList();
        LocalDateTime receiveTime = null;
        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
            if(deliveryOrderId!=null){
                List<ManageOrderDeliveryVo> dataList = orderDeliveryList.stream().filter(e -> e.getId().equals(deliveryOrderId)).collect(Collectors.toList());
                if(dataList!=null&&dataList.size()>0){
                    ManageOrderDeliveryVo manageOrderDeliveryVo = dataList.get(0);
                    receiveTime = manageOrderDeliveryVo.getReceiveTime();
                }else{
                    throw new ServiceException("订单发货信息不存在，不能申请售后！");
                }
            }
        }else{
            throw new ServiceException("订单发货信息不存在，不能申请售后！");
        }

        if (ObjectUtil.isNotNull(receiveTime) &&
                DateUtil.offsetDay(LocalDateTimeUtils.convertLDTToDate(receiveTime),
                        orderSetting.getFinishOvertime()).getTime()
                        < System.currentTimeMillis()) {
            throw new ServiceException(String.format("订单%s已经离确认收货已经超过%s天，不能继续申请售后", order.getId(),
                    orderSetting.getFinishOvertime()));
        }

        List<BaseApplyDetDto> list = dto.getList();
        for (BaseApplyDetDto baseApplyDetDto : list) {
            List<AfsOrder> afsOrderList = this.selectProgressByOrderIdAndProductSkuId(order.getId(),
                    baseApplyDetDto.getProductSkuId(),deliveryOrderId);
            if(afsOrderList!=null&&afsOrderList.size()>0){
                for (AfsOrder afsOrder : afsOrderList) {
                    LambdaQueryWrapper<AfsOrderItem>wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(AfsOrderItem::getAfsId,afsOrder.getId());
                    wrapper.eq(AfsOrderItem::getDeleted, CommonConstants.NUMBER_ZERO);
                    List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(wrapper);
                    if(afsOrderItemList!=null&&afsOrderItemList.size()>0){
                        for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                            if (afsOrderItem.getProductSkuId().equals(baseApplyDetDto.getProductSkuId())) {
                                //当前换货单等待审核的也不能再次申请
                                if (afsOrder.getStatus().equals(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_APPROVED)) {
                                    throw new ServiceException(String.format("订单%s正在申请售后，请等待当前售后流程结束", order.getId()));
                                }
                                if (afsOrder.getType().isRefund() && afsOrder.getStatus().equals(AfsOrderStatusEnum.SUCCESS)) {
                                    throw new ServiceException(String.format("订单%s中商品%s,已经成功退款，不能继续申请售后", order.getId(),
                                            baseApplyDetDto.getProductSkuId()));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查售后单参数
     *
     * @param orderVo
     * @param dto
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:27
     */
    private void checkAfsOrderParam(OrderVo orderVo, UserApplyDto dto) {
        Boolean matchProductSkuId = false;
        List<BaseApplyDetDto> list = dto.getList();
        for (OrderItem orderItem : orderVo.getOrderItemList()) {

            List<BaseApplyDetDto> baseList = list.stream().filter(e -> orderItem.getProductSkuId().equals(e.getProductSkuId()) &&
                    orderItem.getMemberTypeId() == e.getMemberTypeId()).collect(Collectors.toList());

            if(baseList!=null&&baseList.size()>0){
                BaseApplyDetDto baseApplyDetDto = baseList.get(0);
                matchProductSkuId = true;
                BigDecimal rate = NumberUtil.div(baseApplyDetDto.getProductQuantity(), orderItem.getProductQuantity());
                log.info("rate is {}", rate);
                BigDecimal refundAmount = NumberUtil.mul(orderItem.getRealAmount(), rate);
                if (ObjectUtil.isNotNull(orderItem.getRealAmount()) || orderItem.getRealAmount().equals(BigDecimal.ZERO)) {
                    refundAmount = NumberUtil.mul(orderVo.getPayAmount(), rate);
                }
                if (baseApplyDetDto.getProductQuantity() > orderItem.getProductQuantity()) {
                    log.info("dto.getProductQuantity() is {}", baseApplyDetDto.getProductQuantity());
                    log.info("orderItem.getProductQuantity() is {}", orderItem.getProductQuantity());
                    throw new ServiceException("所选的商品数量超过订单的总数量");
                }

                if (NumberUtil.isLess(refundAmount, dto.getRefundAmount())) {
                    log.info("refundAmount is {}", refundAmount);
                    log.info("dto.getRefundAmount() is {}", dto.getRefundAmount());
                    throw new ServiceException("退款金额超出所选商品总金额");

                }
                if (!matchProductSkuId) {
                    throw new ServiceException("申请的订单不包含所选商品");
                }
            }
        }

    }

    /**
     * 根据售后单ID获取售后单详情
     *
     * @param afsId
     * @return com.medusa.gruul.afs.model.AfsOrderVo
     * <AUTHOR>
     * @date 2021/3/17 22:27
     */
    @Override
    public AfsOrderVo getAfsOrderInfo(Long afsId) {
        AfsOrder afsOrder = baseMapper.selectById(afsId);
        if (ObjectUtil.isNull(afsOrder)) {
            throw new ServiceException("无效的售后信息，请刷新后重试");
        }
        AfsOrderVo vo = new AfsOrderVo();
        BeanUtil.copyProperties(afsOrder, vo);
        List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(new LambdaQueryWrapper<AfsOrderItem>()
                .eq(AfsOrderItem::getAfsId, afsId));
        vo.setItem(afsOrderItemList);
        OrderVo orderVo = orderService.orderInfo(afsOrder.getReceiptBillId());

        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        if(orderDeliveryList.size()>1){
            orderDeliveryList.remove(0);
        }
        orderVo.setOrderDeliveryList(orderDeliveryList);

        vo.setOriginalOrder(orderVo);


        return vo;
    }



    /**
     * 检查退货参数
     *
     * @param dto
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:28
     */
    private void checkReturnParam(UserApplyDto dto, OrderVo orderVo) {

        List<BaseApplyDetDto> list = dto.getList();

        for (BaseApplyDetDto baseApplyDetDto : list) {
            if (ObjectUtil.isNull(baseApplyDetDto.getProductQuantity()) || baseApplyDetDto.getProductQuantity() < 1) {
                throw new ServiceException("退货数量不得小于1");
            }
            for (OrderItem orderItem : orderVo.getOrderItemList()) {
                if (orderItem.getProductSkuId().equals(baseApplyDetDto.getProductSkuId())
                        &&orderItem.getMemberTypeId() == baseApplyDetDto.getMemberTypeId()) {
                    if (orderItem.getProductQuantity() < baseApplyDetDto.getProductQuantity()) {
                        throw new ServiceException("退货数量不得大于总数量");
                    }
                }
            }
        }

    }

    /**
     * 检查退款参数
     *
     * @param dto
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:28
     */
    private void checkRefundParam(UserApplyDto dto, OrderVo orderVo) {
//        if (ObjectUtil.isNull(dto.getRefundAmount()) || NumberUtil.isLess(dto.getRefundAmount(), BigDecimal.ZERO)) {
//            throw new ServiceException("退款金额不得小于0");
//        }
        if (NumberUtil.isLess(orderVo.getPayAmount(), dto.getRefundAmount())) {
            throw new ServiceException("退款金额不得大于实际支付金额");
        }
    }

    /**
     * 根据订单ID和商品ID查询进行中的订单
     *
     * @param orderId
     * @param productSkuId
     * @return com.medusa.gruul.afs.api.entity.AfsOrder
     * <AUTHOR>
     * @date 2021/3/17 22:29
     */
    @Override
    public List<AfsOrder> selectProgressByOrderIdAndProductSkuId(Long orderId, Long productSkuId,Long deliveryOrderId) {
        return baseMapper.selectProgressByOrderIdAndProductSkuId(orderId, productSkuId,deliveryOrderId);
    }


    /**
     * 获取用户申请售后的次数
     *
     * @param orderId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/3/17 22:30
     */
    @Override
    public Integer getUserApplyNumber(Long orderId) {
        Set<Long> originalOrderIds = new HashSet<>();
        Long originalOrderId = baseMapper.selectOriginalOrderByOrderId(orderId);
        if (ObjectUtil.isNotNull(originalOrderId)) {
            originalOrderIds.add(originalOrderId);
        }
        originalOrderIds.add(orderId);
        Integer userAfterSaleNum = baseMapper.getOrderApplyNumber(CollUtil.newArrayList(originalOrderIds),
                Arrays.asList(AfsOrderTypeEnum.REFUND.getCode(),
                        AfsOrderTypeEnum.RETURN_REFUND.getCode()));
        return userAfterSaleNum;
    }

    /**
     * 用户取消售后
     *
     * @param afsId
     * @param isSystem
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:30
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userCancel(Long afsId, boolean isSystem) {
        AfsOrder afsOrder = this.getById(afsId);
        if (afsOrder.getStatus() == AfsOrderStatusEnum.SUCCESS){
            throw new ServiceException("该售后单已处理");
        }
        if (ObjectUtil.isNull(afsOrder)) {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
        if (afsOrder.getStatus().canReceipt()) {
            afsOrder.setStatus(AfsOrderStatusEnum.CLOSE);
            afsOrder.setCloseType(AfsOrderCloseTypeEnum.USER_CANCEL);
            afsOrder.setCloseTime(LocalDateTime.now());
            afsOrder.setDeadline(null);
            this.updateById(afsOrder);
            negotiateHistoryService.userCancel(afsOrder, isSystem);
        } else {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
    }

    /**
     * 用户退货
     *
     * @param afsId
     * @param deliveryCode
     * @param deliveryCompany
     * @param deliverySn
     * @param phone
     * @param reason
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:30
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userReturn(Long afsId, String deliveryCode, String deliveryCompany, String deliverySn, String phone,
                           String reason) {
        AfsOrder afsOrder = this.getById(afsId);
        if (ObjectUtil.isNull(afsOrder)) {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
        //获取订单设置
        OrderSetting orderSetting = orderService.getOrderSetting();
        if (afsOrder.getIsLogistics()) {
            if (StrUtil.isBlank(deliveryCompany)) {
                throw new ServiceException("物流公司不能为空");
            }
            if (StrUtil.isBlank(deliverySn)) {
                throw new ServiceException("物流单号不能为空");
            }
            if (StrUtil.isBlank(phone)) {
                throw new ServiceException("联系电话不能为空");
            }
        }
        if (afsOrder.getStatus().equals(AfsOrderStatusEnum.WAIT_FOR_RETURN)) {

            afsOrder.setDeliverySn(deliverySn);
            afsOrder.setDeliveryCode(deliveryCode);
            afsOrder.setDeliveryCompany(deliveryCompany);
            // 设置商家最大审核期限
            afsOrder.setDeadline(LocalDateTimeUtil.now().plusDays(orderSetting.getMerchantConfirmOvertime()));
            afsOrder.setStatus(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_RECEIPT);
            this.updateById(afsOrder);
            //添加协商历史
            negotiateHistoryService.userReturn(afsOrder, afsOrder.getIsLogistics());
            //添加操作倒计时
            BaseAfsOrderMessage message = new BaseAfsOrderMessage();
            message.setId(afsOrder.getId());
        } else {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
    }


    /**
     * 根据签收单查询售后
     *
     * @param receiptBillId
     * @return java.util.List<com.medusa.gruul.afs.api.model.AfsSimpleVo>
     * <AUTHOR>
     * @date 2021/3/17 22:30
     */
    @Override
    public List<AfsSimpleVo> getAfsOrderByReceiptBillId(Long receiptBillId) {
        return baseMapper.selectAfsOrderByReceiptBillId(receiptBillId);
    }

    /**
     * 订单签收
     *
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:30
     */
    @Override
    public void orderReceipt(OrderVo orderVo) {
        if (orderVo.getType().equals(OrderTypeEnum.EXCHANGE)) {
            Long orderId = baseMapper.selectOriginalOrderByOrderId(orderVo.getId());
            orderService.receiptOrder(orderId);
        }

        //生成此售后单的原始订单，在确认收货后自动取消等待商家审批的售后单
        AfsOrder closeAfsOrder = baseMapper.selectByOriginalOrderId(orderVo.getId());
        if (ObjectUtil.isNotNull(closeAfsOrder)) {
            if (closeAfsOrder.getStatus().isBusinessApprove()) {
                closeAfsOrder.setStatus(AfsOrderStatusEnum.CLOSE);
                closeAfsOrder.setCloseType(AfsOrderCloseTypeEnum.USER_CANCEL);
                closeAfsOrder.setCloseTime(LocalDateTime.now());
                this.updateById(closeAfsOrder);
            }
        }
        //此售后单生成的新订单，在确认收货后自动更新此售后单
        AfsOrder completeAfsOrder = baseMapper.selectByOrderId(orderVo.getId());
        if (ObjectUtil.isNotNull(completeAfsOrder)) {
            completeAfsOrder.setStatus(AfsOrderStatusEnum.SUCCESS);
            completeAfsOrder.setSuccessTime(LocalDateTime.now());
            this.updateById(completeAfsOrder);
        }
    }

    /**
     * 订单发货
     *
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:31
     */
    @Override
    public void orderShipped(OrderVo orderVo) {
        log.info("执行订单发货逻辑");
        log.info("OrderVo is {}", JSONUtil.toJsonStr(orderVo));
        if (OrderTypeEnum.isExchange(orderVo.getType())) {
            AfsOrder afsOrder = baseMapper.selectByOrderId(orderVo.getId());
            afsOrder.setStatus(AfsOrderStatusEnum.SHIPPED);
            this.updateById(afsOrder);
            negotiateHistoryService.sellerShipped(afsOrder);
        }
    }


    /**
     * 发货单签收
     *
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:31
     */
    @Override
    public void deliverReceipt(OrderVo orderVo) {
        log.info("执行发货单签收逻辑");
        log.info("OrderVo is {}", JSONUtil.toJsonStr(orderVo));
        if (OrderTypeEnum.isExchange(orderVo.getType())) {
            AfsOrder afsOrder = baseMapper.selectByOrderId(orderVo.getId());
            afsOrder.setStatus(AfsOrderStatusEnum.WAIT_FOR_PICKUP);
            this.updateById(afsOrder);
        }
    }
    @Override
    public ApplyAfsOrderVo getApplyAfsDeliveryOrder(ApplyAfsDeliveryOrderDto dto) {
        Long deliveryOrderId = dto.getDeliveryOrderId();
        if(deliveryOrderId == null){
            throw new ServiceException("发货单id不能为空！");
        }
        ApplyAfsOrderVo applyAfsOrderVo = new ApplyAfsOrderVo();

        ManageOrderDeliveryVo manageOrderDeliveryVo = orderService.getOrderDeliveryById(deliveryOrderId);
        List<ManageOrderDeliveryItemVo> itemList = manageOrderDeliveryVo.getItemList();


        Long orderId = manageOrderDeliveryVo.getOrderId();
        OrderVo orderVo = orderService.orderInfo(orderId);
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();


        Long couponId = orderVo.getCouponId();
        BigDecimal deductionPrice = orderVo.getDeductionPrice();
        if(deductionPrice == null){
            deductionPrice = BigDecimal.ZERO;
        }

        //是否有生效的售后单
        LambdaQueryWrapper<AfsOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfsOrder::getReceiptBillId,orderId);
        List<AfsOrder> list = this.list(wrapper);
        Boolean afsFlag = false;
        if(list!=null&&list.size()>0){
            for (AfsOrder afsOrder : list) {
                //售后单是否生效
                Boolean b = false;
                if(afsOrder.getCloseType()!=null){
                    if(!(afsOrder.getCloseType().equals(AfsOrderCloseTypeEnum.USER_CANCEL)
                            &&afsOrder.getStatus().equals(AfsOrderStatusEnum.CLOSE))){
                        b = true;
                        afsFlag = true;
                    }
                }else{
                    b = true;
                    afsFlag = true;
                }
                if(b){
                    LambdaQueryWrapper<AfsOrderItem>afsOrderItemWrapper = new LambdaQueryWrapper<>();
                    afsOrderItemWrapper.eq(AfsOrderItem::getAfsId,afsOrder.getId());
                    List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(afsOrderItemWrapper);
                    if(afsOrderItemList!=null&&afsOrderItemList.size()>0){
                        for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                            for (OrderItemVo orderItemVo : orderItemList) {
                                if(orderItemVo.getProductSkuId().equals(afsOrderItem.getProductSkuId())){
                                    orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-afsOrderItem.getProductQuantity());
                                }
                            }
                        }
                    }
                }
            }
        }
        //使用优惠券或者使用扣减金额，整单退
        if((couponId!=null&&!couponId.equals(""))
                ||deductionPrice.compareTo(BigDecimal.ZERO)>0){


            //根据订单id获取售后订单
            //判断是否开启特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            Integer allAfsOrderFlag = 0;
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                allAfsOrderFlag = specialSetting.getAllAfsOrderFlag();
            }

            if(allAfsOrderFlag == 1){
                //整单退
                ApplyAfsOrderDto applyAfsOrderDto = new ApplyAfsOrderDto();
                applyAfsOrderDto.setOrderId(orderId);
                applyAfsOrderVo = getApplyAfsAllOrder(applyAfsOrderDto);
            }else{
                //不整单退
                BigDecimal totalAmount = orderVo.getTotalAmount();
                BigDecimal payAmount = orderVo.getPayAmount();
                BigDecimal youhuiAmount = totalAmount.subtract(payAmount);

                List<OrderItemVo> dataList = new ArrayList<>();
                Boolean lastAfsOrder = true;
                for (ManageOrderDeliveryItemVo manageOrderDeliveryItemVo : itemList) {
                    List<OrderItemVo> orderItemVoList = orderItemList.stream().filter(e -> e.getProductSkuId().equals(manageOrderDeliveryItemVo.getProductSkuId()))
                            .collect(Collectors.toList());
                    OrderItemVo orderItemVo = orderItemVoList.get(0);
                    orderItemVo.setProductQuantity(manageOrderDeliveryItemVo.getProductQuantity());
                    //商品数量
                    Integer productQuantity = orderItemVo.getProductQuantity();
                    //可申请售后数量
                    Integer canAfsQty = orderItemVo.getCanAfsQty();

                    if(canAfsQty - productQuantity!=0){
                        lastAfsOrder = false;
                    }
                    dataList.add(orderItemVo);
                }

                BigDecimal realAmount = BigDecimal.ZERO;

                if(dataList!=null&&dataList.size()>0){
                    for (OrderItemVo orderItemVo : dataList) {
                        realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(orderItemVo.getProductQuantity())));
                    }
                    if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                        realAmount = realAmount.add(orderVo.getFreightAmount());
                    }
                }
                applyAfsOrderVo.setOrderItemList(dataList);
                //先判断是否存在生效的售后单
                //不存在则表当前为第一张售后单，真实的售后金额为当前售后金额减掉优惠金额
                if(!afsFlag){
                    realAmount = realAmount.subtract(youhuiAmount);
                    if(realAmount.compareTo(BigDecimal.ZERO)<0){
                        throw new ServiceException("当前申请的售后单退款金额不足优惠金额"+youhuiAmount+"，请选择合适的售后单，或者整单退！");
                    }
                }
                applyAfsOrderVo.setRealAmount(realAmount);
            }


        }else{
            List<OrderItemVo> dataList = new ArrayList<>();
            Boolean lastAfsOrder = true;

            for (ManageOrderDeliveryItemVo manageOrderDeliveryItemVo : itemList) {
                List<OrderItemVo> orderItemVoList = orderItemList.stream().filter(e -> e.getProductSkuId().equals(manageOrderDeliveryItemVo.getProductSkuId()))
                        .collect(Collectors.toList());
                OrderItemVo orderItemVo = orderItemVoList.get(0);
                orderItemVo.setProductQuantity(manageOrderDeliveryItemVo.getProductQuantity());
                //商品数量
                Integer productQuantity = orderItemVo.getProductQuantity();
                //可申请售后数量
                Integer canAfsQty = orderItemVo.getCanAfsQty();

                if(canAfsQty - productQuantity!=0){
                    lastAfsOrder = false;
                }
                dataList.add(orderItemVo);
            }
            BigDecimal realAmount = BigDecimal.ZERO;
            if(dataList!=null&&dataList.size()>0){
                for (OrderItemVo orderItemVo : dataList) {
                    realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(orderItemVo.getProductQuantity())));
                }
                if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                    realAmount = realAmount.add(orderVo.getFreightAmount());
                }
            }
            applyAfsOrderVo.setOrderItemList(dataList);
            applyAfsOrderVo.setRealAmount(realAmount);
        }
        return applyAfsOrderVo;
    }



    @Override
    public Integer getAfterQty(Long productId, Long productSkuId, String userId) {
        Integer afterQty = this.baseMapper.getAfterQty(productId,productSkuId,userId);
        return afterQty;
    }

    @Override
    public Integer getAfterQtyByOrder(Long productId, Long productSkuId, Long orderId) {
        Integer afterQty = this.baseMapper.getAfterQtyByOrder(productId,productSkuId,orderId);
        return afterQty;
    }
    @Override
    public ApplyAfsOrderVo getApplyAfsAllOrder(ApplyAfsOrderDto dto) {
        Long orderId = dto.getOrderId();
        if(orderId == null){
            throw new ServiceException("订单id不能为空！");
        }
        ApplyAfsOrderVo applyAfsOrderVo = new ApplyAfsOrderVo();
        applyAfsOrderVo.setAllAfsOrderFlag(CommonConstants.NUMBER_ONE);
        OrderVo orderVo = orderService.orderInfo(orderId);
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
        applyAfsOrderVo.setOrderItemList(orderItemList);
        applyAfsOrderVo.setRealAmount(orderVo.getPayAmount());
        return applyAfsOrderVo;
    }
    @Override
    public ApplyAfsOrderVo getApplyAfsOrder(ApplyAfsOrderDto dto) {
        Long orderId = dto.getOrderId();
        if(orderId == null){
            throw new ServiceException("订单id不能为空！");
        }
        Long orderItemId = dto.getOrderItemId();

        if(orderItemId == null){
            throw new ServiceException("订单详情id不能为空！");
        }
        ApplyAfsOrderVo applyAfsOrderVo = new ApplyAfsOrderVo();
        OrderVo orderVo = orderService.orderInfo(orderId);
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();



        Long couponId = orderVo.getCouponId();
        BigDecimal deductionPrice = orderVo.getDeductionPrice();
        if(deductionPrice == null){
            deductionPrice = BigDecimal.ZERO;
        }
        //根据订单id获取售后订单
        LambdaQueryWrapper<AfsOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfsOrder::getReceiptBillId,orderId);
        List<AfsOrder> list = this.list(wrapper);
        //是否有生效的售后单
        Boolean afsFlag = false;
        if(list!=null&&list.size()>0){
            for (AfsOrder afsOrder : list) {
                //售后单是否生效
                Boolean b = false;
                if(afsOrder.getCloseType()!=null){
                    if(!(afsOrder.getCloseType().equals(AfsOrderCloseTypeEnum.USER_CANCEL)
                            &&afsOrder.getStatus().equals(AfsOrderStatusEnum.CLOSE))){
                        b = true;
                        afsFlag = true;
                    }
                }else{
                    b = true;
                    afsFlag = true;
                }
                if(b){
                    LambdaQueryWrapper<AfsOrderItem>afsOrderItemWrapper = new LambdaQueryWrapper<>();
                    afsOrderItemWrapper.eq(AfsOrderItem::getAfsId,afsOrder.getId());
                    List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(afsOrderItemWrapper);
                    if(afsOrderItemList!=null&&afsOrderItemList.size()>0){
                        for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                            for (OrderItemVo orderItemVo : orderItemList) {
                                if(orderItemVo.getProductSkuId().equals(afsOrderItem.getProductSkuId())){
                                    orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-afsOrderItem.getProductQuantity());
                                }
                            }
                        }
                    }
                }
            }
        }



        //使用优惠券或者使用扣减金额，整单退
        if((couponId!=null&&!couponId.equals(""))
                ||deductionPrice.compareTo(BigDecimal.ZERO)>0){

            //判断是否开启特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            Integer allAfsOrderFlag = 0;
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                allAfsOrderFlag = specialSetting.getAllAfsOrderFlag();
            }

            if(allAfsOrderFlag == 1){
                //整单退
                applyAfsOrderVo = getApplyAfsAllOrder(dto);
            }else{
                //不整单退
                BigDecimal totalAmount = orderVo.getTotalAmount();
                BigDecimal payAmount = orderVo.getPayAmount();
                BigDecimal youhuiAmount = totalAmount.subtract(payAmount);
                List<OrderItemVo> dataList = new ArrayList<>();
                Boolean lastAfsOrder = true;
                for (OrderItemVo orderItemVo : orderItemList) {
                    if(orderItemVo.getId().equals(orderItemId)){
                        orderItemVo.setProductQuantity(orderItemVo.getUnDeliveryQuantity());
                        orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-orderItemVo.getUnDeliveryQuantity());
                        dataList.add(orderItemVo);
                    }
                    if(orderItemVo.getCanAfsQty()!=0){
                        lastAfsOrder = false;
                    }
                }
                BigDecimal realAmount = BigDecimal.ZERO;
                if(dataList!=null&&dataList.size()>0){
                    OrderItemVo orderItemVo = dataList.get(0);
                    realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(orderItemVo.getProductQuantity())));
                    if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                        realAmount = realAmount.add(orderVo.getFreightAmount());
                    }
                }
                applyAfsOrderVo.setOrderItemList(dataList);
                //先判断是否存在生效的售后单
                //不存在则表当前为第一张售后单，真实的售后金额为当前售后金额减掉优惠金额
                if(!afsFlag){
                    realAmount = realAmount.subtract(youhuiAmount);
                    if(realAmount.compareTo(BigDecimal.ZERO)<0){
                        throw new ServiceException("当前申请的售后单退款金额不足优惠金额"+youhuiAmount+"，请选择合适的售后单，或者整单退！");
                    }
                }

                applyAfsOrderVo.setRealAmount(realAmount);
            }
        }else{
            List<OrderItemVo> dataList = new ArrayList<>();
            Boolean lastAfsOrder = true;
            for (OrderItemVo orderItemVo : orderItemList) {
                if(orderItemVo.getId().equals(orderItemId)){
                    orderItemVo.setProductQuantity(orderItemVo.getUnDeliveryQuantity());
                    orderItemVo.setCanAfsQty(orderItemVo.getCanAfsQty()-orderItemVo.getUnDeliveryQuantity());
                    dataList.add(orderItemVo);
                }
                if(orderItemVo.getCanAfsQty()!=0){
                    lastAfsOrder = false;
                }

            }
            BigDecimal realAmount = BigDecimal.ZERO;
            if(dataList!=null&&dataList.size()>0){
                OrderItemVo orderItemVo = dataList.get(0);
                realAmount = realAmount.add(orderItemVo.getProductPrice().multiply(new BigDecimal(orderItemVo.getProductQuantity())));
                if(lastAfsOrder&&orderVo.getStatus().equals(OrderStatusEnum.WAIT_FOR_SEND)){
                    realAmount = realAmount.add(orderVo.getFreightAmount());
                }
            }
            applyAfsOrderVo.setOrderItemList(dataList);
            applyAfsOrderVo.setRealAmount(realAmount);
        }
        return applyAfsOrderVo;
    }

    @Override
    public List<AfsOrder> selectProgressByOrderIdAndIdNotIn(Long orderId, Long afsId) {
        return baseMapper.selectProgressByOrderIdAndIdNotIn(orderId,afsId);
    }




    /**
     * 查询订单列表
     *
     * @param dto
     * @return com.medusa.gruul.common.core.util.PageUtils
     * <AUTHOR>
     * @date 2021/3/17 22:31
     */
    @Override
    public PageUtils<ApiAfsOrderVo> searchOrder(SearchOrderDto dto) {
        Page<ApiAfsOrderVo> page = baseMapper.searchOrder(new Page(dto.getCurrent(), dto.getSize()),
                CurUserUtil.getHttpCurUser().getUserId());
        return new PageUtils<ApiAfsOrderVo>(page);

    }



    /**
     * 订单完成
     *
     * @param orderVo
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:32
     */
    @Override
    public void orderCompleted(OrderVo orderVo) {
        //生成此售后单的原始订单，在评价订单后自动取消等待商家审批的售后单
        AfsOrder closeAfsOrder = baseMapper.selectByOriginalOrderId(orderVo.getId());
        if (ObjectUtil.isNotNull(closeAfsOrder)) {
            if (closeAfsOrder.getStatus().isBusinessApprove()) {
                closeAfsOrder.setStatus(AfsOrderStatusEnum.CLOSE);
                closeAfsOrder.setCloseType(AfsOrderCloseTypeEnum.USER_CANCEL);
                closeAfsOrder.setCloseTime(LocalDateTime.now());
                this.updateById(closeAfsOrder);
            }
        }
    }

    /**
     * 获取退货地址
     *
     * @param orderId
     * @return com.medusa.gruul.afs.model.ReturnAddressVo
     * <AUTHOR>
     * @date 2021/3/17 22:32
     */
    @Override
    public ReturnAddressVo getReturnAddress(Long orderId) {
        ReturnAddressVo returnAddressVo = new ReturnAddressVo();
        OrderVo orderVo = orderService.orderInfo(orderId);
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("无效的订单信息");
        }

        LogisticsAddressVo logisticsAddressVo = remoteLogisticsFeginService.getFeignDefaultAddress(2);
        if (ObjectUtil.isNotNull(logisticsAddressVo)) {
            log.info(JSONUtil.toJsonStr(logisticsAddressVo));
            returnAddressVo.setAddress(logisticsAddressVo.getProvince() + logisticsAddressVo.getCity() + logisticsAddressVo.getCountry() + logisticsAddressVo.getAddress());
            returnAddressVo.setName(logisticsAddressVo.getName());
            returnAddressVo.setPhone(logisticsAddressVo.getPhone());
        } else {
            throw new ServiceException("获取默认退货地址失败");
        }

        return returnAddressVo;
    }

    /**
     * 获取订单是否过期
     *
     * @param orderId
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/3/17 22:33
     */
    @Override
    public Boolean getAfsExpire(Long orderId) {
        OrderVo orderVo = orderService.orderInfo(orderId);
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("订单获取失败，请刷新后重试");
        }

        //获取订单设置
        boolean expire = false;

        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
            OrderSetting orderSetting = orderService.getOrderSetting();
            if (ObjectUtil.isNull(orderSetting)) {
                throw new ServiceException("订单设置获取失败，请管理员检查设置并稍后重试");
            }
            for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
                if (ObjectUtil.isNotNull(manageOrderDeliveryVo.getReceiveTime())) {
                    expire =
                            DateUtil.offsetDay(LocalDateTimeUtils.convertLDTToDate(manageOrderDeliveryVo.getReceiveTime()),
                                    orderSetting.getFinishOvertime()).getTime()
                                    < System.currentTimeMillis();
                }
            }
        }


        return expire;
    }

}
