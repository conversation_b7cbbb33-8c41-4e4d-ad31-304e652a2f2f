package com.medusa.gruul.platform.web.controller.api;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:45 2024/9/12
 */

import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.entity.PlatformEmployee;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.dto.OutPlatformEmployeeDto;
import com.medusa.gruul.platform.service.IPlatformDepartmentService;
import com.medusa.gruul.platform.service.IPlatformEmployeeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外部系统部门
 */
@RestController
@RequestMapping("/out/employee")
public class OutPlatformEmployeeController {

    @Autowired
    private IPlatformEmployeeService platformEmployeeService;


    /**
     * 外部系统-职员信息-添加
     *
     * @param outPlatformEmployeeDto
     * @return
     */
    /**@AutoLog(value = "外部系统-职员信息-添加")*/
    @ApiOperation(value="外部系统-职员信息-添加", notes="外部系统-职员信息-添加")
    @PostMapping(value = "/newAdd")
    public Result<?> newAdd(@RequestBody OutPlatformEmployeeDto outPlatformEmployeeDto) {
        PlatformEmployee platformEmployee = this.platformEmployeeService.newAdd(outPlatformEmployeeDto);
        return Result.ok(platformEmployee);
    }

}
