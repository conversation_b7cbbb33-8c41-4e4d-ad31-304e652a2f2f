package com.medusa.gruul.platform.web.controller.api;

import com.medusa.gruul.common.core.util.Result;

import com.medusa.gruul.platform.model.dto.*;

import com.medusa.gruul.platform.model.vo.*;
import com.medusa.gruul.platform.service.IReservationOrderEvaluateService;
import com.medusa.gruul.platform.service.IReservationOrderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 预约服务
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/api/reservation/order")
@Api(tags = "预约服务接口")
public class ApiReservationOrderController {

    @Autowired
    private IReservationOrderService reservationService;
    @Autowired
    private IReservationOrderEvaluateService reservationOrderEvaluateService;

    /**
     * 获取可预约服务项目列表
     */
    @GetMapping("/services")
    @ApiOperation(value = "获取可预约服务项目列表")
    public Result<List<ServiceContentVo>> listServices() {
        List<ServiceContentVo> serviceList = reservationService.listEnabledServices();
        return Result.ok(serviceList);
    }

   /* *//**
     * 查询上班状态的职员列表
     *//*
    @GetMapping("/employees")
    @ApiOperation(value = "查询上班状态的职员列表")
    public Result<List<ReservationEmployeeVo>> listEmployees() {
        List<ReservationEmployeeVo> employeeList = reservationService.listWorkingEmployees();
        return Result.ok(employeeList);
    }*/
    /**
     * 获取时间 展示的信息
     */
    @PostMapping("/getTimeSlots")
    @ApiOperation(value = "获取时间槽")
    public Result<ReservationTimeSlotVo> getTimeSlots(@Validated @RequestBody TimeSlotQueryDto queryDto) {
        ReservationTimeSlotVo timeSlots = reservationService.getTimeSlots(queryDto);
        return Result.ok(timeSlots);
    }
    /**
     * 获取可预约时间段列表
     */
    @PostMapping("/getEmployeesByTime")
    @ApiOperation(value = "获取可预约时间段列表")
    public Result<List<ReservationEmployeeVo>> listTimeSlots(@RequestBody @Validated TimeSlotQueryDto queryDto) {
        List<ReservationEmployeeVo> employeeVos = reservationService.listAvailableEmployees(queryDto);
        return Result.ok(employeeVos);
    }

    /**
     * 提交预约
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交预约")
    public Result<String> submitReservation(@RequestBody @Validated ReservationSubmitDto submitDto) {
        return reservationService.submitReservation(submitDto);
    }

    /**
     * 获取用户预约列表
     */
    @PostMapping("/user-list")
    @ApiOperation(value = "获取用户预约列表")
    public Result<List<ReservationOrderVo>> getUserReservations(@RequestBody ReservationPageQueryDto queryDto) {
        List<ReservationOrderVo> orderList = reservationService.getUserReservations(queryDto);
        return Result.ok(orderList);
    }


    /**
     * 取消预约
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "取消预约")
    public Result<String> cancelReservation(@RequestBody ReservationPageQueryDto queryDto) {
        if (null ==queryDto.getId()){
            return Result.failed("预约单id不能为空");
        }
        return reservationService.cancelReservation(queryDto.getId());
    }

    /**
     * 完成预约
     */
    @PostMapping("/complete")
    @ApiOperation(value = "完成预约")
    public Result<String> completeReservation(@RequestBody ReservationPageQueryDto queryDto) {
        if (null ==queryDto.getId()){
            return Result.failed("预约单id不能为空");
        }
        return reservationService.completeReservation(queryDto.getId());
    }
    /**
     * 预约单评价
     *
     * @param dto 预约单评价信息
     * @return 操作结果
     */
    @PostMapping("/evaluate")
    @ApiOperation(value = "预约单评价")
    public Result<Boolean> evaluate(@RequestBody @Validated ReservationOrderEvaluateDto dto) {
        boolean result = reservationOrderEvaluateService.evaluate(dto);
        return Result.ok(result);
    }
} 