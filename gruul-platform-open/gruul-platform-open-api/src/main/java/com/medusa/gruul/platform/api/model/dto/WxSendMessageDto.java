package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 19:47 2024/11/6
 */
@Data
@ApiModel(value = "微信小程序发送消息DTO")
public class WxSendMessageDto {


    @ApiModelProperty(value = "小程序appId")
    private String appId;
    @ApiModelProperty(value = "公众号appId")
    private String appMpId;
    @ApiModelProperty(value = "小程序Secret")
    private String appSecret;
    @ApiModelProperty(value = "公众号Secret")
    private String appMpSecret;
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "消息内容")
    private String message;

    @ApiModelProperty(value = "使用类型 1-买家通知 2-商家通知")
    private Integer useType;

    @ApiModelProperty(value = "消息类别 1-订单消息 2-售后消息 3-用户消息 4-营销活动")
    private Integer messageType;

    @ApiModelProperty(value = "标题名称")
    private String title;


    @ApiModelProperty(value = "小程序用户openIds")
    private List<String> openIds;

    @ApiModelProperty(value = "消息模板id")
    private String templateId;

    @ApiModelProperty(value = "点击订阅地址的跳转链接")
    private String page;

    @ApiModelProperty(value = "发送的消息")
    private List<Map<String,String>>mapList;


}
