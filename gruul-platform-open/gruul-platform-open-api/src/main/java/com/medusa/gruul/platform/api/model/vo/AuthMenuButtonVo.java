package com.medusa.gruul.platform.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 菜单角色返回信息
 * @Date: Created in 10:21 2023/9/21
 */
@Data
@ApiModel(value = "AuthMenuButtonVo对象", description = "菜单角色返回信息")
public class AuthMenuButtonVo {

    @ApiModelProperty(value = "按钮id")
    private Long id;

    @ApiModelProperty(value = "按钮名称")
    private String buttonName;

    @ApiModelProperty(value = "按钮编码")
    private String buttonCode;

    @ApiModelProperty(value = "菜单id")
    private Long menuId;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

}
