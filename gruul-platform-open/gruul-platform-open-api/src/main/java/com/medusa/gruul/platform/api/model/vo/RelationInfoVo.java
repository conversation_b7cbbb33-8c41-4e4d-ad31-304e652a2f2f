package com.medusa.gruul.platform.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:49 2024/10/29
 */
@Data
@ApiModel(value = "RelationInfoVo对象", description = "关联信息Dto")
public class RelationInfoVo {

    @ApiModelProperty(value = "平台用户商家id")
    private String shopId;

    @ApiModelProperty(value = "平台用户id")
    private String accountId;

    @ApiModelProperty(value = "用户名称")
    private String accountName;

    @ApiModelProperty(value = "职员id")
    private String employeeId;

    @ApiModelProperty(value = "职员标识")
    private String employeeOutId;

    @ApiModelProperty(value = "职员名称")
    private String employeeName;

    @ApiModelProperty(value = "部门id")
    private String departmentId;

    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "门店id")
    private String storeFrontId;

    @ApiModelProperty(value = "门店标识")
    private String storeFrontCode;

    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    @ApiModelProperty(value = "仓库id")
    private String stockId;

    @ApiModelProperty(value = "仓库标识")
    private String stockCode;

    @ApiModelProperty(value = "仓库名称")
    private String stockName;

    @ApiModelProperty(value = "小程序客户id")
    private String miniAccountId;

}
