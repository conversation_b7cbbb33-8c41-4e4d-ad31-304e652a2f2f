package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:48 2024/5/13
 */
@Data
@ApiModel(value = "租户菜单dto")
public class AuthUserMenuDto {

    @ApiModelProperty(value = "父级菜单")
    @NotEmpty
    private List<MenuDto> menuParent;

    @ApiModelProperty(value = "菜单")
    private List<MenuDto> menu;

    @ApiModelProperty(value = "按钮")
    private List<ButtonDto>button;


}
