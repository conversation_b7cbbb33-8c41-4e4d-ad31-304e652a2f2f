package com.medusa.gruul.platform.api.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.platform.api.entity.AuthMenuButton;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 新增或修改菜单按钮DTO
 * @Date: Created in 9:48 2023/9/21
 */
@Data
@ApiModel(value = "新增或修改菜单按钮DTO")
public class AuthMenuButtonDto {

    @ApiModelProperty(value = "按钮id")
    private Long id;

    @ApiModelProperty(value = "按钮名称")
    @NotNull(message = "按钮名称不能为空")
    private String buttonName;

    @ApiModelProperty(value = "按钮编码")
    @NotNull(message = "按钮编码不能为空")
    private String buttonCode;

    @ApiModelProperty(value = "菜单id")
    @NotNull(message = "菜单id不能为空")
    private Long menuId;

    public AuthMenuButton coverAuthMenuButton() {
        AuthMenuButton authMenuButton = new AuthMenuButton();
        BeanUtil.copyProperties(this, authMenuButton);
        return authMenuButton;
    }
}
