package com.medusa.gruul.platform.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PlatformPositionVo对象", description = "商家职位Vo")
public class PlatformPositionVo {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 职位名称
     */
    @ApiModelProperty(value = "职位名称")
    private String positionName;


    /**
     * 状态 0-停用，1-启用
     */
    @ApiModelProperty(value = "状态 0-停用，1-启用")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否为当前员工的职位
     */
    @ApiModelProperty(value = "是否为当前员工的职位 0-否 1-是")
    private Integer isEmployeePosts;

}
