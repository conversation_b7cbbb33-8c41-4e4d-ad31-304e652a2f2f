package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:47 2024/9/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="接收外部接口部门对象", description="部门信息")
public class OutPlatformDepartmentDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    private String shopId;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;

    /**
     * 部门全称
     */
    @ApiModelProperty(value = "部门全称")
    private String deptFullName;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String departmentName;


    /**
     * classCode
     */
    @ApiModelProperty(value = "classCode")
    private String classCode;

    /**
     * 停用启用状态
     */
    @ApiModelProperty(value = "停用启用状态")
    private String 	stopOpenState;
}
