package com.medusa.gruul.platform.api.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 新增或修改二级菜单DTO
 * @Date: Created in 10:35 2023/9/18
 */
@Data
@ApiModel(value = "新增或修改二级菜单DTO")
public class AuthMenuInfoSecondDto {

    @ApiModelProperty(value = "菜单id")
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "父结点id,0为顶级节点")
    private Long menuPid;

    @ApiModelProperty(value = "菜单等级;0：一级；1：二级；")
    private Integer level;

    public AuthMenuInfo coverAuthMenuInfo() {
        AuthMenuInfo authMenuInfo = new AuthMenuInfo();
        BeanUtil.copyProperties(this, authMenuInfo);
        authMenuInfo.setLevel(1);
        return authMenuInfo;
    }

}
