package com.medusa.gruul.platform.api.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:03 2024/11/6
 */
@Data
public class WxMessageTemplateDetailVo {

    @ApiModelProperty(value = "模板内容id")
    private Long id;

    @ApiModelProperty(value = "主表id")
    private String mainId;

    @ApiModelProperty(value = "字段名称")
    private String name;

    @ApiModelProperty(value = "字段标识")
    private String keyData;

    @ApiModelProperty(value = "字段值")
    private String valueData;

}
