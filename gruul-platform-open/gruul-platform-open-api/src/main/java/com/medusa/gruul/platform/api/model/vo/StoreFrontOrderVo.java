package com.medusa.gruul.platform.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 商家平台登录用户绑定的职员、部门、门店信息
 * @Date: Created in 8:59 2024/10/10
 */
@Data
@ApiModel(value = "StoreFrontOrderVo对象", description = "下单获取门店部门职员信息vo")
public class StoreFrontOrderVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门code")
    private String departmentCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private String employeeId;

    /**
     * 职员名称
     */
    @ApiModelProperty(value = "职员名称")
    private String employeeName;
    /**
     * 仓库标识
     */
    @ApiModelProperty(value = "仓库标识")
    private String stockCode;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private String storeFrontId;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String storeFrontCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    /**
     * 门店地址
     */
    @ApiModelProperty(value = "门店地址")
    private String storeFrontAddress;

    /**
     * 门店末级
     */
    @ApiModelProperty(value = "门店末级")
    private String isCatalog;

}
