package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 菜单dto
 * @Date: Created in 14:47 2023/9/19
 */
@Data
@ApiModel(value = "菜单dto")
public class MenuDto {

    @ApiModelProperty(value = "菜单id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "菜单父级id")
    @NotNull
    private Long parentId;


}
