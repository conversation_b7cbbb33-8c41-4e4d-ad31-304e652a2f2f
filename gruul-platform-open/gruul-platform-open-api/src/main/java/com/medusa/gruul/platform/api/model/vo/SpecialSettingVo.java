package com.medusa.gruul.platform.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:19 2024/8/15
 */
@Data
@ApiModel(value = "SpecialSettingVo对象", description = "特殊配置vo")
public class SpecialSettingVo {

    /**
     * 平台查看商户订单：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "平台查看商户订单：0->否；1->是")
    private Integer platformSearchShopOrder;


    /**
     * 商户允许平台查看订单：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "商户允许平台查看订单：0->否；1->是")
    private Integer shopAllowPlatformSearchOrder;

    /**
     * 平台代商户发货：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "平台代商户发货：0->否；1->是")
    private Integer platformProxyShipping;

    /**
     * 新人优惠券领取天数
     */
    @NotNull
    @ApiModelProperty(value = "新人优惠券领取天数")
    private Integer newPeopleCouponDays;

    /**
     * 小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;
     */
    @NotNull
    @ApiModelProperty(value = "小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;")
    private Integer miniShowProductType;

    /**
     * 会员指定商品销售->0.关闭;1.启用;
     */
    @NotNull
    @ApiModelProperty(value = "会员指定商品销售->0.关闭;1.启用;")
    private Integer memberSales;


    /**
     * 佣金提现是否自动审核：0->否；1->是
     */
    @ApiModelProperty(value = "佣金提现是否自动审核：0->否；1->是")
    private Integer commissionCashFlag;

    /**
     * 接口对接职员是否可添加：0->否；1->是
     */
    @ApiModelProperty(value = "接口对接职员是否可添加：0->否；1->是")
    private Integer addEmployeeFlag;

    /**
     * 认证注册才允许进入小程序：0->关闭；1.开启
     */
    @ApiModelProperty(value = "认证注册才允许进入小程序：0->关闭；1.开启")
    private Integer authRegisterBrowseFlag;

    /**
     * 商品显示会员价：0->关闭；1.开启
     */
    @ApiModelProperty(value = "商品显示会员价：0->关闭；1.开启")
    private Integer showMemberPriceFlag;

    /**
     * 提现方式:1->线下提现；2.线上提现；3.线上+线下提现
     */
    @ApiModelProperty(value = "提现方式:1->线下提现；2.线上提现；3.线上+线下提现")
    private Integer withdrawalMethod;

    /**
     * 是否开启售后整单退：0->关闭；1.开启
     */
    @ApiModelProperty(value = "是否开启售后整单退：0->关闭；1.开启")
    private Integer allAfsOrderFlag;
}
