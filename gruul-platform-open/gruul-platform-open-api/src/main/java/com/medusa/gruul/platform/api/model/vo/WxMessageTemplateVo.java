package com.medusa.gruul.platform.api.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:06 2024/11/6
 */
@Data
public class WxMessageTemplateVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "模板标识->1.活动通知;2.生日祝福提醒;3.商品上新;4.卖家发货提醒")
    private String code;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板类型->1.订阅消息;2.公众号模板消息")
    private Integer type;

    @ApiModelProperty(value = "模板状态->1.启用;2.停用")
    private Integer status;

    @ApiModelProperty(value = "模板内容")
    private List<WxMessageTemplateDetailVo>list;

}
