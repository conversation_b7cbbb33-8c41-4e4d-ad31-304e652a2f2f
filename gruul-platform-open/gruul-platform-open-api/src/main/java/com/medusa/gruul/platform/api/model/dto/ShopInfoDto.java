package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @data: 2020/7/24
 */
@Data
public class ShopInfoDto {

    @ApiModelProperty(value = "店铺首页图片")
    private String logoUrl;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;


    @ApiModelProperty(value = "0审核中，1部署中 2正常 ，3已打烊，4禁用")
    private Integer status;

    @ApiModelProperty(value = "绑定的小程序id")
    private Long bindMiniId;

    @ApiModelProperty(value = "绑定的公众号id")
    private Long bindMpId;

    @ApiModelProperty(value = "允许商品上架最大数量")
    private String publishProductMaxNum;

    @ApiModelProperty(value = "允许权益包上架最大数量")
    private String publishPackageMaxNum;

    @ApiModelProperty(value = "允许末级门店启用最大数量")
    private String enableStoreMaxNum;

    @ApiModelProperty(value = "业务员邀请码")
    private String salesmanInviteCode;
}
