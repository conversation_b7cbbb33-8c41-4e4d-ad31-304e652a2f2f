package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:18 2024/10/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="商家管理保存门店地址对象", description="商家管理保存门店地址对象")
public class StoreFrontAddressDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @TableField("address")
    private String address;


    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    @TableField("map_x")
    private Double mapX;


    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    @TableField("map_y")
    private Double mapY;


    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private String accountId;

    /**
     * 会员名称
     */
    @ApiModelProperty(value = "会员名称")
    private String accountName;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private String employeeId;

}
