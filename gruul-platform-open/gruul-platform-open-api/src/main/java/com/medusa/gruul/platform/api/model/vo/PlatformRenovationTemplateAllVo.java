package com.medusa.gruul.platform.api.model.vo;

import com.medusa.gruul.platform.api.entity.PlatformRenovationPageAssembly;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePage;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePlugin;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@ApiModel(value = "PlatformRenovationTemplateAllVo 实体", description = "平台装修模板及其关联表所有数据视图")
public class PlatformRenovationTemplateAllVo {


    @ApiModelProperty("id")
    private Long id;

    /**模版名称*/
    @ApiModelProperty(value = "模版名称")
    private String name;
    /**模版编号*/
    @ApiModelProperty(value = "模版编号")
    private String code;
    /**分类类型：1 系统模版 2 定制模版*/
    @ApiModelProperty(value = "分类类型：1 系统模版 2 定制模版")
    private Integer type;
    /**模版应用类型：1商城，2社区拼团，3门店版*/
    @ApiModelProperty(value = "模版应用类型：0自定义，1社区拼团，2商城，3门店版")
    private Integer shopTemplateType;
    /**
     * 模板全局颜色 0红 1绿 2蓝
     */
    @NotNull
    @ApiModelProperty(value = "模板全局颜色 0红 1绿 2蓝")
    private String colour;

    /**
     * 模板是否使用中 0 否, 1 是
     */
    @NotNull
    @ApiModelProperty(value = "模板是否使用中 0 否, 1 是")
    private String onlineStatus;
    /**备注*/
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "平台装修模板全局控件属性列表")
    private List<PlatformRenovationTemplatePlugin> pluginList;
    @ApiModelProperty(value = "平台装修模板页面列表")
    private List<PlatformRenovationTemplatePage> pageList;
    @ApiModelProperty(value = "平台装修模板页面组件属性列表")
    private List<PlatformRenovationPageAssembly> assemblyList;

}
