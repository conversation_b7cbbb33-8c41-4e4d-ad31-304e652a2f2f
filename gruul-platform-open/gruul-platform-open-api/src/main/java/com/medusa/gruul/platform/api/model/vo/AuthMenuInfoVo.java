package com.medusa.gruul.platform.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:37 2023/9/18
 */
@Data
@ApiModel(value = "AuthMenuInfoVo对象", description = "展示菜单查询返回信息")
public class AuthMenuInfoVo {

    @ApiModelProperty(value = "菜单id")
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "父结点id,0为顶级节点")
    private Long menuPid;

    @ApiModelProperty(value = "二级菜单id(数据传输用)")
    private Long authMenuInfoId;

    @ApiModelProperty(value = "菜单等级;0：一级；1：二级；")
    private Integer level;

    @ApiModelProperty(value = "二级菜单")
    private List<AuthMenuInfoSecondVo> authMenuInfoSecondVos;


}
