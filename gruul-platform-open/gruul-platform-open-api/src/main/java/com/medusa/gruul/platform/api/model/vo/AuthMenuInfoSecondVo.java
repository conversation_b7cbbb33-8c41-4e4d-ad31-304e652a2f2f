package com.medusa.gruul.platform.api.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 展示二级菜单查询返回信息
 * @Date: Created in 9:38 2023/9/18
 */
@Data
@ApiModel(value = "AuthMenuInfoSecondVo对象", description = "展示二级菜单查询返回信息")
public class AuthMenuInfoSecondVo {

    @ApiModelProperty(value = "菜单id")
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "父结点id,0为顶级节点")
    private Long menuPid;

    @ApiModelProperty(value = "二级菜单id(数据传输用)")
    private Long authMenuInfoId;

    @ApiModelProperty(value = "菜单等级;0：一级；1：二级；")
    private Integer level;

    @ApiModelProperty(value = "菜单按钮")
    private List<AuthMenuButtonVo> authMenuButtonVos;

}
