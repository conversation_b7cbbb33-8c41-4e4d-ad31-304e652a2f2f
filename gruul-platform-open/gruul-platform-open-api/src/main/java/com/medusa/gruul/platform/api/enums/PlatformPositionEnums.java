package com.medusa.gruul.platform.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <p>
 * 平台相关枚举定义
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public class PlatformPositionEnums {

    /**
     * <p>
     * 适用商家类型枚举
     * </p>
     */
    @Getter
    public enum PartnerModelEnum {

        /**
         * 加盟
         */
        FRANCHISE("0", "加盟"),

        /**
         * 子公司
         */
        SUBSIDIARY("1", "子公司"),

        /**
         * 运营中心
         */
        OPERATION_CENTER("2", "运营中心");

        /**
         * 类型码
         */
        @EnumValue
        @JsonValue
        private final String code;

        /**
         * 类型描述
         */
        private final String desc;

        PartnerModelEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 类型码
         * @return 对应的枚举值
         */
        public static PartnerModelEnum getByCode(String code) {
            for (PartnerModelEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * <p>
     * 状态枚举
     * </p>
     */
    @Getter
    public enum StatusEnum {

        /**
         * 停用
         */
        DISABLED(0, "停用"),

        /**
         * 启用
         */
        ENABLED(1, "启用");

        /**
         * 状态码
         */
        @EnumValue
        @JsonValue
        private final Integer code;

        /**
         * 状态描述
         */
        private final String desc;

        StatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 状态码
         * @return 对应的枚举值
         */
        public static StatusEnum getByCode(Integer code) {
            for (StatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    public enum DeleteEnum {
        /**
         * 已删除
         */
        DELETED(1, "已删除"),

        /**
         * 未删除
         */
        NOT_DELETED(0, "未删除");

        /**
         * 状态码
         */
        @EnumValue
        @JsonValue
        private final Integer code;

        /**
         * 状态描述
         */
        private final String desc;

        DeleteEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        /**
         * 根据code获取枚举
         *
         * @param code 状态码
         * @return 对应的枚举值
         */
        public static DeleteEnum getByCode(Integer code) {
            for (DeleteEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    public enum DefaultEnum {
        YES(1, "是"),
        NO(0, "否");
        @EnumValue
        @JsonValue
        private final Integer code;
        private final String desc;
        DefaultEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        public static DefaultEnum getByCode(Integer code) {
            for (DefaultEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }
} 