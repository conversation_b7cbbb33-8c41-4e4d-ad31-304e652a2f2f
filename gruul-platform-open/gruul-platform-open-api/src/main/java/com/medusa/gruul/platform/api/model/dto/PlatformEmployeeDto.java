package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:59 2025/5/19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="职员信息Dto", description="职员信息Dto")
public class PlatformEmployeeDto {


    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 职员编号
     */
    @ApiModelProperty(value = "职员编号")
    private String empNumber;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String empFullName;

    /**
     * 职员照片
     */
    @ApiModelProperty(value = "职员照片")
    private String employeeImageUrl;

    /**
     * 初始好评数，必须大于等于0
     */
    @Min(value = 0, message = "初始好评数必须大于等于0")
    @ApiModelProperty(value = "初始好评数")
    private Integer highNumber;

    /**
     * 初始服务人数，必须大于等于0
     */
    @Min(value = 0, message = "初始服务人数必须大于等于0")
    @ApiModelProperty(value = "初始服务人数")
    private Integer serviceNumber;

    /**
     * 工作状态
     */
    @NotNull(message = "工作状态不能为空")
    @ApiModelProperty(value = "工作状态")
    private Integer workStatus;


}
