package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:31 2024/10/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="接收外部接口门店对象", description="门店信息")
public class OutPlatformStoreFrontDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    private String shopId;

    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String classCode;

    /**
     * 门店编号
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    /**
     * 门店全称
     */
    @ApiModelProperty(value = "门店全称")
    private String storeFullName;

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String storeArea;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String departmentId;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private String employeeId;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private String stockId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusId;

    /**
     * 部门标识
     */
    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    /**
     * 仓库标识
     */
    @ApiModelProperty(value = "仓库标识")
    private String stockCode;

    /**
     * 是否末级
     */
    @ApiModelProperty(value = "是否末级")
    private String isCatalog;

}
