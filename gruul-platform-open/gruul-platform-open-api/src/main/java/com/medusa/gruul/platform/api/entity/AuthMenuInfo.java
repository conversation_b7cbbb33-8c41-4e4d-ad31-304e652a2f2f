package com.medusa.gruul.platform.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 系统菜单表
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@Accessors(chain = true)
@TableName("t_auth_menu_info")
@ApiModel(value = "AuthMenuInfo对象", description = "系统菜单表")
public class AuthMenuInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 菜单名称
     */
    @ApiModelProperty(value = "菜单名称")
    @TableField("menu_name")
    private String menuName;

    /**
     * 菜单编码
     */
    @ApiModelProperty(value = "菜单编码")
    @TableField("menu_code")
    private String menuCode;
    /**
     * 父结点id,0为顶级节点
     */
    @ApiModelProperty(value = "父结点id,0为顶级节点")
    @TableField(value = "menu_pid")
    private Long menuPid;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    @TableField("menu_sort")
    private Integer menuSort;


    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近一次修改人id
     */
    @ApiModelProperty(value = "最近一次修改人id")
    @TableField("modify_user_id")
    private Long modifyUserId;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;


    /**
     * 最近一次修改人姓名
     */
    @ApiModelProperty(value = "最近一次修改人姓名")
    @TableField("modify_user_name")
    private String modifyUserName;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    @TableField("model_id")
    private Long modelId;

    /**
     * 菜单路径
     */
    @ApiModelProperty(value = "菜单路径")
    @TableField("menu_path")
    private String menuPath;


    /**
     * 菜单组件
     */
    @ApiModelProperty(value = "菜单组件")
    @TableField("menu_component")
    private String menuComponent;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    @TableField("menu_icon")
    private String menuIcon;

    /**
     * 是否显示;0：是；1：否
     */
    @ApiModelProperty(value = "是否显示;0：否；1：是")
    @TableField(value = "is_show")
    private Integer isShow;

    /**
     * 是否链接;0：是；1：否
     */
    @ApiModelProperty(value = "是否链接;0：否；1：是")
    @TableField("is_link")
    private Integer isLink;

    /**
     * 菜单等级;0：一级；1：二级；
     */
    @ApiModelProperty(value = "菜单等级;0：一级；1：二级；")
    @TableField("level")
    private Integer level;

}
