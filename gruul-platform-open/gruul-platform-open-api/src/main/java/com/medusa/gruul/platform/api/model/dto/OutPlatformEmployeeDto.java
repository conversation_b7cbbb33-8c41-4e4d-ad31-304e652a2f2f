package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:45 2024/9/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="接收外部接口职员对象", description="职员信息")
public class OutPlatformEmployeeDto {


    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    private String shopId;

    /**
     * 职员编号
     */
    @ApiModelProperty(value = "职员编号")
    private String empNumber;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String departmentCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String empFullName;


    /**
     * classCode
     */
    @ApiModelProperty(value = "classCode")
    private String classCode;

    /**
     * 外部系统对应职员id
     */
    @ApiModelProperty(value = "外部系统对应职员id")
    private String outId;


}
