package com.medusa.gruul.common.core.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批量填充工具类 - 极简版
 * 一行代码解决批量关联数据填充问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
public class BatchFillUtils {

    /**
     * 批量填充一对多关联数据 - 极简版
     * 
     * 使用示例：
     * BatchFillUtils.fillOneToMany(records, EmployeeVo::getId, 
     *     ids -> positionService.getPositionsByEmployeeIds(ids), 
     *     EmployeeVo::setPositionList);
     * 
     * @param list 主数据列表
     * @param keyGetter 获取关联键的方法
     * @param dataLoader 批量加载数据的方法
     * @param dataSetter 设置数据的方法
     */
    public static <T, K, V> void fillOneToMany(
            List<T> list,
            Function<T, K> keyGetter,
            Function<List<K>, Map<K, List<V>>> dataLoader,
            java.util.function.BiConsumer<T, List<V>> dataSetter) {
        
        if (CollectionUtils.isEmpty(list)) return;
        
        // 提取关联键 -> 批量加载 -> 填充数据
        List<K> keys = list.stream().map(keyGetter).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keys)) return;
        
        Map<K, List<V>> dataMap = dataLoader.apply(keys);
        if (dataMap == null || dataMap.isEmpty()) return;
        
        list.forEach(item -> {
            K key = keyGetter.apply(item);
            if (key != null) {
                List<V> data = dataMap.get(key);
                dataSetter.accept(item, data != null ? data : Collections.emptyList());
            }
        });
    }

    /**
     * 批量填充一对一关联数据 - 极简版
     */
    public static <T, K, V> void fillOneToOne(
            List<T> list,
            Function<T, K> keyGetter,
            Function<List<K>, Map<K, V>> dataLoader,
            java.util.function.BiConsumer<T, V> dataSetter) {
        
        if (CollectionUtils.isEmpty(list)) return;
        
        List<K> keys = list.stream().map(keyGetter).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keys)) return;
        
        Map<K, V> dataMap = dataLoader.apply(keys);
        if (dataMap == null || dataMap.isEmpty()) return;
        
        list.forEach(item -> {
            K key = keyGetter.apply(item);
            if (key != null) {
                V data = dataMap.get(key);
                if (data != null) {
                    dataSetter.accept(item, data);
                }
            }
        });
    }
}
