package com.medusa.gruul.payment.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:47 2025/4/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_wx_transfer_scene_v3")
@ApiModel(value = "WxTransferSceneV3对象", description = "微信转账场景")
public class WxTransferSceneV3 extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 不能超过15个字符，商户所属转账场景下的信息类型，此字段内容为固定值
     */
    @ApiModelProperty(value = "信息类型")
    @TableField("info_type")
    private String infoType;

    /**
     * 不能超过32个字符，商户所属转账场景下的信息内容，商户可按实际业务场景自定义传参
     */
    @ApiModelProperty(value = "信息内容")
    @TableField("info_content")
    private String infoContent;

    /**
     * 微信转账id
     */
    @ApiModelProperty(value = "微信转账id")
    @TableField("transfer_id")
    private String transferId;

}
