package com.medusa.gruul.payment.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.annotations.SerializedName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:56 2025/4/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_wx_transfer_v3")
@ApiModel(value = "WxTransferV3对象", description = "微信小程序转账信息")
public class WxTransferV3 extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 是微信开放平台和微信公众平台为开发者的应用程序(APP、小程序、公众号、企业号corpid即为此AppID)提供的一个唯一标识。此处，
     * 可以填写这四种类型中的任意一种APPID，但请确保该appid与商户号有绑定关系
     */
    @ApiModelProperty(value = "商户AppID")
    @TableField("appid")
    private String appid;

    /**
     * 商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
     */
    @ApiModelProperty(value = "商户单号")
    @TableField("out_bill_no")
    private String outBillNo;

    /**
     * 该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1000（现金营销），1005（佣金报酬），1006（企业报销）等
     */
    @ApiModelProperty(value = "转账场景ID")
    @TableField("transfer_scene_id")
    private String transferSceneId;

    /**
     * 用户在商户appid下的唯一标识。发起转账前需获取到用户的OpenID
     */
    @ApiModelProperty(value = "收款用户OpenID")
    @TableField("openid")
    private String openid;

    /**
     * 收款方真实姓名。需要加密传入，支持标准RSA算法和国密算法，公钥由微信侧提供。
     */
    @ApiModelProperty(value = "收款用户OpenID")
    @TableField("user_name")
    private String userName;
    /**
     * 转账金额单位为“分”
     */
    @ApiModelProperty(value = "转账金额")
    @TableField("transfer_amount")
    private Integer transferAmount;

    /**
     * 异步接收微信支付结果通知的回调地址，通知url必须为公网可访问的url，必须为https，不能携带参数。
     */
    @ApiModelProperty(value = "通知地址")
    @TableField("notify_url")
    private String notifyUrl;

    /**
     * 用户收款时感知到的收款原因将根据转账场景自动展示默认内容。如有其他展示需求，可在本字段传入。各场景展示的默认内容和支持传入的内容
     */
    @ApiModelProperty(value = "用户收款感知")
    @TableField("user_recv_perception")
    private String userRecvPerception;

    /**
     * 转账备注，用户收款时可见该备注信息，UTF8编码
     */
    @ApiModelProperty(value = "转账备注")
    @TableField("transfer_remark")
    private String transferRemark;

    /**
     * 转账状态
     */
    @ApiModelProperty(value = "转账状态")
    @TableField("state")
    private String state;

    /**
     * 转账结果
     */
    @ApiModelProperty(value = "转账结果")
    @TableField("reason")
    private String reason;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    @TableField("fail_result")
    private String failResult;

    /**
     * 微信转账单号
     */
    @ApiModelProperty(value = "微信转账单号")
    @TableField("transfer_bill_no")
    private String transferBillNo;

    /** 跳转领取页面的package信息 Y 说明：跳转微信支付收款页的package信息，APP调起用户确认收款或JSAPI调起用户确认收款需要使用的参数。*/
    @TableField("package_info")
    @ApiModelProperty(value = "跳转领取页面的package信息")
    private String packageInfo;

}
