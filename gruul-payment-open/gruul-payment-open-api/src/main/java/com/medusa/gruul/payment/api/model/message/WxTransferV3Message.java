package com.medusa.gruul.payment.api.model.message;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 微信转账消息体
 * @Date: Created in 15:42 2025/4/22
 */
@Data
public class WxTransferV3Message {

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "商户AppID")
    private String appid;

    @ApiModelProperty(value = "商户单号")
    private String outBillNo;

    @ApiModelProperty(value = "转账场景ID")
    private String transferSceneId;

    @ApiModelProperty(value = "收款用户OpenID")
    private String openid;


    @ApiModelProperty(value = "收款用户OpenID")
    private String userName;

    @ApiModelProperty(value = "转账金额")
    private Integer transferAmount;

    @ApiModelProperty(value = "通知地址")
    private String notifyUrl;

    @ApiModelProperty(value = "转账备注")
    private String transferRemark;

    @ApiModelProperty(value = "用户收款感知")
    private String userRecvPerception;

    @ApiModelProperty(value = "微信转账场景消息")
    private List<WxTransferSceneV3Message>list;

    @ApiModelProperty(value = "微信支付商户号")
    private String mchId;

}
