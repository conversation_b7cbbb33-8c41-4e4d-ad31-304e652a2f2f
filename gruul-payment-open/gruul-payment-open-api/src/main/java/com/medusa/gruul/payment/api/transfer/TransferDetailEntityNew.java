package com.medusa.gruul.payment.api.transfer;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 商户单号查询转账单实体类信息
 * @Date: Created in 15:31 2025/4/22
 */
@Data
public class TransferDetailEntityNew {

    /** 商户号 Y 说明：微信支付分配的商户号 */
    @SerializedName("mch_id")
    private String mchId;

    /** 商户单号 Y 说明：商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一 */
    @SerializedName("out_bill_no")
    private String outBillNo;

    /** 商家转账订单号 Y 说明：商家转账订单的主键，唯一定义此资源的标识 */
    @SerializedName("transfer_bill_no")
    private String transferBillNo;

    /** 商户appid Y 说明：申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid） */
    @SerializedName("appid")
    private String appid;

    /** 单据状态 Y 说明：单据状态  */
    @SerializedName("state")
    private String state;

    /** 转账金额 Y 说明：转账金额单位为“分”。*/
    @SerializedName("transfer_amount")
    private Integer transferAmount;

    /** 转账备注 Y 说明：转账备注，用户收款时可见该备注信息，UTF8编码，最多允许32个字符。*/
    @SerializedName("transfer_remark")
    private String transferRemark;

    /** 失败原因 N 说明：订单已失败或者已退资金时，返回失败原因。 */
    @SerializedName("fail_reason")
    private String failReason;

    /** 收款用户OpenID Y 说明：商户AppID下，某用户的OpenID  */
    @SerializedName("openid")
    private String openid;

    /** 收款用户姓名 N 说明：收款方真实姓名。需要加密传入，支持标准RSA算法和国密算法，公钥由微信侧提供。
     转账金额 >= 2,000元时，该笔明细必须填写
     若商户传入收款用户姓名，微信支付会校验收款用户与输入姓名是否一致，并提供电子回单 */
    @SerializedName("user_name")
    private String userName;

    /** 单据创建时间 N 说明：单据受理成功时返回，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE */
    @SerializedName("create_time")
    private String createTime;

    /** 最后一次状态变更时间 N 说明：单据最后更新时间，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE */
    @SerializedName("update_time")
    private String updateTime;

}
