package com.medusa.gruul.payment.api.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:55 2025/6/13
 */
@Data
@ApiModel(value = "微信退款消息", description = "微信退款message")
public class RefundRequestWechatMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * orderId
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * routeKey
     */
    @ApiModelProperty(value = "路由键,路由键和回调url必须选一个")
    private String routeKey;

    /**
     * totalFee
     */
    @ApiModelProperty(value = "退款总金额")
    private Integer totalFee;

}
