package com.medusa.gruul.payment.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:19 2024/5/14
 */
@Data
public class PaymentWechatDto {

    /**
     * 交易状态：1（交易创建，等待买家付款）、2（未付款交易超时关闭）、3（交易支付成功）
     */
    @ApiModelProperty(value = "交易状态：1（交易创建，等待买家付款）、2（未付款交易超时关闭）、3（交易支付成功）")
    private Integer tradeStatus;

    /**
     * 用户openId
     */
    @ApiModelProperty(value = "用户openId")
    private String openId;
}