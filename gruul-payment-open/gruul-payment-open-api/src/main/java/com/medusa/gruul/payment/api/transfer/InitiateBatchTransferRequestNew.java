package com.medusa.gruul.payment.api.transfer;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 商家零钱转账自定义请求体
 * @Date: Created in 15:35 2025/4/22
 */
@Data
public class InitiateBatchTransferRequestNew {

    /** 商户appid Y 说明：申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid） */
    @SerializedName("appid")
    private String appid;

    /** 商户单号 Y 说明：商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一 */
    @SerializedName("out_bill_no")
    private String outBillNo;

    /** 转账场景ID Y 说明：该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1001-现金营销 */
    @SerializedName("transfer_scene_id")
    private String transferSceneId;

    /** 收款用户OpenID Y 说明：商户AppID下，某用户的OpenID  */
    @SerializedName("openid")
    private String openid;

    /** 收款用户姓名 N 说明：收款方真实姓名。需要加密传入，支持标准RSA算法和国密算法，公钥由微信侧提供。
     转账金额 >= 2,000元时，该笔明细必须填写
     若商户传入收款用户姓名，微信支付会校验收款用户与输入姓名是否一致，并提供电子回单 */
    @SerializedName("user_name")
    private String userName;

    /** 转账金额 Y 说明：转账金额单位为“分”。*/
    @SerializedName("transfer_amount")
    private Integer transferAmount;

    /** 转账备注 Y 说明：转账备注，用户收款时可见该备注信息，UTF8编码，最多允许32个字符。*/
    @SerializedName("transfer_remark")
    private String transferRemark;

    /** 通知地址 N 说明：异步接收微信支付结果通知的回调地址，通知url必须为公网可访问的url，必须为https，不能携带参数。 */
    @SerializedName("notify_url")
    private String notifyUrl;

    /** 转账场景报备信息 Y 说明：各转账场景下需报备的内容，可通过 产品文档 了解 */
    @SerializedName("transfer_scene_report_infos")
    private List<TransferSceneReportInfoNew> transferSceneReportInfos;


}
