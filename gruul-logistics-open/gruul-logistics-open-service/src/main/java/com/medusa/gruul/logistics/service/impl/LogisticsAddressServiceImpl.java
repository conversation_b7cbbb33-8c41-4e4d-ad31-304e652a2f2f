package com.medusa.gruul.logistics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.PriceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.logistics.api.entity.LogisticsAddress;
import com.medusa.gruul.logistics.api.entity.LogisticsCompany;
import com.medusa.gruul.logistics.api.entity.LogisticsExpress;
import com.medusa.gruul.logistics.api.entity.LogisticsShop;
import com.medusa.gruul.logistics.mapper.*;
import com.medusa.gruul.logistics.model.dto.manager.*;
import com.medusa.gruul.logistics.model.dto.manager.express.ExpressInfoDto;
import com.medusa.gruul.logistics.model.enums.*;
import com.medusa.gruul.logistics.model.param.LogisticsAddressParam;
import com.medusa.gruul.logistics.model.param.LogisticsExpressPrintParam;
import com.medusa.gruul.logistics.model.vo.*;
import com.medusa.gruul.logistics.mq.Sender;
import com.medusa.gruul.logistics.service.ILogisticsAddressService;
import com.medusa.gruul.logistics.util.express.kuaidihelp.KuaiDiHelp;
import com.medusa.gruul.logistics.util.express.sf.SFExpressUtil;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.PayTypeEnum;
import com.medusa.gruul.order.api.enums.WxDeliverStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.payment.api.constant.StatusConstant;
import com.medusa.gruul.payment.api.entity.Payment;
import com.medusa.gruul.payment.api.feign.RemotePaymentService;
import com.medusa.gruul.payment.api.model.dto.PaymentWechatDto;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.vo.MiniInfoVo;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收发货地址管理业务层
 * <AUTHOR>
 */
@Service
@Slf4j
public class LogisticsAddressServiceImpl implements ILogisticsAddressService {

    @Autowired
    private LogisticsAddressMapper logisticsAddressMapper;
    @Resource
    private LogisticsShopMapper logisticsShopMapper;
    @Resource
    private LogisticsCompanyMapper logisticsCompanyMapper;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Autowired
    private LogisticsExpressPrintMapper logisticsExpressPrintMapper;
    @Autowired
    private LogisticsExpressAddressMapper logisticsExpressAddressMapper;
    @Autowired
    private LogisticsExpressMapper logisticsExpressMapper;

    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private RemotePaymentService remotePaymentService;

    @Value("${wx.deliverGoods}")
    private String deliverGoodsUrl;

    @Value("${wx.accessToken}")
    private String accessTokenUrl;

    @Value("${wx.deliverGoodsStatus}")
    private String deliverGoodsStatusUrl;
    @Autowired
    private Sender sender;
    /**
     * 查询收发货地址列表
     *
     * @param logisticsAddressParam
     * @return IPage<LogisticsAddressVo>
     */
    @Override
    public IPage<LogisticsAddressVo> getAddressList(LogisticsAddressParam logisticsAddressParam) {
        IPage<LogisticsAddressVo> page = new Page<>(logisticsAddressParam.getCurrent(),
                logisticsAddressParam.getSize());
        List<LogisticsAddressVo> logisticsAddressVos = this.logisticsAddressMapper
                .queryLogisticsAddressList(page, logisticsAddressParam);
        return page.setRecords(logisticsAddressVos);
    }

    /**
     * 查询所有地址
     *
     * @return List<LogisticsAddressVo>
     */
    @Override
    public List<LogisticsAddressVo> getAllAddressList() {
        List<LogisticsAddressVo> logisticsAddressVos = this.logisticsAddressMapper.queryAllLogisticsAddressList();
        return logisticsAddressVos;
    }

    /**
     * 更新/新增 地址
     *
     * @param logisticsAddressDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAddress(LogisticsAddressDto logisticsAddressDto) {
        // 新增,修改地址
        if (logisticsAddressDto.getId() == null) {
            //验证是否有一模一样的地址信息 全信息匹配
            int logisticsAddressSearch = this.logisticsAddressMapper.selectCount(
                    new QueryWrapper<LogisticsAddress>().eq("name", logisticsAddressDto.getName())
                            .eq("province_id", logisticsAddressDto.getProvinceId())
                            .eq("city_id", logisticsAddressDto.getCityId())
                            .eq("country_id", logisticsAddressDto.getCountryId())
                            .eq("address", logisticsAddressDto.getAddress())
                            .eq("zip_code", logisticsAddressDto.getZipCode())
                            .eq("phone", logisticsAddressDto.getPhone()));
            if (logisticsAddressSearch > 0) {
                throw new ServiceException("有相同信息的地址！", SystemCode.DATA_ADD_FAILED.getCode());
            }
            //新增 先判断是否有数据  没有的话设置为默认收发货地址
            int count = this.logisticsAddressMapper.selectCount(new QueryWrapper<>());
            LogisticsAddress logisticsAddress = logisticsAddressDto.coverBean();
            if (count == 0) {
                logisticsAddress.setDefSend(AddressDefaultEnum.YES.getAddressDefault());
                logisticsAddress.setDefReceive(AddressDefaultEnum.YES.getAddressDefault());
            }
            int insert = this.logisticsAddressMapper.insert(logisticsAddress);
            if (insert == 0) {
                throw new ServiceException("地址新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
        } else {
            //修改
            LogisticsAddress logisticsAddress = this.logisticsAddressMapper.selectById(logisticsAddressDto.getId());
            if (BeanUtil.isEmpty(logisticsAddress)) {
                throw new ServiceException("地址不存在！", SystemCode.DATA_EXISTED.getCode());
            }
            //验证是否有一模一样的地址信息 全信息匹配
            int logisticsAddressSearch = this.logisticsAddressMapper.selectCount(
                    new QueryWrapper<LogisticsAddress>().eq("name", logisticsAddressDto.getName())
                            .eq("province_id", logisticsAddressDto.getProvinceId())
                            .eq("city_id", logisticsAddressDto.getCityId())
                            .eq("country_id", logisticsAddressDto.getCountryId())
                            .eq("address", logisticsAddressDto.getAddress())
                            .eq("zip_code", logisticsAddressDto.getZipCode())
                            .eq("phone", logisticsAddressDto.getPhone()).ne("id", logisticsAddressDto.getId()));
            if (logisticsAddressSearch > 0) {
                throw new ServiceException("有相同信息的地址！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
            logisticsAddress.setName(logisticsAddressDto.getName());
            logisticsAddress.setAddress(logisticsAddressDto.getAddress());
            logisticsAddress.setPhone(logisticsAddressDto.getPhone());
            logisticsAddress.setZipCode(logisticsAddressDto.getZipCode());
            logisticsAddress.setProvince(logisticsAddressDto.getProvince());
            logisticsAddress.setProvinceId(logisticsAddressDto.getProvinceId());
            logisticsAddress.setCity(logisticsAddressDto.getCity());
            logisticsAddress.setCityId(logisticsAddressDto.getCityId());
            logisticsAddress.setCountry(logisticsAddressDto.getCountry());
            logisticsAddress.setCountryId(logisticsAddressDto.getCountryId());
            int update = this.logisticsAddressMapper.updateById(logisticsAddress);
            if (update == 0) {
                throw new ServiceException("地址修改失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        }
    }

    /**
     * 设置 默认地址
     *
     * @param type 1-发货地址 2-收货地址
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefAddress(Integer type, Long id) {
        if (type == AddressTypeEnum.DEFSEND.getAddressType()) {
            //说明是设置发货地址 先去除其他默认的发货地址
            boolean updateSign = new LambdaUpdateChainWrapper<>(logisticsAddressMapper)
                    .eq(LogisticsAddress::getDefSend, AddressDefaultEnum.YES.getAddressDefault())
                    .set(LogisticsAddress::getDefSend, AddressDefaultEnum.NO.getAddressDefault()).update();
            if (!updateSign) {
                throw new ServiceException("设置默认发货地址失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            } else {
                //设置为发货地址
                boolean sign = new LambdaUpdateChainWrapper<>(logisticsAddressMapper).eq(LogisticsAddress::getId, id)
                        .set(LogisticsAddress::getDefSend, AddressDefaultEnum.YES.getAddressDefault()).update();
                if (!sign) {
                    throw new ServiceException("设置默认发货地址失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
                }
            }
        } else {
            //说明是设置收货地址 先去除其他默认的收货地址
            boolean updateSign = new LambdaUpdateChainWrapper<>(logisticsAddressMapper)
                    .eq(LogisticsAddress::getDefReceive, AddressDefaultEnum.YES.getAddressDefault())
                    .set(LogisticsAddress::getDefReceive, AddressDefaultEnum.NO.getAddressDefault()).update();
            if (!updateSign) {
                throw new ServiceException("设置默认发货地址失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            } else {
                //设置为收货地址
                boolean sign = new LambdaUpdateChainWrapper<>(logisticsAddressMapper).eq(LogisticsAddress::getId, id)
                        .set(LogisticsAddress::getDefReceive, AddressDefaultEnum.YES.getAddressDefault()).update();
                if (!sign) {
                    throw new ServiceException("设置默认收货地址失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
                }
            }

        }

    }

    /**
     * 删除地址
     *
     * @param id 主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAddress(Long id) {
        LogisticsAddress logisticsAddress = this.logisticsAddressMapper.selectById(id);
        if (BeanUtil.isEmpty(logisticsAddress)) {
            throw new ServiceException("地址不存在！", SystemCode.DATA_EXISTED.getCode());
        } else {
            //判断是否为默认收/发货地址 是的话不允许删除
            if (AddressDefaultEnum.YES.getAddressDefault() == logisticsAddress.getDefReceive()
                    || AddressDefaultEnum.YES.getAddressDefault() == logisticsAddress.getDefSend()) {
                throw new ServiceException("默认地址不能删除！", SystemCode.DATA_DELETE_FAILED.getCode());
            } else {
                int delete = this.logisticsAddressMapper.deleteById(id);
                if (delete == 0) {
                    throw new ServiceException("地址删除失败！", SystemCode.DATA_DELETE_FAILED.getCode());
                }
            }
        }
    }

    /**
     * @param type 收发货类型 1-发货地址 2-收货地址
     *             获取默认收/发货地址
     */
    @Override
    public LogisticsAddressVo getDefaultAddress(Integer type) {
        try {
            LogisticsAddressVo logisticsAddress = this.logisticsAddressMapper.queryDefaultAddress(type);
            if (BeanUtil.isEmpty(logisticsAddress)) {
                throw new ServiceException("暂无发货地址,请添加！", SystemCode.DATA_DELETE_FAILED.getCode());
            }
            return logisticsAddress;
        } catch (Exception e) {
            throw new ServiceException("发货地址有误,请核对发货地址！", SystemCode.DATA_DELETE_FAILED.getCode());
        }
    }

    /**
     * 获取打印机与快递公司信息、发货地址
     */
    @Override
    public Map<String, Object> listLogisticsCompany(Long orderId) {
        String shopId = ShopContextHolder.getShopId();
        if(orderId!=null){
            Order order = remoteOrderService.getOrderById(orderId);
            ShopContextHolder.setShopId(order.getShopId());
        }

        Map<String, Object> res = new HashMap<>(CommonConstants.NUMBER_TWO);
        Map<String, Object> param = new HashMap<>(CommonConstants.NUMBER_THREE);
        List<LogisticsCompany> logisticsCompanies = logisticsCompanyMapper.selectListCompany(param);
        List<LogisticsCompanyVo> logisticsCompanyVos = new ArrayList<>(logisticsCompanies.size());
        LogisticsShop logisticsShop = logisticsShopMapper
                .selectOne(new QueryWrapper<LogisticsShop>().eq("is_default", 1));
        if (CollectionUtil.isNotEmpty(logisticsCompanies)) {
            logisticsCompanies.forEach(logisticsCompany -> {
                LogisticsCompanyVo logisticsCompanyVo = new LogisticsCompanyVo();
                logisticsCompanyVo.setCode(logisticsCompany.getCode());
                logisticsCompanyVo.setName(logisticsCompany.getName());
                logisticsCompanyVo.setIsDefault(CommonConstants.NUMBER_ZERO);
                logisticsCompanyVo.setLogisticsCompanyId(logisticsCompany.getId());
                if ((null != logisticsShop) && (logisticsCompany.getId()
                        .equals(logisticsShop.getLogisticsCompanyId()))) {
                    logisticsCompanyVo.setIsDefault(CommonConstants.NUMBER_ONE);
                }
                List<LogisticsExpressAddressVo> logisticsExpressAddressVos = logisticsExpressAddressMapper
                        .queryByExpressCode(logisticsCompany.getCode());
                logisticsCompanyVo.setLogisticsExpressAddressVos(logisticsExpressAddressVos);
                logisticsCompanyVos.add(logisticsCompanyVo);
            });
        }
        //打印机列表
        List<LogisticsExpressPrintVo> logisticsExpressPrintVos = this.logisticsExpressPrintMapper
                .queryLogisticsExpressPrintList(null, new LogisticsExpressPrintParam());
        //地址列表
        List<LogisticsAddressVo> logisticsAddressVos = this.logisticsAddressMapper.queryAllLogisticsAddressList();

        res.put("LogisticsPrinterVos", logisticsExpressPrintVos);
        res.put("logisticsCompanyVos", logisticsCompanyVos);
        res.put("logisticsAddressVos", logisticsAddressVos);
        ShopContextHolder.setShopId(shopId);
        return res;
    }

    /**
     * 设置默认打印快递公司
     *
     * @param logisticsCompanyId
     */
    @Override
    public void setCompanyDefault(Long logisticsCompanyId) {
        LogisticsShop logisticsShop = logisticsShopMapper
                .selectOne(new QueryWrapper<LogisticsShop>().eq("is_default", CommonConstants.NUMBER_ONE));
        if (BeanUtil.isEmpty(logisticsShop)) {
            logisticsShop = new LogisticsShop();
            logisticsShop.setIsDefault(CommonConstants.NUMBER_ONE);
            logisticsShop.setLogisticsCompanyId(logisticsCompanyId);
            logisticsShop.setCreateTime(DateUtils.timestampCoverLocalDateTime(System.currentTimeMillis()));
            int insert = logisticsShopMapper.insert(logisticsShop);
            if (insert == 0) {
                throw new ServiceException("设置失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
        } else {
            logisticsShop.setUpdateTime(DateUtils.timestampCoverLocalDateTime(System.currentTimeMillis()));
            logisticsShop.setLogisticsCompanyId(logisticsCompanyId);
            int update = logisticsShopMapper.updateById(logisticsShop);
            if (update == 0) {
                throw new ServiceException("设置失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        }
    }
    @Override
    public void doDeliverGoodsAgain(Long orderId,String printCode) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Long>orderIds = new ArrayList<>();
        orderIds.add(orderId);
        OrderVo orderVo = remoteOrderService.orderInfo(orderId);
        Long warehouseId = orderVo.getWarehouseId();
        List<ManageOrderDeliveryVo> orderDeliveryList = orderVo.getOrderDeliveryList();
        for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
            Integer deliveryType = manageOrderDeliveryVo.getDeliveryType().getCode();
            String deliveryCode = manageOrderDeliveryVo.getDeliveryCode();
            OrderSetting orderSetting = remoteOrderService.getOrderSetting();
            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                if (StrUtil.isEmpty(orderSetting.getKdAppKey())) {
                    throw new ServiceException("请先设置快宝KEY");
                }
                if (StrUtil.isEmpty(orderSetting.getKdAppId())) {
                    throw new ServiceException("请先设置快宝ID");
                }
            }
            LogisticsCompany logisticsCompany = null;
            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                Map<String, Object> param = new HashMap<>(CommonConstants.NUMBER_THREE);
                param.put("code", deliveryCode);
                logisticsCompany = logisticsCompanyMapper.selectListCompanyByParam(param);
                if (null == logisticsCompany) {
                    throw new ServiceException("发货物流公司已下架");
                }
            }
            LogisticsCompany finalLogisticsCompany = logisticsCompany;        OrderDeliveryDto orderDeliveryDto = new OrderDeliveryDto();
            orderDeliveryDto.setOrderId(orderId);
            orderDeliveryDto.setDeliveryType(deliveryType);
            LogisticsPrintDeliverDto logisticsPrintDeliverDto = new LogisticsPrintDeliverDto();
            logisticsPrintDeliverDto.setDeliveryType(deliveryType);
            logisticsPrintDeliverDto.setDeliverCode(finalLogisticsCompany.getCode());
            logisticsPrintDeliverDto.setWarehouseId(warehouseId);
            logisticsPrintDeliverDto.setOrderIds(orderIds);
            logisticsPrintDeliverDto.setPrintCode(printCode);
            /**
             * 获取物流公司发货地址设置
             */
            LambdaQueryWrapper<LogisticsExpress>lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(LogisticsExpress::getCode,deliveryCode);
            List<LogisticsExpress> logisticsExpressList = logisticsExpressMapper.selectList(lambdaQueryWrapper);
            if(logisticsExpressList!=null&&logisticsExpressList.size()>0){
                LogisticsExpress logisticsExpress = logisticsExpressList.get(0);
                Long addressId = logisticsExpress.getAddressId();
                Long expressId = logisticsExpress.getId();
                logisticsPrintDeliverDto.setAddressId(addressId);
                logisticsPrintDeliverDto.setExpressId(expressId);
            }
            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                orderDeliveryDto.setDeliveryCompany(finalLogisticsCompany.getName());
                orderDeliveryDto.setDeliveryCode(finalLogisticsCompany.getCode());
                String resultList = getExpressInfoDto(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), orderId,new ArrayList<>());
                Map<String, String> resultMap = (Map<String, String>) JSON.parse(resultList);
                if (MapUtil.isEmpty(resultMap)) {
                    throw new ServiceException("快递单号生成失败！");
                }
                orderDeliveryDto.setDeliverySn(resultMap.get("waybill_no"));
                orderDeliveryDto.setDeliverySnShow(resultMap.get("waybill_no"));
                orderDeliveryDto.setSortingCode(resultMap.get("sorting_code"));
                orderDeliveryDto.setPackageName(resultMap.get("package_name"));
                orderDeliveryDto.setPackageCode(resultMap.get("package_code"));
                RoutingInfoVo routingInfoVo = new RoutingInfoVo();
                routingInfoVo.setWaybillCode(resultMap.get("waybill_no"));
                routingInfoVo.setSortingCode(resultMap.get("sorting_code"));
                routingInfoVo.setPackageName(resultMap.get("package_name"));
                routingInfoVo.setPackageCode(resultMap.get("package_code"));
                //打印物流单号
                expressPrint(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), routingInfoVo, orderId,new ArrayList<>());

                //更新打印次数
                remoteOrderService.updateOrderPrintTime(orderId);
                //更新物流单号（显示）
                remoteOrderService.updateOrderDeliveryDeliverySnShow(orderDeliveryDto);
            }
            ShopContextHolder.setShopId(oldShopId);
        }
        //OrderDelivery orderDelivery = orderVo.getOrderDelivery();

    }
    /**
     * 批量快递订单打印并发货 (通知订单服务修改状态)
     *
     * @param logisticsPrintDeliverBatchDtoList 打印并发货dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doBatchPrintDeliverGoods(List<LogisticsPrintDeliverBatchDto> logisticsPrintDeliverBatchDtoList) {
        OrderSetting orderSetting = remoteOrderService.getOrderSetting();


        List<OrderDeliveryDto> orderDeliveryDtos = new ArrayList<>();

        if(logisticsPrintDeliverBatchDtoList!=null&&logisticsPrintDeliverBatchDtoList.size()>0){
            List<LogisticsPrintDeliverBatchDto> dataList = logisticsPrintDeliverBatchDtoList.stream().filter(e -> e.getOrderDeLiveryProxyId() != null
                    && !e.getOrderDeLiveryProxyId().equals("")).collect(Collectors.toList());

            if(dataList!=null&&dataList.size()>0){
                List<OrderItemDeliveryDto> orderItemDeliveryList = new ArrayList<>();
                for (LogisticsPrintDeliverBatchDto logisticsPrintDeliverBatchDto : dataList) {
                    orderItemDeliveryList.addAll(logisticsPrintDeliverBatchDto.getOrderItemDeliveryList());
                }
                if(orderItemDeliveryList!=null&&orderItemDeliveryList.size()>0){
                    Map<Long, List<OrderItemDeliveryDto>> detailList = orderItemDeliveryList.stream().collect(Collectors.groupingBy(OrderItemDeliveryDto::getOrderDeliveryProxyItemId));
                    for (Map.Entry<Long, List<OrderItemDeliveryDto>> entry : detailList.entrySet()) {
                        OrderDeliveryProxyItem orderDeliveryProxyItem = remoteOrderService.getOrderDeliveryProxyItem(entry.getKey());
                        Integer unDeliveryQuantity = orderDeliveryProxyItem.getUnDeliveryQuantity();
                        Integer deliveryQuantity = 0;
                        for (OrderItemDeliveryDto orderItemDeliveryDto : entry.getValue()) {
                            deliveryQuantity = deliveryQuantity + orderItemDeliveryDto.getDeliveryQuantity();
                        }
                        if(deliveryQuantity>unDeliveryQuantity){
                            throw new ServiceException(orderDeliveryProxyItem.getProductName()+"发货数量大于待发货数量");
                        }
                    }
                }
            }



        }

        for (LogisticsPrintDeliverBatchDto logisticsPrintDeliverBatchDto : logisticsPrintDeliverBatchDtoList) {

            List<OrderItemDeliveryDto> orderItemDeliveryList = logisticsPrintDeliverBatchDto.getOrderItemDeliveryList();

            if(orderItemDeliveryList==null||orderItemDeliveryList.size()==0){
                throw new ServiceException("订单发货详情不能为空");
            }


            int deliveryType = logisticsPrintDeliverBatchDto.getDeliveryType();
            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                if (StrUtil.isEmpty(orderSetting.getKdAppKey())) {
                    throw new ServiceException("请先设置快宝KEY");
                }
                if (StrUtil.isEmpty(orderSetting.getKdAppId())) {
                    throw new ServiceException("请先设置快宝ID");
                }
            }



            LogisticsCompany logisticsCompany = null;
            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                Map<String, Object> param = new HashMap<>(CommonConstants.NUMBER_THREE);
                param.put("code", logisticsPrintDeliverBatchDto.getDeliverCode());
                logisticsCompany = logisticsCompanyMapper.selectListCompanyByParam(param);
                if (null == logisticsCompany) {
                    throw new ServiceException("发货物流公司已下架");
                }
            }
            Long orderId = logisticsPrintDeliverBatchDto.getOrderId();
            if (orderId==null) {
                throw new ServiceException("订单id不存在");
            }

            String oldShopId = ShopContextHolder.getShopId();
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);

            Map<Long,List <ProductStock>> productStockMap=new HashMap();
            //订单总数量
            Map<Long,Integer> productStockNumberMap=new HashMap();
            //查询库存
            List <ProductStock> stockList=new ArrayList<>();

            Map orderItemMap=new HashMap();
            List<Long> skuIds=new ArrayList<>();
            List<Long> linkSkuIds=new ArrayList<>();
            List<OrderItem>orderItemList = new ArrayList<>();
            List<OrderDeliveryItemDto>orderDeliveryItemList = new ArrayList<>();

            for (OrderItemDeliveryDto orderItemDeliveryDto : orderItemDeliveryList) {
                Long id = orderItemDeliveryDto.getId();
                if(id == null || id.equals("")){
                    throw new ServiceException("订单明细id不能为空！");
                }
                OrderItem orderItem = remoteOrderService.getOrderItemById(id);
                if(orderItem == null || orderItem.equals("")){
                    throw new ServiceException("订单明细不存在！");
                }
                Integer deliveryQuantity = orderItemDeliveryDto.getDeliveryQuantity();
                if(deliveryQuantity == null || deliveryQuantity.equals("") || deliveryQuantity == 0){
                    throw new ServiceException("订单明细发货数量必须大于0！");
                }
                Integer unDeliveryQuantity = orderItem.getUnDeliveryQuantity();
                String productName = orderItem.getProductName();
                if(deliveryQuantity>unDeliveryQuantity){
                    throw new ServiceException(productName+"商品发货数量已大于待发货数量！");
                }





                skuIds.add(orderItem.getProductSkuId());
                if(orderItem.getLinkSkuId()!=null){
                    linkSkuIds.add(orderItem.getLinkSkuId());
                }
                orderItemList.add(orderItem);

                OrderDeliveryItemDto orderDeliveryItemDto = new OrderDeliveryItemDto();
                orderDeliveryItemDto.setOrderItemId(orderItem.getId());
                orderDeliveryItemDto.setProductId(orderItem.getProductId());
                orderDeliveryItemDto.setProductName(orderItem.getProductName());
                orderDeliveryItemDto.setProductPic(orderItem.getProductPic());
                orderDeliveryItemDto.setProductQuantity(orderItemDeliveryDto.getDeliveryQuantity());
                orderDeliveryItemDto.setProductSkuCode(orderItem.getProductSkuCode());
                orderDeliveryItemDto.setProductSkuId(orderItem.getProductSkuId());
                orderDeliveryItemDto.setOrderDeliveryProxyItemId(orderItemDeliveryDto.getOrderDeliveryProxyItemId());
                orderDeliveryItemDto.setOrderDeLiveryProxyId(orderItemDeliveryDto.getOrderDeLiveryProxyId());
                orderItemMap.put(orderItem.getProductSkuId()+":"+orderItem.getPriceType(),orderDeliveryItemDto);

                orderDeliveryItemList.add(orderDeliveryItemDto);
            }

            List<ProductStock> productStockList=remoteGoodsService.productStockBySkuIds(skuIds,logisticsPrintDeliverBatchDto.getWarehouseId());
            List<ProductStock> linkProductStockList=remoteGoodsService.productStockBySkuIds(linkSkuIds,logisticsPrintDeliverBatchDto.getWarehouseId());
            if(CollectionUtils.isEmpty(productStockList)){
                throw new ServiceException("库存数量不足，发货失败！");
            }
            if(CollectionUtil.isNotEmpty(linkSkuIds) && (CollectionUtils.isEmpty(linkProductStockList) || linkProductStockList.size()!=productStockList.size())){
                throw new ServiceException("关联商品库存数量不足，发货失败！");
            }

            for(int j=0;j<productStockList.size();j++){
                ProductStock productStock=productStockList.get(j);
                //库存数据
                BigDecimal stock=productStock.getStock();
                Integer quantity=0;
                Integer num = 0;
                if(productStockNumberMap.get(productStock.getSkuId())==null){
                    productStockNumberMap.put(productStock.getSkuId(),0);
                }
                for(int i = 0 ;i<PriceTypeEnum.values().length;i++){

                    OrderDeliveryItemDto orderDeliveryItemDto = (OrderDeliveryItemDto)orderItemMap.get(productStock.getSkuId()+":"+(i+1));
                    //订单数量
                    if(orderDeliveryItemDto!=null){
                        quantity=orderDeliveryItemDto.getProductQuantity()+quantity;
                        num=productStockNumberMap.get(orderDeliveryItemDto.getProductSkuId())+quantity;
                    }
                }
                //累计订单数量
                productStockNumberMap.put(productStock.getSkuId(),num);
                //剩余数量
                BigDecimal number =stock.subtract(new BigDecimal(num));
                productStock.setStock(number);
                stockList.add(productStock);
                if(number.compareTo(new BigDecimal("0")) < 0){
                    ProductVo productVo = remoteGoodsService.findProductById(productStock.getProductId());
                    throw new ServiceException(productVo.getName() + "库存数量不足，发货失败！");
                }
                if(CollectionUtil.isNotEmpty(linkProductStockList)){
                    ProductStock linkProductStock=linkProductStockList.get(j);
                    //库存数据
                    BigDecimal linkStock=linkProductStock.getStock();
                    //剩余数量
                    BigDecimal linkNumber =linkStock.subtract(new BigDecimal(num));
                    linkProductStock.setStock(linkNumber);
                    stockList.add(linkProductStock);
                    if(linkNumber.compareTo(new BigDecimal("0")) <= 0){
                        ProductVo productVo = remoteGoodsService.findProductById(linkProductStock.getProductId());
                        throw new ServiceException("关联商品【" + productVo.getName() + "】库存数量不足，发货失败！");
                    }
                }
            }
            productStockMap.put(orderId,stockList);
            LogisticsCompany finalLogisticsCompany = logisticsCompany;
            OrderDeliveryDto orderDeliveryDto = new OrderDeliveryDto();
            orderDeliveryDto.setOrderId(orderId);
            orderDeliveryDto.setDeliveryType(deliveryType);
            orderDeliveryDto.setOrderDeLiveryProxyId(logisticsPrintDeliverBatchDto.getOrderDeLiveryProxyId());
            orderDeliveryDto.setReceiverName(logisticsPrintDeliverBatchDto.getReceiverName());
            orderDeliveryDto.setReceiverPhone(logisticsPrintDeliverBatchDto.getReceiverPhone());
            orderDeliveryDto.setReceiverPostCode(logisticsPrintDeliverBatchDto.getReceiverPostCode());
            orderDeliveryDto.setReceiverProvince(logisticsPrintDeliverBatchDto.getReceiverProvince());
            orderDeliveryDto.setReceiverCity(logisticsPrintDeliverBatchDto.getReceiverCity());
            orderDeliveryDto.setReceiverRegion(logisticsPrintDeliverBatchDto.getReceiverRegion());
            orderDeliveryDto.setReceiverDetailAddress(logisticsPrintDeliverBatchDto.getReceiverDetailAddress());

            LogisticsPrintDeliverDto logisticsPrintDeliverDto = new LogisticsPrintDeliverDto();
            logisticsPrintDeliverDto.setExpressId(logisticsPrintDeliverBatchDto.getExpressId());
            logisticsPrintDeliverDto.setDeliverCode(logisticsPrintDeliverBatchDto.getDeliverCode());
            logisticsPrintDeliverDto.setPrintCode(logisticsPrintDeliverBatchDto.getPrintCode());
            logisticsPrintDeliverDto.setAddressId(logisticsPrintDeliverBatchDto.getAddressId());
            logisticsPrintDeliverDto.setWarehouseId(logisticsPrintDeliverBatchDto.getWarehouseId());
            logisticsPrintDeliverDto.setDeliveryType(logisticsPrintDeliverBatchDto.getDeliveryType());
            logisticsPrintDeliverDto.setReceiverName(logisticsPrintDeliverBatchDto.getReceiverName());
            logisticsPrintDeliverDto.setReceiverPhone(logisticsPrintDeliverBatchDto.getReceiverPhone());
            logisticsPrintDeliverDto.setReceiverPostCode(logisticsPrintDeliverBatchDto.getReceiverPostCode());
            logisticsPrintDeliverDto.setReceiverProvince(logisticsPrintDeliverBatchDto.getReceiverProvince());
            logisticsPrintDeliverDto.setReceiverCity(logisticsPrintDeliverBatchDto.getReceiverCity());
            logisticsPrintDeliverDto.setReceiverRegion(logisticsPrintDeliverBatchDto.getReceiverRegion());
            logisticsPrintDeliverDto.setReceiverDetailAddress(logisticsPrintDeliverBatchDto.getReceiverDetailAddress());


            if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
                //物流配送选择选择的打印机，物流公司
                orderDeliveryDto.setDeliveryCompany(finalLogisticsCompany.getName());
                orderDeliveryDto.setDeliveryCode(finalLogisticsCompany.getCode());
                String resultList = getExpressInfoDto(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), orderId,orderDeliveryItemList);
                Map<String, String> resultMap = (Map<String, String>) JSON.parse(resultList);
                if (MapUtil.isEmpty(resultMap)) {
                    throw new ServiceException("快递单号生成失败！");
                }
                orderDeliveryDto.setDeliverySn(resultMap.get("waybill_no"));
                orderDeliveryDto.setDeliverySnShow(resultMap.get("waybill_no"));
                orderDeliveryDto.setSortingCode(resultMap.get("sorting_code"));
                orderDeliveryDto.setPackageName(resultMap.get("package_name"));
                orderDeliveryDto.setPackageCode(resultMap.get("package_code"));
                RoutingInfoVo routingInfoVo = new RoutingInfoVo();
                routingInfoVo.setWaybillCode(resultMap.get("waybill_no"));
                routingInfoVo.setSortingCode(resultMap.get("sorting_code"));
                routingInfoVo.setPackageName(resultMap.get("package_name"));
                routingInfoVo.setPackageCode(resultMap.get("package_code"));
                //打印物流单号
                expressPrint(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), routingInfoVo, orderId,orderDeliveryItemList);
                //更新打印次数
                remoteOrderService.updateOrderPrintTime(orderId);
            }else if(deliveryType == DeliveryTypeEnum.SELF.getType()){
                //自提地址选发货选择的地址
                LogisticsAddress address = logisticsAddressMapper.selectById(logisticsPrintDeliverBatchDto.getAddressId());
                //自提
                orderDeliveryDto.setSelfName(address.getName());
                orderDeliveryDto.setSelfPhone(address.getPhone());
                orderDeliveryDto.setSelfDetailAddress(address.getAddress());
            }
            orderDeliveryDto.setTenantId(TenantContextHolder.getTenantId());
            orderDeliveryDto.setShopId(oldShopId);
            orderDeliveryDto.setItemList(orderDeliveryItemList);
            orderDeliveryDto.setWarehouseId(logisticsPrintDeliverBatchDto.getWarehouseId());

            orderDeliveryDtos.add(orderDeliveryDto);

            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
            RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByAccountId(curUserDto.getUserId());
            //获取订单信息
            Order order = remoteOrderService.getOrderById(orderId);
            if(order.getMallOrderType() == ProductTypeEnum.BASIC_PRODUCT.getStatus() || order.getMallOrderType() == ProductTypeEnum.GROUP_PRODUCT.getStatus()){//普通订单发货生成出库单
                CreateOrderOutStockMessage createOrderOutStockMessage = new CreateOrderOutStockMessage();
                createOrderOutStockMessage.setOrderId(orderId+"");

                createOrderOutStockMessage.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
                createOrderOutStockMessage.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
                createOrderOutStockMessage.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称
                createOrderOutStockMessage.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
                createOrderOutStockMessage.setEmployeeOutId(relationInfoVo.getEmployeeOutId());//职员标识
                createOrderOutStockMessage.setEmployeeName(relationInfoVo.getEmployeeName());//职员名称
                createOrderOutStockMessage.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
                createOrderOutStockMessage.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
                createOrderOutStockMessage.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称
                createOrderOutStockMessage.setAccountId(relationInfoVo.getAccountId());//用户id
                createOrderOutStockMessage.setAccountName(relationInfoVo.getAccountName());//用户名称
                //获取仓库信息
                Warehouse warehouse = remoteGoodsService.getWarehouseById(logisticsPrintDeliverBatchDto.getWarehouseId());
                createOrderOutStockMessage.setStockId(warehouse.getId()+"");//仓库id
                createOrderOutStockMessage.setStockCode(warehouse.getClassCode());//仓库编码
                createOrderOutStockMessage.setStockName(warehouse.getWarehouseFullName());//仓库名称

                createOrderOutStockMessage.setUserId(order.getUserId());
                createOrderOutStockMessage.setSendStatus(0);
                createOrderOutStockMessage.setShopId(order.getShopId());
                createOrderOutStockMessage.setTenantId(order.getTenantId());
                Date date = new Date();
                createOrderOutStockMessage.setOutTime(DateUtil.format(date,"yyyy-MM-dd"));
                List<StockOutOrderDetDto>detDtoList = new ArrayList<>();
                BigDecimal allAmount = BigDecimal.ZERO;

                if(orderItemDeliveryList!=null&&orderItemDeliveryList.size()>0){
                    for (OrderItemDeliveryDto orderItemDeliveryDto : orderItemDeliveryList) {
                        Long id = orderItemDeliveryDto.getId();
                        OrderItem orderItem = remoteOrderService.getOrderItemById(id);
                        Long productId = orderItem.getProductId();
                        ProductVo product = remoteGoodsService.findProductById(productId);
                        StockOutOrderDetDto stockOutOrderDetDto = new StockOutOrderDetDto();
                        stockOutOrderDetDto.setProductId(product.getId()+"");
                        stockOutOrderDetDto.setProductCode(product.getClassCode());
                        stockOutOrderDetDto.setProductName(product.getName());
                        // 查询关联商品id及规格2
                        stockOutOrderDetDto.setSpecs2(orderItem.getSpecs2());
                        if(orderItem.getLinkProductId() != null){
                            ProductVo linkProduct = remoteGoodsService.findProductById(orderItem.getLinkProductId());
                            if(linkProduct != null){
                                stockOutOrderDetDto.setLinkProductId(linkProduct.getId()+"");
                                stockOutOrderDetDto.setLinkProductCode(linkProduct.getClassCode());
                                stockOutOrderDetDto.setLinkProductName(linkProduct.getName());
                            }
                        }

                        stockOutOrderDetDto.setNumber(orderItemDeliveryDto.getDeliveryQuantity());
                        stockOutOrderDetDto.setPrice(orderItem.getProductPrice());
                        BigDecimal qty = BigDecimal.ZERO;
                        BigDecimal price = BigDecimal.ZERO;
                        if(orderItemDeliveryDto.getDeliveryQuantity()!=null){
                            qty = BigDecimal.valueOf(orderItemDeliveryDto.getDeliveryQuantity());
                        }
                        if(orderItem.getProductPrice()!=null){
                            price = orderItem.getProductPrice();
                        }
                        stockOutOrderDetDto.setAmount(qty.multiply(price));
                        allAmount = allAmount.add(stockOutOrderDetDto.getAmount());
                        detDtoList.add(stockOutOrderDetDto);
                    }
                }
                createOrderOutStockMessage.setAllAmount(allAmount);
                createOrderOutStockMessage.setDetList(detDtoList);
                //发送生成出库单消息
                sender.sendOrderStockOutMessage(createOrderOutStockMessage);
            }

            //修改库存
            boolean success=remoteGoodsService.batchProductSubtractStock(productStockMap.get(orderId));

            //boolean fl=remoteOrderService.updateOrderWarehouse(orderId,logisticsPrintDeliverBatchDto.getWarehouseId());

            if(!success){
                throw new ServiceException("修改库存失败");
            }

            //小程序发货信息管理
            Boolean wxOrderDelivery = orderSetting.getWxOrderDelivery();
            if(wxOrderDelivery){
                wxOrderDelivery(orderId,deliveryType,orderDeliveryItemList);
            }
            ShopContextHolder.setShopId(oldShopId);
        }
        //发货
        int i = remoteOrderService.doLogisticsOrderDelivery(JSON.toJSONString(orderDeliveryDtos));
        if (i < CommonConstants.NUMBER_ONE) {
            throw new ServiceException("发货失败");
        }
    }

    /**
     * 快递订单打印并发货 (通知订单服务修改状态)
     *
     * @param logisticsPrintDeliverDto 打印并发货dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPrintDeliverGoods(LogisticsPrintDeliverDto logisticsPrintDeliverDto) {
        int deliveryType = logisticsPrintDeliverDto.getDeliveryType();
        List<OrderItemDeliveryDto> orderItemDeliveryList = logisticsPrintDeliverDto.getOrderItemDeliveryList();

        if(orderItemDeliveryList==null||orderItemDeliveryList.size()==0){
            throw new ServiceException("订单发货详情不能为空");
        }

        OrderSetting orderSetting = remoteOrderService.getOrderSetting();
        if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
            if (StrUtil.isEmpty(orderSetting.getKdAppKey())) {
                throw new ServiceException("请先设置快宝KEY");
            }
            if (StrUtil.isEmpty(orderSetting.getKdAppId())) {
                throw new ServiceException("请先设置快宝ID");
            }
        }

        //把订单状态设置成已发货
        List<OrderDeliveryDto> orderDeliveryDtos = new ArrayList<>(CommonConstants.NUMBER_ONE);

        LogisticsCompany logisticsCompany = null;
        if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
            Map<String, Object> param = new HashMap<>(CommonConstants.NUMBER_THREE);
            param.put("code", logisticsPrintDeliverDto.getDeliverCode());
            logisticsCompany = logisticsCompanyMapper.selectListCompanyByParam(param);
            if (null == logisticsCompany) {
                throw new ServiceException("发货物流公司已下架");
            }
        }

        List<Long> orderIds = logisticsPrintDeliverDto.getOrderIds();
        if (CollectionUtil.isEmpty(orderIds)) {
            throw new ServiceException("订单id不存在");
        }
        Long orderId = logisticsPrintDeliverDto.getOrderId();
        if(orderId==null || orderId.equals("")){
            throw new ServiceException("订单id不能为空");
        }

        Map<Long,List <ProductStock>> productStockMap=new HashMap();
        //订单总数量
        Map<Long,Integer> productStockNumberMap=new HashMap();


        Map orderItemMap=new HashMap();
        List<Long> skuIds=new ArrayList<>();
        //查询库存
        for (OrderItemDeliveryDto orderItemDeliveryDto : orderItemDeliveryList) {
            Long id = orderItemDeliveryDto.getId();
            if(id == null || id.equals("")){
                throw new ServiceException("订单明细id不能为空！");
            }
            OrderItem orderItem = remoteOrderService.getOrderItemById(id);
            if(orderItem == null || orderItem.equals("")){
                throw new ServiceException("订单明细不存在！");
            }
            Integer deliveryQuantity = orderItemDeliveryDto.getDeliveryQuantity();
            if(deliveryQuantity == null || deliveryQuantity.equals("") || deliveryQuantity == 0){
                throw new ServiceException("订单明细发货数量必须大于0！");
            }
            Integer unDeliveryQuantity = orderItem.getUnDeliveryQuantity();
            String productName = orderItem.getProductName();
            if(deliveryQuantity>unDeliveryQuantity){
                throw new ServiceException(productName+"商品发货数量已大于待发货数量！");
            }
            skuIds.add(orderItem.getProductSkuId());
            orderItemMap.put(orderItem.getProductSkuId()+":"+orderItem.getPriceType(),orderItem);
        }
        List<ProductStock> productStockList=remoteGoodsService.productStockBySkuIds(skuIds,logisticsPrintDeliverDto.getWarehouseId());
        if(CollectionUtils.isEmpty(productStockList)){
            throw new ServiceException("库存数量不足，发货失败！");
        }

        List <ProductStock> stockList=new ArrayList<>();
        for(int j=0;j<productStockList.size();j++){
            ProductStock productStock=productStockList.get(j);

            //库存数据
            BigDecimal stock=productStock.getStock();
            Integer quantity=0;
            Integer num = 0;

            if(productStockNumberMap.get(productStock.getSkuId())==null){
                productStockNumberMap.put(productStock.getSkuId(),0);
            }

            for(int k = 0 ;k<PriceTypeEnum.values().length;k++){
                OrderItem orderItem=(OrderItem)orderItemMap.get(productStock.getSkuId()+":"+(k+1));
                //订单数量
                if(orderItem!=null){
                    quantity=orderItem.getProductQuantity()+quantity;
                    num=productStockNumberMap.get(orderItem.getProductSkuId())+quantity;
                }
            }
            //累计订单数量
            productStockNumberMap.put(productStock.getSkuId(),num);
            //剩余数量
            BigDecimal number =stock.subtract(new BigDecimal(num));
            productStock.setStock(number);
            stockList.add(productStock);
            if(number.compareTo(new BigDecimal("0")) <= 0){
                throw new ServiceException("库存数量不足，发货失败！");
            }

        }
        productStockMap.put(orderId,stockList);
        LogisticsCompany finalLogisticsCompany = logisticsCompany;

        OrderDeliveryDto orderDeliveryDto = new OrderDeliveryDto();
        orderDeliveryDto.setOrderId(orderId);
        orderDeliveryDto.setDeliveryType(deliveryType);
        orderDeliveryDto.setWarehouseId(logisticsPrintDeliverDto.getWarehouseId());
        if(deliveryType == DeliveryTypeEnum.LOGISTICS.getType()){
            orderDeliveryDto.setDeliveryCompany(finalLogisticsCompany.getName());
            orderDeliveryDto.setDeliveryCode(finalLogisticsCompany.getCode());
            String resultList = getExpressInfoDto(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), orderId,new ArrayList<>());
            Map<String, String> resultMap = (Map<String, String>) JSON.parse(resultList);
            if (MapUtil.isEmpty(resultMap)) {
                throw new ServiceException("快递单号生成失败！");
            }
            orderDeliveryDto.setDeliverySn(resultMap.get("waybill_no"));
            orderDeliveryDto.setDeliverySnShow(resultMap.get("waybill_no"));
            orderDeliveryDto.setSortingCode(resultMap.get("sorting_code"));
            orderDeliveryDto.setPackageName(resultMap.get("package_name"));
            orderDeliveryDto.setPackageCode(resultMap.get("package_code"));
            RoutingInfoVo routingInfoVo = new RoutingInfoVo();
            routingInfoVo.setWaybillCode(resultMap.get("waybill_no"));
            routingInfoVo.setSortingCode(resultMap.get("sorting_code"));
            routingInfoVo.setPackageName(resultMap.get("package_name"));
            routingInfoVo.setPackageCode(resultMap.get("package_code"));
            //打印物流单号
            expressPrint(logisticsPrintDeliverDto, finalLogisticsCompany.getCode(), routingInfoVo, orderId,new ArrayList<>());

            //更新打印次数
            remoteOrderService.updateOrderPrintTime(orderId);
        }else if(deliveryType == DeliveryTypeEnum.SELF.getType()){
            LogisticsAddress address = logisticsAddressMapper.selectById(logisticsPrintDeliverDto.getAddressId());
            //自提
            orderDeliveryDto.setSelfName(address.getName());
            orderDeliveryDto.setSelfPhone(address.getPhone());
            orderDeliveryDto.setSelfDetailAddress(address.getAddress());
        }

        orderDeliveryDtos.add(orderDeliveryDto);

        //发货
        int i = remoteOrderService.doLogisticsOrderDelivery(JSON.toJSONString(orderDeliveryDtos));
        if (i < CommonConstants.NUMBER_ONE) {
            throw new ServiceException("发货失败");
        }
        //批量修改库存
        boolean success=remoteGoodsService.batchProductSubtractStock(productStockMap.get(orderId));
        boolean fl=remoteOrderService.updateOrderWarehouse(orderId,logisticsPrintDeliverDto.getWarehouseId());
        if(!success && !fl){
            throw new ServiceException("修改库存失败");
        }
        log.info("更改订单为已发货，结果 ： " + i);
        //小程序发货信息管理
        Boolean wxOrderDelivery = orderSetting.getWxOrderDelivery();
        if(wxOrderDelivery){
            wxOrderDelivery(orderId,deliveryType,new ArrayList<>());
        }
    }

    /**
     * 微信发货
     * @param orderId
     * @param deliveryType
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxOrderDelivery(Long orderId,Integer deliveryType,List<OrderDeliveryItemDto> itemList){
        Order order = remoteOrderService.getOrderById(orderId);
        //微信支付才进行微信发货功能
        if(order.getPayType().equals(PayTypeEnum.WECHAT)){
            //支付流水号
            String transactionId = order.getTransactionId();
            //支付信息
            PaymentWechatDto paymentWechatDto = remotePaymentService.getPayment(transactionId);
            if(paymentWechatDto==null||"".equals(paymentWechatDto)){
                throw new ServiceException("支付信息不存在！");
            }
            if(paymentWechatDto.getTradeStatus()!=null&&!paymentWechatDto.getTradeStatus().equals(StatusConstant.TradeStatus.TRADE_SUCCESS)){
                throw new ServiceException("订单未完成支付！");
            }
            //小程序支付配置
            ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
            if(shopConfig==null||"".equals(shopConfig)){
                throw new ServiceException("小程序支付配置不存在！");
            }
            //小程序配置信息
            MiniInfoVo shopConfigMini = remoteMiniInfoService.getShopConfigMini();
            if(shopConfigMini==null||"".equals(shopConfigMini)){
                throw new ServiceException("小程序配置信息不存在！");
            }
            //小程序授权令牌
            String appId = shopConfigMini.getAppId();
            String appSecret = shopConfigMini.getAppSecret();
            String result = HttpUtil.post(accessTokenUrl+"?grant_type=client_credential&appid="+appId+"&secret="+appSecret, "");
            JSONObject jsonObject = JSON.parseObject(result);
            String accessToken = jsonObject.getString("access_token");
            if(StringUtil.isEmpty(accessToken)){
                throw new ServiceException("获取小程序令牌失败！");
            }
            //发货商品信息
            String itemDesc = "";
            //List<OrderItem> orderItemList =remoteOrderService.orderItemByOrderIds(orderId);
            if(itemList!=null&&itemList.size()>0){
                for (OrderDeliveryItemDto orderDeliveryItemDto : itemList) {
                    if(itemDesc.length()>0){
                        itemDesc+="，";
                    }
                    String productName = orderDeliveryItemDto.getProductName();
                    Integer productQuantity = orderDeliveryItemDto.getProductQuantity();
                    itemDesc+=productName+"*"+productQuantity;
                }
            }
            //微信小程序-发货信息录入
            OrderKeyDto orderKeyDto = new OrderKeyDto();
            orderKeyDto.setOrder_number_type(OrderNumberType.MCHNO.getType());
            orderKeyDto.setMchid(shopConfig.getPayInfo().getMchId());
            orderKeyDto.setOut_trade_no(transactionId);
            orderKeyDto.setTransaction_id("");
            //微信小程序-订单信息录入
            List<ShippingDto>list = new ArrayList<>();
            ShippingDto shipping = new ShippingDto();
            shipping.setItem_desc(itemDesc);
            //微信小程序-联系信息录入
            ContactDto contact = new ContactDto();
            //微信小程序-小程序关联用户录入
            PayerDto payerDto = new PayerDto();
            payerDto.setOpenid(paymentWechatDto.getOpenId());


            //微信小程序-发货信息录入
            WxDeliverGoodsDto wxDeliverGoodsDto = new WxDeliverGoodsDto();

            wxDeliverGoodsDto.setOrder_key(orderKeyDto);
            wxDeliverGoodsDto.setIs_all_delivered(false);
            wxDeliverGoodsDto.setDelivery_mode(DeliveryModeEnum.UNIFIED_DELIVERY.getType());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            wxDeliverGoodsDto.setUpload_time(sdf.format(new Date()));
            wxDeliverGoodsDto.setPayer(payerDto);
            OrderDelivery orderDelivery = remoteOrderService.getOrderDelivery(String.valueOf(orderId));
            if(orderDelivery!=null){
                contact.setReceiver_contact(orderDelivery.getReceiverPhone());
            }
            shipping.setContact(contact);
            if(deliveryType==DeliveryTypeEnum.SELF.getType()) {//自提
                shipping.setTracking_no("");
                shipping.setExpress_company("");
                shipping.setContact(contact);
                list.add(shipping);
                wxDeliverGoodsDto.setLogistics_type(LogisticsTypeEnum.SELF_PICK_UP.getType());
                wxDeliverGoodsDto.setShipping_list(list);
            }else if(deliveryType==DeliveryTypeEnum.TOHOME.getType()){//送货上门
                shipping.setTracking_no("");
                shipping.setExpress_company("");
                list.add(shipping);
                wxDeliverGoodsDto.setLogistics_type(LogisticsTypeEnum.SAME_CITY.getType());
                wxDeliverGoodsDto.setShipping_list(list);
            }else{//物流配送,手动发货
                shipping.setTracking_no(orderDelivery.getDeliverySn());
                shipping.setExpress_company("");
                if(orderDelivery.getDeliveryCode().equals("sf")){
                    shipping.setExpress_company("SF");
                }
                if(orderDelivery.getDeliveryCode().equals("postx")){
                    shipping.setExpress_company("EMS");
                }
                if(orderDelivery.getDeliveryCode().equals("sto")){
                    shipping.setExpress_company("STO");
                }
                list.add(shipping);
                wxDeliverGoodsDto.setLogistics_type(LogisticsTypeEnum.EXPRESS.getType());
                wxDeliverGoodsDto.setShipping_list(list);
            }
            String param = JSONObject.toJSONString(wxDeliverGoodsDto);

            String msg = String.format("微信小程序-发货信息录入接口参数：%s", param);
            log.info(msg);

            String deliverGoodsResult = HttpUtil.post(deliverGoodsUrl+"?access_token="+accessToken, param);
            JSONObject deliverGoodsObject= JSON.parseObject(deliverGoodsResult);
            int errcode = deliverGoodsObject.getInteger("errcode");
            if (errcode != 0) {
                String errmsg = deliverGoodsObject.getString("errmsg");
                String err = String.format("微信小程序-发货信息录入接口异常，code码：%s, msg：%s", errcode, errmsg);
                log.error(err);
                throw new ServiceException("微信小程序-发货信息录入接口异常：" + errmsg);
            }else{
                remoteOrderService.updateWxDeliverStatus(String.valueOf(orderId), WxDeliverStatusEnum.DELIVER);
            }
        }
    }




    @Override
    public String getWxDeliverStatus(WxDeliverGoodsStatusDto wxDeliverGoodsStatusDto) {
        //小程序配置信息
        MiniInfoVo shopConfigMini = remoteMiniInfoService.getShopConfigMini();
        if(shopConfigMini==null||"".equals(shopConfigMini)){
            throw new ServiceException("小程序配置信息不存在！");
        }
        //小程序授权令牌
        String appId = shopConfigMini.getAppId();
        String appSecret = shopConfigMini.getAppSecret();
        String result = HttpUtil.post(accessTokenUrl+"?grant_type=client_credential&appid="+appId+"&secret="+appSecret, "");
        JSONObject jsonObject = JSON.parseObject(result);
        String accessToken = jsonObject.getString("access_token");
        if(StringUtil.isEmpty(accessToken)){
            throw new ServiceException("获取小程序令牌失败！");
        }

        String param = JSONObject.toJSONString(wxDeliverGoodsStatusDto);
        String deliverGoodsStatusResult = HttpUtil.post(deliverGoodsStatusUrl+"?access_token="+accessToken, param);
        JSONObject deliverGoodsStatusObject= JSON.parseObject(deliverGoodsStatusResult);
        int errcode = deliverGoodsStatusObject.getInteger("errcode");
        if (errcode != 0) {
            String errmsg = deliverGoodsStatusObject.getString("errmsg");
            String err = String.format("微信小程序-获取订单发货状态接口异常，code码：%s, msg：%s", errcode, errmsg);
            log.error(err);
            return "获取数据失败";
        }else{
            JSONObject orderObject = deliverGoodsStatusObject.getJSONObject("order");
            if(orderObject!=null&&!"".equals(orderObject)){
                return orderObject.getString("order_state");
            }else{
                return "获取数据失败";
            }

        }
    }

    @Override
    public void UpdateWxDeliverStatus() {
        List<Order>orderList =  remoteOrderService.getOrderByWxDeliverStatus();
        if(orderList!=null&&orderList.size()>0){

            for (Order order : orderList) {
                //小程序支付配置
                TenantContextHolder.setTenantId(order.getTenantId());
                ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
                if(shopConfig==null||"".equals(shopConfig)){
                    throw new ServiceException("小程序支付配置不存在！");
                }
                WxDeliverGoodsStatusDto wxDeliverGoodsStatusDto = new WxDeliverGoodsStatusDto();
                wxDeliverGoodsStatusDto.setMerchant_id(shopConfig.getPayInfo().getMchId());
                wxDeliverGoodsStatusDto.setMerchant_trade_no(order.getTransactionId());
                String wxDeliverStatus = getWxDeliverStatus(wxDeliverGoodsStatusDto);
                //订单状态为待取货才字段签收
                if(wxDeliverStatus!=null&&order.getStatus().equals(OrderStatusEnum.WAIT_FOR_PICKUP)&&(wxDeliverStatus.equals("3")||wxDeliverStatus.equals("4"))){
                    remoteOrderService.receiptOrder(order.getId());
                }

            }
        }
    }



    /**
     * 生成快递单号
     *
     * @param logisticsPrintDeliverDto
     * @param code
     * @param orderId
     */
    private String getExpressInfoDto(LogisticsPrintDeliverDto logisticsPrintDeliverDto, String code, Long orderId,List<OrderDeliveryItemDto>orderDeliveryItemList) {
        ExpressInfoDto expressInfoDto;
        expressInfoDto = convertDelivery(logisticsPrintDeliverDto,code, orderId,orderDeliveryItemList);
        Result logisticsExpressNumber = getLogisticsExpressNumber(expressInfoDto, logisticsPrintDeliverDto.getExpressId());
        if (logisticsExpressNumber.getCode() == CommonConstants.SUCCESS) {
            return (String) logisticsExpressNumber.getData();
        } else {
            //{"code":300115,"msg":"业务参数有误，请检查","data":{"reason":"请确认账户合法性"}}
            if (logisticsExpressNumber.getMsg().equals("业务参数有误，请检查")) {
                throw new ServiceException("请检查快递公司是否配置正常");
            }
            throw new ServiceException(logisticsExpressNumber.getMsg(), SystemCode.FAILURE.getCode());
        }

    }

    /**
     * 根据快递公司代码生成快递单号
     * 顺丰快宝接口暂不支持，调用自己对接丰桥的接口
     *
     * @param expressInfoDto 订单传输信息
     * @param expressId      物流设置表id
     */
    @Override
    public Result getLogisticsExpressNumber(ExpressInfoDto expressInfoDto, Long expressId) {
        log.info("getLogisticsExpressNumber {} ", JSON.toJSONString(expressInfoDto), expressId);
        LogisticsExpressAddressVo logisticsExpressAddressVo = this.logisticsExpressAddressMapper.queryByExpressId(expressId);
        if (BeanUtil.isEmpty(logisticsExpressAddressVo)) {
            return Result.failed(String.valueOf("暂无此快递公司发货信息！"));
        }
        try {
            String data;
            if (KuaiDiHelp.SFCODE.equals(expressInfoDto.getShipperType())) {
                log.info("sfparam {},{}", JSON.toJSONString(expressInfoDto), JSON.toJSONString(logisticsExpressAddressVo));
                cn.hutool.json.JSONObject sfNo = SFExpressUtil.getSFNo(expressInfoDto, logisticsExpressAddressVo);
                cn.hutool.json.JSONObject response = sfNo.getJSONObject("Response");
                String head = (String) response.get("Head");
                log.info("sfres {}", head);
                if ("OK".equals(head)) {
                    Map<String, String> resultMap = new HashMap(CommonConstants.NUMBER_FOUR);
                    cn.hutool.json.JSONObject jsonResult = response.getJSONObject("Body").getJSONObject("OrderResponse");
                    resultMap.put("waybill_no", String.valueOf(jsonResult.get("mailno")));
                    resultMap.put("sorting_code", String.valueOf(jsonResult.getJSONObject("rls_info").getJSONObject("rls_detail").get("twoDimensionCode")));
                    resultMap.put("package_name", String.valueOf(jsonResult.get("origincode")));
                    resultMap.put("package_code", String.valueOf(jsonResult.get("dest_code")));
                    data = JSON.toJSONString(resultMap);
                    return Result.ok(data);
                } else {
                    cn.hutool.json.JSONObject error = response.getJSONObject("ERROR");
                    return Result.failed(String.valueOf(error.get("content")));
                }
            } else {
                OrderSetting orderSetting = remoteOrderService.getOrderSetting();
                String kdAppId = orderSetting.getKdAppId();
                String kdAppKey = orderSetting.getKdAppKey();
                log.info("financeOrderSetting kdAppId {} kdAppKey{}", kdAppId, kdAppKey);
                data = KuaiDiHelp.getExpressNo(expressInfoDto, logisticsExpressAddressVo, kdAppId, kdAppKey);
                log.info("KuaiDiHelp data {} ", data);
                JSONObject jsonObject = JSON.parseObject(data);
                if (String.valueOf(jsonObject.get("code")).equals(CommonConstants.NUMBER_ZERO.toString())) {
                    Map<String, String> resultMap = new HashMap(CommonConstants.NUMBER_FOUR);
                    JSONObject jsonData = jsonObject.getJSONObject("data");
                    for (Map.Entry<String, Object> entry : jsonData.entrySet()) {
                        String key = entry.getKey();
                        JSONObject jsonResult = jsonData.getJSONObject(key);
                        if("failure".equalsIgnoreCase(jsonResult.getString("status"))){
                            return Result.failed(jsonResult.getString("message"));
                        }
                        resultMap.put("waybill_no", String.valueOf(jsonResult.get("waybill_code")));
                        resultMap.put("sorting_code", String.valueOf(jsonResult.get("sorting_code")));
                        resultMap.put("package_name", String.valueOf(jsonResult.get("package_name")));
                        resultMap.put("package_code", String.valueOf(jsonResult.get("package_code")));
                    }
                    data = JSON.toJSONString(resultMap);
                    return Result.ok(data);
                } else {
                    return Result.failed(String.valueOf(jsonObject.get("msg")));
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.failed("快递单号生成失败！");
        }
    }




    private ExpressInfoDto convertDelivery(LogisticsPrintDeliverDto logisticsPrintDeliverDto,String code, Long orderId,List<OrderDeliveryItemDto>orderDeliveryItemList) {
        ExpressInfoDto expressInfoDto = new ExpressInfoDto();
        expressInfoDto.setOrderId(String.valueOf(orderId));
        expressInfoDto.setShipperType(code);
        expressInfoDto.setTemplateId("");
        LambdaQueryWrapper<LogisticsExpress>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsExpress::getCode,code);
        List<LogisticsExpress> logisticsExpressList = logisticsExpressMapper.selectList(wrapper);
        if(logisticsExpressList!=null&&logisticsExpressList.size()>0){
            LogisticsExpress logisticsExpress = logisticsExpressList.get(0);
            if(StringUtil.isNotEmpty(logisticsExpress.getTemplateId())){
                expressInfoDto.setTemplateId(logisticsExpress.getTemplateId());
            }
        }
        OrderVo orderVo = remoteOrderService.orderInfo(orderId);
        log.info(" remoteOrderService.orderInfo {}", JSON.toJSONString(orderVo));


        //OrderDelivery orderDelivery = orderVo.getOrderDelivery();
        expressInfoDto.setTime(orderVo.getPrintTime());
        expressInfoDto.setName(logisticsPrintDeliverDto.getReceiverName());
        expressInfoDto.setMobile(logisticsPrintDeliverDto.getReceiverPhone());
        expressInfoDto.setProvince(logisticsPrintDeliverDto.getReceiverProvince());
        expressInfoDto.setCity(logisticsPrintDeliverDto.getReceiverCity());
        expressInfoDto.setDistrict(logisticsPrintDeliverDto.getReceiverRegion());
        expressInfoDto.setAddress(logisticsPrintDeliverDto.getReceiverDetailAddress());

        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
        expressInfoDto.setTradeName(orderDeliveryItemList.get(0).getProductName());
        expressInfoDto.setTradeNumber(orderDeliveryItemList.get(0).getProductQuantity());
        List<String> itemNames = new ArrayList<>(orderItemList.size());
        orderItemList.forEach(orderItem -> {
            itemNames.add(orderItem.getProductName());
        });
        return expressInfoDto;
    }

    /**
     * 打印运单
     *
     * @param logisticsPrintDeliverDto
     * @param code
     * @param routingInfoVo
     * @param orderId
     */
    private void expressPrint(LogisticsPrintDeliverDto logisticsPrintDeliverDto, String code, RoutingInfoVo routingInfoVo,
                              Long orderId,List<OrderDeliveryItemDto>orderDeliveryItemList) {
        ExpressInfoDto expressInfoDto;
        //获取发货地址信息以及对应物流公司信息
        LogisticsExpressAddressVo logisticsExpressAddressVo = this.logisticsExpressAddressMapper
                .queryByExpressId(logisticsPrintDeliverDto.getExpressId());
        if (null == logisticsExpressAddressVo) {
            throw new ServiceException("暂无此快递公司发货信息！", SystemCode.DATA_ADD_FAILED.getCode());
        }
        expressInfoDto = convertDelivery(logisticsPrintDeliverDto,code, orderId,orderDeliveryItemList);

        //获取快宝的客户id与密匙
        OrderSetting orderSetting = remoteOrderService.getOrderSetting();
        String kdAppId = orderSetting.getKdAppId();
        String kdAppKey = orderSetting.getKdAppKey();
        log.info("expressInfoDto {} ", JSON.toJSONString(expressInfoDto));
        log.info("financeOrderSetting {} ", JSON.toJSONString(orderSetting));
        log.info("logisticsExpressAddressVo {} ", JSON.toJSONString(logisticsExpressAddressVo));
        log.info("routingInfoVo {} ", routingInfoVo);
        log.info("printCode {} ", logisticsPrintDeliverDto.getPrintCode());
        String expressPrint = KuaiDiHelp
                .getExpressPrint(expressInfoDto, logisticsExpressAddressVo, kdAppId, kdAppKey, routingInfoVo,
                        logisticsPrintDeliverDto.getPrintCode());
        JSONObject jsonObject = JSON.parseObject(expressPrint);
        if (jsonObject.isEmpty() || !CommonConstants.STATUS_NORMAL.equals(String.valueOf(jsonObject.get("code")))) {
            throw new ServiceException(String.valueOf(jsonObject.get("msg")), SystemCode.DATA_ADD_FAILED.getCode());
        }
    }

    /**
     * 批量发货
     *
     * @param logisticsBatchDeliverDtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doBatchDeliver(List<LogisticsBatchDeliverDto> logisticsBatchDeliverDtos) {



        if (null == logisticsBatchDeliverDtos || CommonConstants.NUMBER_ZERO == logisticsBatchDeliverDtos.size()) {
            throw new ServiceException("未获取到请求参数");
        }
        //订单配置
        OrderSetting orderSetting = remoteOrderService.getOrderSetting();
        Boolean wxOrderDelivery = orderSetting.getWxOrderDelivery();

        List<OrderDeliveryDto> list = new ArrayList<>();

        if(logisticsBatchDeliverDtos!=null&&logisticsBatchDeliverDtos.size()>0){
            List<LogisticsBatchDeliverDto> dataList = logisticsBatchDeliverDtos.stream().filter(e -> e.getOrderDeLiveryProxyId() != null
                    && !e.getOrderDeLiveryProxyId().equals("")).collect(Collectors.toList());

            if(dataList!=null&&dataList.size()>0){
                List<OrderItemDeliveryDto> orderItemDeliveryList = new ArrayList<>();
                for (LogisticsBatchDeliverDto logisticsBatchDeliverDto : dataList) {
                    orderItemDeliveryList.addAll(logisticsBatchDeliverDto.getOrderItemDeliveryList());
                }
                if(orderItemDeliveryList!=null&&orderItemDeliveryList.size()>0){
                    Map<Long, List<OrderItemDeliveryDto>> detailList = orderItemDeliveryList.stream().collect(Collectors.groupingBy(OrderItemDeliveryDto::getOrderDeliveryProxyItemId));
                    for (Map.Entry<Long, List<OrderItemDeliveryDto>> entry : detailList.entrySet()) {
                        OrderDeliveryProxyItem orderDeliveryProxyItem = remoteOrderService.getOrderDeliveryProxyItem(entry.getKey());
                        Integer unDeliveryQuantity = orderDeliveryProxyItem.getUnDeliveryQuantity();
                        Integer deliveryQuantity = 0;
                        for (OrderItemDeliveryDto orderItemDeliveryDto : entry.getValue()) {
                            deliveryQuantity = deliveryQuantity + orderItemDeliveryDto.getDeliveryQuantity();
                        }
                        if(deliveryQuantity>unDeliveryQuantity){
                            throw new ServiceException(orderDeliveryProxyItem.getProductName()+"发货数量大于待发货数量");
                        }
                    }
                }
            }
        }


        for (LogisticsBatchDeliverDto logisticsBatchDeliverDto : logisticsBatchDeliverDtos) {
            String oldShopId = ShopContextHolder.getShopId();
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);

            List<OrderItemDeliveryDto> orderItemDeliveryList = logisticsBatchDeliverDto.getOrderItemDeliveryList();

            if(orderItemDeliveryList==null||orderItemDeliveryList.size()==0){
                throw new ServiceException("订单发货详情不能为空");
            }


            Long orderId = logisticsBatchDeliverDto.getOrderId();

            Long warehouseId = logisticsBatchDeliverDto.getWarehouseId();
            List<ProductStock> stockList = new ArrayList<>();


            Map orderItemMap=new HashMap();
            List<Long> skuIds=new ArrayList<>();
            List<Long> linkSkuIds=new ArrayList<>();
            List<OrderItem>orderItemList = new ArrayList<>();
            List<OrderDeliveryItemDto>orderDeliveryItemList = new ArrayList<>();

            for (OrderItemDeliveryDto orderItemDeliveryDto : orderItemDeliveryList) {
                Long id = orderItemDeliveryDto.getId();
                if(id == null || id.equals("")){
                    throw new ServiceException("订单明细id不能为空！");
                }
                OrderItem orderItem = remoteOrderService.getOrderItemById(id);
                if(orderItem == null || orderItem.equals("")){
                    throw new ServiceException("订单明细不存在！");
                }
                Integer deliveryQuantity = orderItemDeliveryDto.getDeliveryQuantity();
                if(deliveryQuantity == null || deliveryQuantity.equals("") || deliveryQuantity == 0){
                    throw new ServiceException("订单明细发货数量必须大于0！");
                }
                Integer unDeliveryQuantity = orderItem.getUnDeliveryQuantity();
                String productName = orderItem.getProductName();
                if(deliveryQuantity>unDeliveryQuantity){
                    throw new ServiceException(productName+"商品发货数量已大于待发货数量！");
                }

                skuIds.add(orderItem.getProductSkuId());
                if(orderItem.getLinkSkuId()!=null){
                    linkSkuIds.add(orderItem.getLinkSkuId());
                }
                orderItemList.add(orderItem);

                OrderDeliveryItemDto orderDeliveryItemDto = new OrderDeliveryItemDto();
                orderDeliveryItemDto.setOrderItemId(orderItem.getId());
                orderDeliveryItemDto.setProductId(orderItem.getProductId());
                orderDeliveryItemDto.setProductName(orderItem.getProductName());
                orderDeliveryItemDto.setProductPic(orderItem.getProductPic());
                orderDeliveryItemDto.setProductQuantity(orderItemDeliveryDto.getDeliveryQuantity());
                orderDeliveryItemDto.setProductSkuCode(orderItem.getProductSkuCode());
                orderDeliveryItemDto.setProductSkuId(orderItem.getProductSkuId());
                orderDeliveryItemDto.setOrderDeliveryProxyItemId(orderItemDeliveryDto.getOrderDeliveryProxyItemId());
                orderDeliveryItemDto.setOrderDeLiveryProxyId(orderItemDeliveryDto.getOrderDeLiveryProxyId());
                orderItemMap.put(orderItem.getProductSkuId()+":"+orderItem.getPriceType(),orderDeliveryItemDto);

                orderDeliveryItemList.add(orderDeliveryItemDto);
            }

            List<ProductStock> productStockList = remoteGoodsService.productStockBySkuIds(skuIds, warehouseId);
            if (CollectionUtils.isEmpty(productStockList)) {
                throw new ServiceException("库存数量不足，发货失败！");
            }

            for (int j = 0; j < productStockList.size(); j++) {
                ProductStock productStock = productStockList.get(j);
                //库存数据
                BigDecimal stock = productStock.getStock();
                //订单数量
                Integer quantity = 0;
                for(int i = 0 ;i<PriceTypeEnum.values().length;i++) {
                    OrderDeliveryItemDto orderDeliveryItemDto = (OrderDeliveryItemDto) orderItemMap.get(productStock.getSkuId() + ":" + (i + 1));
                    //订单数量
                    if(orderDeliveryItemDto!=null){
                        quantity = orderDeliveryItemDto.getProductQuantity();
                    }
                }
                //剩余数量
                BigDecimal number = stock.subtract(new BigDecimal(quantity));
                productStock.setStock(number);
                stockList.add(productStock);
                if (number.compareTo(new BigDecimal("0")) <= 0) {
                    throw new ServiceException("库存数量不足，发货失败！");
                }
            }

            boolean success=remoteGoodsService.batchProductSubtractStock(stockList);
            boolean fl=remoteOrderService.updateOrderWarehouse(orderId,warehouseId);
            if(!success&&!fl){
                throw new ServiceException("修改库存失败");
            }
            Map<String, Object> param = new HashMap<>(CommonConstants.NUMBER_THREE);
            param.put("code", logisticsBatchDeliverDto.getDeliverCode());
            LogisticsCompany logisticsCompany = logisticsCompanyMapper.selectListCompanyByParam(param);
            OrderDeliveryDto orderDeliveryDto = new OrderDeliveryDto();
            orderDeliveryDto.setOrderDeLiveryProxyId(logisticsBatchDeliverDto.getOrderDeLiveryProxyId());
            orderDeliveryDto.setOrderId(logisticsBatchDeliverDto.getOrderId());
            orderDeliveryDto.setDeliveryCompany(logisticsCompany.getName());
            orderDeliveryDto.setDeliveryCode(logisticsBatchDeliverDto.getDeliverCode());
            orderDeliveryDto.setDeliverySn(logisticsBatchDeliverDto.getLogisticsCode());
            orderDeliveryDto.setOrderId(logisticsBatchDeliverDto.getOrderId());
            orderDeliveryDto.setDeliveryType(logisticsBatchDeliverDto.getDeliveryType());
            orderDeliveryDto.setDeliverySnShow(logisticsBatchDeliverDto.getLogisticsCode());

            orderDeliveryDto.setReceiverName(logisticsBatchDeliverDto.getReceiverName());
            orderDeliveryDto.setReceiverPhone(logisticsBatchDeliverDto.getReceiverPhone());
            orderDeliveryDto.setReceiverPostCode(logisticsBatchDeliverDto.getReceiverPostCode());
            orderDeliveryDto.setReceiverProvince(logisticsBatchDeliverDto.getReceiverProvince());
            orderDeliveryDto.setReceiverCity(logisticsBatchDeliverDto.getReceiverCity());
            orderDeliveryDto.setReceiverRegion(logisticsBatchDeliverDto.getReceiverRegion());
            orderDeliveryDto.setReceiverDetailAddress(logisticsBatchDeliverDto.getReceiverDetailAddress());

            orderDeliveryDto.setTenantId(TenantContextHolder.getTenantId());
            orderDeliveryDto.setShopId(oldShopId);
            orderDeliveryDto.setItemList(orderDeliveryItemList);
            orderDeliveryDto.setWarehouseId(logisticsBatchDeliverDto.getWarehouseId());
            list.add(orderDeliveryDto);
            //小程序发货信息管理
            if(wxOrderDelivery){
                wxOrderDelivery(logisticsBatchDeliverDto.getOrderId(),DeliveryTypeEnum.EXPRESS.getType(),orderDeliveryItemList);
            }
            ShopContextHolder.setShopId(oldShopId);
        }
        //发货
        int i = remoteOrderService.doLogisticsOrderDelivery(JSON.toJSONString(list));
        if (i < CommonConstants.NUMBER_ONE) {
            throw new ServiceException("发货失败");
        }
    }
}
