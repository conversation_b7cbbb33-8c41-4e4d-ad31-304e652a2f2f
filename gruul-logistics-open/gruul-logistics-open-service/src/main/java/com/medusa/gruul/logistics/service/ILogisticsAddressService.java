package com.medusa.gruul.logistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.logistics.model.dto.manager.*;
import com.medusa.gruul.logistics.model.dto.manager.express.ExpressInfoDto;
import com.medusa.gruul.logistics.model.param.LogisticsAddressParam;
import com.medusa.gruul.logistics.model.vo.LogisticsAddressVo;
import com.medusa.gruul.order.api.model.OrderDeliveryItemDto;

import java.util.List;
import java.util.Map;

/**
 * 收发货地址管理
 * <AUTHOR>
 */
public interface ILogisticsAddressService {
    /**
     * Description: getAddressList
     * <p>Version: </p>
     * <p>Author: Mr.zhaozheng </p>
     * <p>Date: 2020-02-11 16:10 </p>
     * @param logisticsAddressParam
     * @return com.medusa.gruul.common.core.util.ResultList
     */
    IPage<LogisticsAddressVo> getAddressList(LogisticsAddressParam logisticsAddressParam);

    /**
     * 查看所有地址
     * @return List<LogisticsAddressVo>
     */
    List<LogisticsAddressVo> getAllAddressList();

    /**
     * 更新/新增 地址
     * @param logisticsAddressDto
     */
    void setAddress(LogisticsAddressDto logisticsAddressDto);

    /**
     * 设置 默认地址
     * @param type 1-发货地址 2-收货地址
     * @param id
     */
    void setDefAddress(Integer type, Long id);


    /**
     * 删除地址
     * @param id 主键
     */
    void delAddress(Long id);

    /**
     * 获取默认收/发货地址
     *
     * @param type 收发货类型 1-发货地址 2-收货地址
     * @return LogisticsAddressVo
     */
    LogisticsAddressVo getDefaultAddress(Integer type);

    /**
     * 获取发货所需
     * @return
     */
    Map<String, Object> listLogisticsCompany(Long orderId);

    /**
     * 设置默认快递公司
     * @param logisticsCompanyId
     * @author: wangpeng
     * @version : v1.0
     * @date: 2020/3/13 8:35 下午
     */
    void setCompanyDefault(Long logisticsCompanyId);

    /***
    * 打印并发货
     * @param logisticsPrintDeliverDto
    * @author: wangpeng
    * @version : v1.0
    * @date: 2020/3/13 9:46 下午
    */
    void doPrintDeliverGoods(LogisticsPrintDeliverDto logisticsPrintDeliverDto);

    /**
     * 批量打印并发货
     * @param logisticsPrintDeliverBatchDtos
     */
    void doBatchPrintDeliverGoods(List<LogisticsPrintDeliverBatchDto> logisticsPrintDeliverBatchDtos);

    /** 
    * 批量发货
    * @param logisticsBatchDeliverDtos
     * @author: wangpeng
    * @version : v1.0      
    * @date: 2020/3/14 4:47 下午
    */ 
    void doBatchDeliver(List<LogisticsBatchDeliverDto> logisticsBatchDeliverDtos);

    /**
     * 生成快递公司快递单号
     * @param expressInfoDto 订单传输信息
     * @param addressId 发货地址id
     * @return Result
     */
    Result getLogisticsExpressNumber(ExpressInfoDto expressInfoDto, Long addressId);




    /**
     * 获取微信订单收货状态
     * @param wxDeliverGoodsStatusDto
     * @return
     */
    String getWxDeliverStatus(WxDeliverGoodsStatusDto wxDeliverGoodsStatusDto);

    /**
         * 查看微信发货是否确认收货，确认则同步程序订单完成确认收货
     */
    void UpdateWxDeliverStatus();

    /**
     *  打印快递面单
     * @param orderId
     * @param printCode
     */

    void doDeliverGoodsAgain(Long orderId,String printCode);

    /**
     * 微信发货管理
     * @param orderId
     * @param deliveryType
     */
    void wxOrderDelivery(Long orderId,Integer deliveryType,List<OrderDeliveryItemDto> itemList);


}
