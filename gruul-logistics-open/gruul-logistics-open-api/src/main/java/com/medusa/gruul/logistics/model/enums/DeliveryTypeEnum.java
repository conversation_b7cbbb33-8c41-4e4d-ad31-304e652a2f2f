package com.medusa.gruul.logistics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 发货方式枚举类
 */
@Getter
public enum DeliveryTypeEnum {
    EXPRESS(103, "手动发货"),
    LOGISTICS(102, "物流配送"),
    TOHOME(101, "送货上门"),
    SELF(100, "自提");


    @EnumValue
    /**
     * 值
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    DeliveryTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
