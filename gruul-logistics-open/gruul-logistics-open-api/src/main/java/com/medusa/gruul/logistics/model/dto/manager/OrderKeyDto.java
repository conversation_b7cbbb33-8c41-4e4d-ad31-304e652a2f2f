package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: plh
 * @Description: 微信小程序-订单信息录入-入参DTO
 * @Date: Created in 16:58 2024/5/14
 */
@Data
public class OrderKeyDto implements Serializable {

    @ApiModelProperty("必填 订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号")
    @NotNull(message = "order_number_type不能为空")
    private Integer order_number_type;

    @ApiModelProperty("非必填 原支付交易对应的微信订单号")
    private String transaction_id;

    @ApiModelProperty("支付下单商户的商户号，由微信支付生成并下发。")
    private String mchid;

    @ApiModelProperty("商户系统内部订单号，只能是数字、大小写字母`_-*`且在同一个商户号下唯一")
    private String out_trade_no;

}
