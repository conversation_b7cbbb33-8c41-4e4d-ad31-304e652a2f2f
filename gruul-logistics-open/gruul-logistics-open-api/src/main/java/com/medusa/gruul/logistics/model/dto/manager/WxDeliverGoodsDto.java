package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:57 2024/5/14
 */
@Data
@ApiModel("微信小程序-发货信息录入-入参DTO")
public class WxDeliverGoodsDto {

    @ApiModelProperty("必填 订单，需要上传物流信息的订单")
    @NotNull(message = "order_key不能为空")
    private OrderKeyDto order_key;

    @ApiModelProperty("必填 物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提")
    @NotNull(message = "logistics_type不能为空")
    private Integer logistics_type;

    @ApiModelProperty("必填 发货模式，发货模式枚举值：1、UNIFIED_DELIVERY（统一发货）2、SPLIT_DELIVERY（分拆发货） 示例值: UNIFIED_DELIVERY")
    @NotNull(message = "delivery_mode不能为空")
    private Integer delivery_mode;

    @ApiModelProperty("分拆发货模式时必填，用于标识分拆发货模式下是否已全部发货完成，只有全部发货完成的情况下才会向用户推送发货完成通知。示例值: true/false")
    private Boolean is_all_delivered;

    @ApiModelProperty( "必填 物流信息列表，发货物流单列表，支持统一发货（单个物流单）和分拆发货（多个物流单）两种模式，多重性: [1, 10]")
    @NotNull(message = "shipping_list不能为空")
    private List<ShippingDto> shipping_list;

    @ApiModelProperty("必填 上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`")
    @NotBlank(message = "upload_time不能为空")
    private String upload_time;

    @ApiModelProperty("必填 支付者，支付者信息")
    @NotNull(message = "payer不能为空")
    private PayerDto payer;
}
