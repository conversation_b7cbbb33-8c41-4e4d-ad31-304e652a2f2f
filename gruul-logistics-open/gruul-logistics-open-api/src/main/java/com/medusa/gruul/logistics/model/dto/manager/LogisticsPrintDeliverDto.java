package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("打印发货")
public class LogisticsPrintDeliverDto {

    @ApiModelProperty("订单id")
    private List<Long> orderIds;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("物流公司编号")
    //@NotBlank(message = "物流公司不能为空")
    private String deliverCode;

    @ApiModelProperty("打印机编号")
    //@NotBlank(message = "打印机编号不能为空")
    private String printCode;

    @ApiModelProperty("物流公司id")
    //@NotNull(message = "物流公司发货地址不能为空")
    private Long expressId;

    @ApiModelProperty("仓库id")
    @NotNull(message = "仓库不能为空")
    private Long warehouseId;

    @ApiModelProperty("发货方式，102：物流配送，101：送货上门，100：自提")
    @NotNull(message = "发货方式不能为空")
    private int deliveryType;

    @ApiModelProperty("地址id")
    private Long addressId;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    private String receiverPostCode;

    /**
     * 省份/直辖市
     */
    @ApiModelProperty(value = "省份/直辖市")
    private String receiverProvince;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String receiverCity;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String receiverRegion;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String receiverDetailAddress;

    @ApiModelProperty("订单发货详情")
    private List<OrderItemDeliveryDto>orderItemDeliveryList;
}
