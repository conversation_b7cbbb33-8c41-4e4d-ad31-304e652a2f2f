package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:33 2025/5/28
 */
@Data
@ApiModel("订单发货详情")
public class OrderItemDeliveryDto {

    @ApiModelProperty(value = "订单明细id")
    private Long id;

    @ApiModelProperty(value = "发货数量")
    private Integer deliveryQuantity;

    /**
     * 代发货订单ID
     */
    @ApiModelProperty(value = "代发货订单ID")
    private Long orderDeLiveryProxyId;
    /**
     * 代发货订单明细id
     */
    @ApiModelProperty(value = "代发货订单明细id")
    private Long orderDeliveryProxyItemId;

}
