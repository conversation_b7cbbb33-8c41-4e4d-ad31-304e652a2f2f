package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: plh
 * @Description: 微信小程序-物流信息录入-入参DTO
 * @Date: Created in 17:06 2024/5/14
 */

@Data
public class ShippingDto implements Serializable {

    @ApiModelProperty("物流单号，物流快递发货时必填，示例值: 323244567777 字符字节限制: [1, 128]")
    private String tracking_no;

    @ApiModelProperty("物流公司编码，快递公司ID，参见「查询物流公司编码列表」，物流快递发货时必填， 示例值: DHL 字符字节限制: [1, 128]")
    private String express_company;

    @ApiModelProperty("必填 商品信息，例如：微信红包抱枕*1个，限120个字以内")
    @NotBlank(message = "item_desc不能为空")
    private String item_desc;

    @ApiModelProperty("联系方式，当发货的物流公司为顺丰时，联系方式为必填，收件人或寄件人联系方式二选一")
    private ContactDto contact;


}
