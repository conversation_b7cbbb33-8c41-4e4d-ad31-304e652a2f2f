package com.medusa.gruul.logistics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 微信小程序-物流模式枚举
 * @Date: Created in 10:04 2024/5/15
 */
@Getter
public enum LogisticsTypeEnum {

    EXPRESS(1, "快递配送"),
    SAME_CITY(2, "同城配送"),
    FICTITIOUS(3, "虚拟商品"),
    SELF_PICK_UP(4, "自提");
    @EnumValue
    /**
     * 值
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    LogisticsTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
