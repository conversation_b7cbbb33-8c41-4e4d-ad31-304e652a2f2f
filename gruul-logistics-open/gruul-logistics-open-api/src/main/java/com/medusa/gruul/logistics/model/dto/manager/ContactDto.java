package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: 微信小程序-联系信息录入-入参DTO
 * @Date: Created in 17:08 2024/5/14
 */
@Data
public class ContactDto implements Serializable {

    @ApiModelProperty("寄件人联系方式，寄件人联系方式，采用掩码传输，最后4位数字不能打掩码 示例值: `189****1234, 021-****1234, ****1234, 0**2-***1234, 0**2-******23-10, ****123-8008` 值限制: 0 ≤ value ≤ 1024")
    private String consignor_contact;

    @ApiModelProperty("收件人联系方式，收件人联系方式为，采用掩码传输，最后4位数字不能打掩码 示例值: `189****1234, 021-****1234, ****1234, 0**2-***1234, 0**2-******23-10, ****123-8008` 值限制: 0 ≤ value ≤ 1024")
    private String receiver_contact;


}
