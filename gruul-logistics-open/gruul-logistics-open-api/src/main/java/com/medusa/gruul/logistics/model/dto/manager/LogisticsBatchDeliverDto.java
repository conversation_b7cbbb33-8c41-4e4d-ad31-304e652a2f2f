package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("批量发货")
public class LogisticsBatchDeliverDto {

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("物流公司编号")
    private String deliverCode;

    @ApiModelProperty("物流公司名称")
    private String deliverName;

    @ApiModelProperty("物流单号")
    private String logisticsCode;

    @ApiModelProperty("仓库id")
    private Long warehouseId;

    @ApiModelProperty("发货方式，102：物流配送，101：送货上门，100：自提,103：手动发货")
    private int deliveryType;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    private String receiverPostCode;

    /**
     * 省份/直辖市
     */
    @ApiModelProperty(value = "省份/直辖市")
    private String receiverProvince;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String receiverCity;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String receiverRegion;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String receiverDetailAddress;


    @ApiModelProperty("订单发货详情")
    private List<OrderItemDeliveryDto> orderItemDeliveryList;

    /**
     * 代发货订单ID
     */
    @ApiModelProperty(value = "代发货订单ID")
    private Long orderDeLiveryProxyId;

}
