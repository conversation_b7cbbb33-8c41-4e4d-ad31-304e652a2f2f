package com.medusa.gruul.logistics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 微信小程序-发货模式枚举
 * @Date: Created in 9:53 2024/5/15
 */
@Getter
public enum DeliveryModeEnum {

    UNIFIED_DELIVERY(1, "统一发货"),
    SPLIT_DELIVERY(2, "分拆发货");
    @EnumValue
    /**
     * 值
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    DeliveryModeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
